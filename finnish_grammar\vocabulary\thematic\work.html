﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Work and Professions - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="work-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="work-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="work-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Work and Professions</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Work and Professions in Finnish</h2>
            <p>This page covers essential Finnish vocabulary related to work, professions, and the workplace. Whether you're looking for a job in Finland, discussing your career, or talking about your workplace, these terms will help you communicate effectively in professional contexts.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>General Work Vocabulary</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>työ</td>
                    <td class="pronunciation">työ</td>
                    <td>work, job</td>
                </tr>
                <tr>
                    <td>työpaikka</td>
                    <td class="pronunciation">työ-paik-ka</td>
                    <td>workplace</td>
                </tr>
                <tr>
                    <td>työaika</td>
                    <td class="pronunciation">työ-ai-ka</td>
                    <td>working hours</td>
                </tr>
                <tr>
                    <td>työntekijä</td>
                    <td class="pronunciation">työn-te-ki-jä</td>
                    <td>employee</td>
                </tr>
                <tr>
                    <td>työnantaja</td>
                    <td class="pronunciation">työn-an-ta-ja</td>
                    <td>employer</td>
                </tr>
                <tr>
                    <td>työkokemus</td>
                    <td class="pronunciation">työ-ko-ke-mus</td>
                    <td>work experience</td>
                </tr>
                <tr>
                    <td>työhakemus</td>
                    <td class="pronunciation">työ-ha-ke-mus</td>
                    <td>job application</td>
                </tr>
                <tr>
                    <td>ansioluettelo</td>
                    <td class="pronunciation">an-si-o-lu-et-te-lo</td>
                    <td>CV, resume</td>
                </tr>
                <tr>
                    <td>työhaastattelu</td>
                    <td class="pronunciation">työ-haas-tat-te-lu</td>
                    <td>job interview</td>
                </tr>
                <tr>
                    <td>palkka</td>
                    <td class="pronunciation">palk-ka</td>
                    <td>salary, wage</td>
                </tr>
                <tr>
                    <td>kuukausipalkka</td>
                    <td class="pronunciation">kuu-kau-si-palk-ka</td>
                    <td>monthly salary</td>
                </tr>
                <tr>
                    <td>tuntipalkka</td>
                    <td class="pronunciation">tun-ti-palk-ka</td>
                    <td>hourly wage</td>
                </tr>
                <tr>
                    <td>kokopäivätyö</td>
                    <td class="pronunciation">ko-ko-päi-vä-työ</td>
                    <td>full-time job</td>
                </tr>
                <tr>
                    <td>osa-aikatyö</td>
                    <td class="pronunciation">o-sa-ai-ka-työ</td>
                    <td>part-time job</td>
                </tr>
                <tr>
                    <td>määräaikainen työ</td>
                    <td class="pronunciation">mää-rä-ai-kai-nen työ</td>
                    <td>temporary job</td>
                </tr>
                <tr>
                    <td>vakituinen työ</td>
                    <td class="pronunciation">va-ki-tui-nen työ</td>
                    <td>permanent job</td>
                </tr>
                <tr>
                    <td>työsopimus</td>
                    <td class="pronunciation">työ-so-pi-mus</td>
                    <td>employment contract</td>
                </tr>
                <tr>
                    <td>työttömyys</td>
                    <td class="pronunciation">työt-tö-myys</td>
                    <td>unemployment</td>
                </tr>
                <tr>
                    <td>eläke</td>
                    <td class="pronunciation">e-lä-ke</td>
                    <td>pension, retirement</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Etsin uutta työpaikkaa. Lähetin työhakemuksen eilen.</p>
                <p><strong>English:</strong> I'm looking for a new job. I sent a job application yesterday.</p>
                <p><strong>Finnish:</strong> Minulla on työhaastattelu huomenna.</p>
                <p><strong>English:</strong> I have a job interview tomorrow.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Common Professions</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>opettaja</td>
                    <td class="pronunciation">o-pet-ta-ja</td>
                    <td>teacher</td>
                </tr>
                <tr>
                    <td>lääkäri</td>
                    <td class="pronunciation">lää-kä-ri</td>
                    <td>doctor</td>
                </tr>
                <tr>
                    <td>sairaanhoitaja</td>
                    <td class="pronunciation">sai-raan-hoi-ta-ja</td>
                    <td>nurse</td>
                </tr>
                <tr>
                    <td>insinööri</td>
                    <td class="pronunciation">in-si-nöö-ri</td>
                    <td>engineer</td>
                </tr>
                <tr>
                    <td>ohjelmoija</td>
                    <td class="pronunciation">oh-jel-moi-ja</td>
                    <td>programmer</td>
                </tr>
                <tr>
                    <td>myyjä</td>
                    <td class="pronunciation">myy-jä</td>
                    <td>salesperson</td>
                </tr>
                <tr>
                    <td>kokki</td>
                    <td class="pronunciation">kok-ki</td>
                    <td>cook, chef</td>
                </tr>
                <tr>
                    <td>tarjoilija</td>
                    <td class="pronunciation">tar-joi-li-ja</td>
                    <td>waiter/waitress</td>
                </tr>
                <tr>
                    <td>siivooja</td>
                    <td class="pronunciation">sii-voo-ja</td>
                    <td>cleaner</td>
                </tr>
                <tr>
                    <td>kirjanpitäjä</td>
                    <td class="pronunciation">kir-jan-pi-tä-jä</td>
                    <td>accountant</td>
                </tr>
                <tr>
                    <td>lakimies</td>
                    <td class="pronunciation">la-ki-mies</td>
                    <td>lawyer</td>
                </tr>
                <tr>
                    <td>poliisi</td>
                    <td class="pronunciation">po-lii-si</td>
                    <td>police officer</td>
                </tr>
                <tr>
                    <td>palomies</td>
                    <td class="pronunciation">pa-lo-mies</td>
                    <td>firefighter</td>
                </tr>
                <tr>
                    <td>kuljettaja</td>
                    <td class="pronunciation">kul-jet-ta-ja</td>
                    <td>driver</td>
                </tr>
                <tr>
                    <td>toimittaja</td>
                    <td class="pronunciation">toi-mit-ta-ja</td>
                    <td>journalist</td>
                </tr>
                <tr>
                    <td>tutkija</td>
                    <td class="pronunciation">tut-ki-ja</td>
                    <td>researcher</td>
                </tr>
                <tr>
                    <td>johtaja</td>
                    <td class="pronunciation">joh-ta-ja</td>
                    <td>manager, director</td>
                </tr>
                <tr>
                    <td>toimitusjohtaja</td>
                    <td class="pronunciation">toi-mi-tus-joh-ta-ja</td>
                    <td>CEO</td>
                </tr>
                <tr>
                    <td>yrittäjä</td>
                    <td class="pronunciation">y-rit-tä-jä</td>
                    <td>entrepreneur</td>
                </tr>
                <tr>
                    <td>opiskelija</td>
                    <td class="pronunciation">o-pis-ke-li-ja</td>
                    <td>student</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finnish, profession names are the same regardless of gender. For example, "opettaja" can refer to either a male or female teacher.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Workplace Vocabulary</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>toimisto</td>
                    <td class="pronunciation">toi-mis-to</td>
                    <td>office</td>
                </tr>
                <tr>
                    <td>työpöytä</td>
                    <td class="pronunciation">työ-pöy-tä</td>
                    <td>desk</td>
                </tr>
                <tr>
                    <td>työtuoli</td>
                    <td class="pronunciation">työ-tuo-li</td>
                    <td>office chair</td>
                </tr>
                <tr>
                    <td>tietokone</td>
                    <td class="pronunciation">tie-to-ko-ne</td>
                    <td>computer</td>
                </tr>
                <tr>
                    <td>näppäimistö</td>
                    <td class="pronunciation">näp-päi-mis-tö</td>
                    <td>keyboard</td>
                </tr>
                <tr>
                    <td>hiiri</td>
                    <td class="pronunciation">hii-ri</td>
                    <td>mouse</td>
                </tr>
                <tr>
                    <td>tulostin</td>
                    <td class="pronunciation">tu-los-tin</td>
                    <td>printer</td>
                </tr>
                <tr>
                    <td>kokoushuone</td>
                    <td class="pronunciation">ko-kous-huo-ne</td>
                    <td>meeting room</td>
                </tr>
                <tr>
                    <td>kokous</td>
                    <td class="pronunciation">ko-kous</td>
                    <td>meeting</td>
                </tr>
                <tr>
                    <td>esitys</td>
                    <td class="pronunciation">e-si-tys</td>
                    <td>presentation</td>
                </tr>
                <tr>
                    <td>projekti</td>
                    <td class="pronunciation">pro-jek-ti</td>
                    <td>project</td>
                </tr>
                <tr>
                    <td>asiakas</td>
                    <td class="pronunciation">a-si-a-kas</td>
                    <td>client, customer</td>
                </tr>
                <tr>
                    <td>kollega</td>
                    <td class="pronunciation">kol-le-ga</td>
                    <td>colleague</td>
                </tr>
                <tr>
                    <td>tiimi</td>
                    <td class="pronunciation">tii-mi</td>
                    <td>team</td>
                </tr>
                <tr>
                    <td>esimies</td>
                    <td class="pronunciation">e-si-mies</td>
                    <td>supervisor, boss</td>
                </tr>
                <tr>
                    <td>alainen</td>
                    <td class="pronunciation">a-lai-nen</td>
                    <td>subordinate</td>
                </tr>
                <tr>
                    <td>työtehtävä</td>
                    <td class="pronunciation">työ-teh-tä-vä</td>
                    <td>task, assignment</td>
                </tr>
                <tr>
                    <td>määräaika</td>
                    <td class="pronunciation">mää-rä-ai-ka</td>
                    <td>deadline</td>
                </tr>
                <tr>
                    <td>raportti</td>
                    <td class="pronunciation">ra-port-ti</td>
                    <td>report</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Meillä on kokous kokoushuoneessa kello 10.</p>
                <p><strong>English:</strong> We have a meeting in the meeting room at 10 o'clock.</p>
                <p><strong>Finnish:</strong> Esimieheni antoi minulle uuden työtehtävän.</p>
                <p><strong>English:</strong> My supervisor gave me a new assignment.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Job Search and Application</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>työpaikkailmoitus</td>
                    <td class="pronunciation">työ-paik-ka-il-moi-tus</td>
                    <td>job advertisement</td>
                </tr>
                <tr>
                    <td>avoin työpaikka</td>
                    <td class="pronunciation">a-voin työ-paik-ka</td>
                    <td>job vacancy</td>
                </tr>
                <tr>
                    <td>hakea työtä</td>
                    <td class="pronunciation">ha-ke-a työ-tä</td>
                    <td>to apply for a job</td>
                </tr>
                <tr>
                    <td>työnhakija</td>
                    <td class="pronunciation">työn-ha-ki-ja</td>
                    <td>job applicant</td>
                </tr>
                <tr>
                    <td>työhakemus</td>
                    <td class="pronunciation">työ-ha-ke-mus</td>
                    <td>job application</td>
                </tr>
                <tr>
                    <td>saatekirje</td>
                    <td class="pronunciation">saa-te-kir-je</td>
                    <td>cover letter</td>
                </tr>
                <tr>
                    <td>ansioluettelo (CV)</td>
                    <td class="pronunciation">an-si-o-lu-et-te-lo</td>
                    <td>resume, CV</td>
                </tr>
                <tr>
                    <td>työhaastattelu</td>
                    <td class="pronunciation">työ-haas-tat-te-lu</td>
                    <td>job interview</td>
                </tr>
                <tr>
                    <td>haastattelija</td>
                    <td class="pronunciation">haas-tat-te-li-ja</td>
                    <td>interviewer</td>
                </tr>
                <tr>
                    <td>työtodistus</td>
                    <td class="pronunciation">työ-to-dis-tus</td>
                    <td>work certificate</td>
                </tr>
                <tr>
                    <td>suositus</td>
                    <td class="pronunciation">suo-si-tus</td>
                    <td>recommendation</td>
                </tr>
                <tr>
                    <td>koulutus</td>
                    <td class="pronunciation">kou-lu-tus</td>
                    <td>education, training</td>
                </tr>
                <tr>
                    <td>tutkinto</td>
                    <td class="pronunciation">tut-kin-to</td>
                    <td>degree, qualification</td>
                </tr>
                <tr>
                    <td>taito</td>
                    <td class="pronunciation">tai-to</td>
                    <td>skill</td>
                </tr>
                <tr>
                    <td>kielitaito</td>
                    <td class="pronunciation">kie-li-tai-to</td>
                    <td>language skills</td>
                </tr>
                <tr>
                    <td>työlupa</td>
                    <td class="pronunciation">työ-lu-pa</td>
                    <td>work permit</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Löysin kiinnostavan työpaikkailmoituksen ja päätin hakea työtä.</p>
                <p><strong>English:</strong> I found an interesting job advertisement and decided to apply for the job.</p>
                <p><strong>Finnish:</strong> Valmistaudun työhaastatteluun lukemalla yrityksen verkkosivuja.</p>
                <p><strong>English:</strong> I'm preparing for the job interview by reading the company's website.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Work-Related Verbs</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>työskennellä</td>
                    <td class="pronunciation">työs-ken-nel-lä</td>
                    <td>to work</td>
                </tr>
                <tr>
                    <td>hakea</td>
                    <td class="pronunciation">ha-ke-a</td>
                    <td>to apply</td>
                </tr>
                <tr>
                    <td>palkata</td>
                    <td class="pronunciation">pal-ka-ta</td>
                    <td>to hire</td>
                </tr>
                <tr>
                    <td>irtisanoa</td>
                    <td class="pronunciation">ir-ti-sa-no-a</td>
                    <td>to fire, to lay off</td>
                </tr>
                <tr>
                    <td>irtisanoutua</td>
                    <td class="pronunciation">ir-ti-sa-nou-tu-a</td>
                    <td>to resign</td>
                </tr>
                <tr>
                    <td>johtaa</td>
                    <td class="pronunciation">joh-taa</td>
                    <td>to lead, to manage</td>
                </tr>
                <tr>
                    <td>neuvotella</td>
                    <td class="pronunciation">neu-vo-tel-la</td>
                    <td>to negotiate</td>
                </tr>
                <tr>
                    <td>suunnitella</td>
                    <td class="pronunciation">suun-ni-tel-la</td>
                    <td>to plan</td>
                </tr>
                <tr>
                    <td>toteuttaa</td>
                    <td class="pronunciation">to-teut-taa</td>
                    <td>to implement</td>
                </tr>
                <tr>
                    <td>raportoida</td>
                    <td class="pronunciation">ra-por-toi-da</td>
                    <td>to report</td>
                </tr>
                <tr>
                    <td>analysoida</td>
                    <td class="pronunciation">a-na-ly-soi-da</td>
                    <td>to analyze</td>
                </tr>
                <tr>
                    <td>kehittää</td>
                    <td class="pronunciation">ke-hit-tää</td>
                    <td>to develop</td>
                </tr>
                <tr>
                    <td>myydä</td>
                    <td class="pronunciation">myy-dä</td>
                    <td>to sell</td>
                </tr>
                <tr>
                    <td>markkinoida</td>
                    <td class="pronunciation">mark-ki-noi-da</td>
                    <td>to market</td>
                </tr>
                <tr>
                    <td>palvella</td>
                    <td class="pronunciation">pal-vel-la</td>
                    <td>to serve</td>
                </tr>
                <tr>
                    <td>opettaa</td>
                    <td class="pronunciation">o-pet-taa</td>
                    <td>to teach</td>
                </tr>
                <tr>
                    <td>oppia</td>
                    <td class="pronunciation">op-pi-a</td>
                    <td>to learn</td>
                </tr>
                <tr>
                    <td>kouluttaa</td>
                    <td class="pronunciation">kou-lut-taa</td>
                    <td>to train</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Useful Phrases for Work</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Hyvää työpäivää!</td>
                    <td>Have a good workday!</td>
                </tr>
                <tr>
                    <td>Olen töissä [yrityksessä].</td>
                    <td>I work at [company].</td>
                </tr>
                <tr>
                    <td>Työskentelen [alalla].</td>
                    <td>I work in [field].</td>
                </tr>
                <tr>
                    <td>Olen [ammatti].</td>
                    <td>I am a [profession].</td>
                </tr>
                <tr>
                    <td>Minulla on kokous.</td>
                    <td>I have a meeting.</td>
                </tr>
                <tr>
                    <td>Voinko auttaa?</td>
                    <td>Can I help?</td>
                </tr>
                <tr>
                    <td>Tarvitsen apua.</td>
                    <td>I need help.</td>
                </tr>
                <tr>
                    <td>Ymmärrätkö?</td>
                    <td>Do you understand?</td>
                </tr>
                <tr>
                    <td>En ymmärrä.</td>
                    <td>I don't understand.</td>
                </tr>
                <tr>
                    <td>Voitko selittää uudelleen?</td>
                    <td>Can you explain again?</td>
                </tr>
                <tr>
                    <td>Milloin on määräaika?</td>
                    <td>When is the deadline?</td>
                </tr>
                <tr>
                    <td>Olen kiinnostunut tästä työpaikasta.</td>
                    <td>I am interested in this job.</td>
                </tr>
                <tr>
                    <td>Minulla on [X] vuoden työkokemus.</td>
                    <td>I have [X] years of work experience.</td>
                </tr>
                <tr>
                    <td>Hyvää viikonloppua!</td>
                    <td>Have a good weekend!</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>At a job interview:</strong></p>
                <p><strong>Haastattelija:</strong> Kertoisitko itsestäsi ja työkokemuksestasi?</p>
                <p><strong>Työnhakija:</strong> Olen koulutukseltani insinööri ja minulla on viiden vuoden työkokemus IT-alalta. Työskentelen tällä hetkellä ohjelmoijana teknologiayrityksessä.</p>
                <p><em>Translation:</em></p>
                <p><strong>Interviewer:</strong> Could you tell about yourself and your work experience?</p>
                <p><strong>Job applicant:</strong> I am an engineer by education and I have five years of work experience in the IT field. I currently work as a programmer in a technology company.</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















