﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relative Pronouns and Clauses in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Relative Pronouns and Clauses</span>
        </div>
        
        <section class="grammar-section">
            <h2>Relative Pronouns and Clauses in Finnish</h2>
            <p>Relative clauses (relatiivilauseet) are subordinate clauses that modify a noun or pronoun in the main clause. They are introduced by relative pronouns such as "joka" (who, which) and "mikä" (what, which). This page explains how relative pronouns and clauses work in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>RELATIVE PRONOUNS IN FINNISH</h3>
            
            <div class="grammar-content">
                <p>Finnish has two main relative pronouns:</p>
                
                <h4>1. Joka (who, which, that)</h4>
                <p>Used primarily to refer to a specific, previously mentioned noun:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies, <strong>joka</strong> seisoo ovella, on opettajani.</span> <span class="english">The man <strong>who</strong> is standing at the door is my teacher.</span></p>
                    <p><span class="finnish">Kirja, <strong>jonka</strong> luin, oli mielenkiintoinen.</span> <span class="english">The book <strong>that</strong> I read was interesting.</span></p>
                </div>
                
                <h4>2. Mikä (what, which)</h4>
                <p>Used to refer to an entire clause or a less specific antecedent:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän voitti kilpailun, <strong>mikä</strong> oli yllätys.</span> <span class="english">He/she won the competition, <strong>which</strong> was a surprise.</span></p>
                    <p><span class="finnish">Kuulin äänen, <strong>mitä</strong> en tunnistanut.</span> <span class="english">I heard a sound <strong>which</strong> I didn't recognize.</span></p>
                </div>
                
                <p>Both "joka" and "mikä" can be inflected in all cases to match the grammatical role they play in the relative clause.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CASE FORMS OF JOKA</h3>
            
            <div class="grammar-content">
                <p>The relative pronoun "joka" changes form according to its case and number:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>joka</td>
                        <td>jotka</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>jonka</td>
                        <td>joiden, joitten</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>jota</td>
                        <td>joita</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>jossa</td>
                        <td>joissa</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>josta</td>
                        <td>joista</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>johon</td>
                        <td>joihin</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>jolla</td>
                        <td>joilla</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>jolta</td>
                        <td>joilta</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>jolle</td>
                        <td>joille</td>
                    </tr>
                </table>
                
                <p>Examples of "joka" in different cases:</p>
                
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">Mies, <strong>joka</strong> puhuu suomea, on opettaja.</span> <span class="english">The man <strong>who</strong> speaks Finnish is a teacher.</span></p>
                    <p>Genitive: <span class="finnish">Kirja, <strong>jonka</strong> ostin, on pöydällä.</span> <span class="english">The book <strong>that</strong> I bought is on the table.</span></p>
                    <p>Partitive: <span class="finnish">Kahvi, <strong>jota</strong> juon, on kuumaa.</span> <span class="english">The coffee <strong>that</strong> I'm drinking is hot.</span></p>
                    <p>Inessive: <span class="finnish">Talo, <strong>jossa</strong> asun, on vanha.</span> <span class="english">The house <strong>in which</strong> I live is old.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CASE FORMS OF MIKÄ</h3>
            
            <div class="grammar-content">
                <p>The relative pronoun "mikä" also changes form according to its case and number:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>mikä</td>
                        <td>mitkä</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>minkä</td>
                        <td>minkä, mitä</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>mitä</td>
                        <td>mitä</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>missä</td>
                        <td>missä</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>mistä</td>
                        <td>mistä</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>mihin</td>
                        <td>mihin</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>millä</td>
                        <td>millä</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>miltä</td>
                        <td>miltä</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>mille</td>
                        <td>mille</td>
                    </tr>
                </table>
                
                <p>Examples of "mikä" in different cases:</p>
                
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">Hän voitti kilpailun, <strong>mikä</strong> oli yllätys.</span> <span class="english">He/she won the competition, <strong>which</strong> was a surprise.</span></p>
                    <p>Genitive: <span class="finnish">Tapahtui onnettomuus, <strong>minkä</strong> vuoksi tie on suljettu.</span> <span class="english">An accident happened, <strong>because of which</strong> the road is closed.</span></p>
                    <p>Inessive: <span class="finnish">Tilanne, <strong>missä</strong> olemme nyt, on vaikea.</span> <span class="english">The situation <strong>in which</strong> we are now is difficult.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CHOOSING BETWEEN JOKA AND MIKÄ</h3>
            
            <div class="grammar-content">
                <p>The choice between "joka" and "mikä" depends on the antecedent (the word or phrase being modified):</p>
                
                <h4>1. Use "joka" when:</h4>
                <ul>
                    <li>The antecedent is a specific noun</li>
                    <li>You're referring to a person, animal, or concrete object</li>
                    <li>The relative clause provides essential information about the antecedent</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Nainen, <strong>joka</strong> puhuu puhelimessa, on äitini.</span> <span class="english">The woman <strong>who</strong> is talking on the phone is my mother.</span></p>
                    <p><span class="finnish">Auto, <strong>jonka</strong> ostin viime vuonna, on sininen.</span> <span class="english">The car <strong>that</strong> I bought last year is blue.</span></p>
                </div>
                
                <h4>2. Use "mikä" when:</h4>
                <ul>
                    <li>The antecedent is an entire clause or sentence</li>
                    <li>The antecedent is a less specific concept or abstract idea</li>
                    <li>The relative clause provides additional, non-essential information</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän ei tullut kokoukseen, <strong>mikä</strong> oli outoa.</span> <span class="english">He/she didn't come to the meeting, <strong>which</strong> was strange.</span></p>
                    <p><span class="finnish">Kuulin äänen, <strong>mitä</strong> en ollut koskaan ennen kuullut.</span> <span class="english">I heard a sound <strong>which</strong> I had never heard before.</span></p>
                </div>
                
                <p>In some contexts, especially in spoken Finnish, the distinction between "joka" and "mikä" is less strict, and "mikä" is sometimes used where "joka" would be more standard in written Finnish.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>FORMING RELATIVE CLAUSES</h3>
            
            <div class="grammar-content">
                <p>To form a relative clause in Finnish:</p>
                
                <ol>
                    <li>Identify the noun or concept you want to modify</li>
                    <li>Choose the appropriate relative pronoun (joka or mikä)</li>
                    <li>Inflect the relative pronoun to match its function in the relative clause</li>
                    <li>Place the relative clause immediately after the antecedent, usually separated by a comma</li>
                </ol>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies, <strong>joka</strong> seisoo ovella, on opettajani.</span> <span class="english">The man <strong>who</strong> is standing at the door is my teacher.</span></p>
                    <p><span class="finnish">Kirja, <strong>jonka</strong> luin, oli mielenkiintoinen.</span> <span class="english">The book <strong>that</strong> I read was interesting.</span></p>
                    <p><span class="finnish">Kaupunki, <strong>jossa</strong> asuin, on kaunis.</span> <span class="english">The city <strong>in which</strong> I lived is beautiful.</span></p>
                </div>
                
                <p>The word order within the relative clause follows normal Finnish sentence structure, with the relative pronoun typically at the beginning of the clause.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>AGREEMENT IN RELATIVE CLAUSES</h3>
            
            <div class="grammar-content">
                <p>In Finnish relative clauses, there are several important agreement patterns:</p>
                
                <h4>1. Number agreement</h4>
                <p>The relative pronoun agrees in number (singular or plural) with its antecedent:</p>
                
                <div class="grammar-example">
                    <p>Singular: <span class="finnish">Mies, <strong>joka</strong> puhuu suomea...</span> <span class="english">The man <strong>who</strong> speaks Finnish...</span></p>
                    <p>Plural: <span class="finnish">Miehet, <strong>jotka</strong> puhuvat suomea...</span> <span class="english">The men <strong>who</strong> speak Finnish...</span></p>
                </div>
                
                <h4>2. Case agreement</h4>
                <p>The case of the relative pronoun depends on its function within the relative clause, not on the case of its antecedent:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Näen miehen, <strong>joka</strong> puhuu suomea.</span> <span class="english">I see the man <strong>who</strong> speaks Finnish. (joka = subject of relative clause)</span></p>
                    <p><span class="finnish">Näen miehen, <strong>jonka</strong> tunnen.</span> <span class="english">I see the man <strong>whom</strong> I know. (jonka = object of relative clause)</span></p>
                </div>
                
                <h4>3. Verb agreement</h4>
                <p>The verb in the relative clause agrees with the relative pronoun in number:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies, joka <strong>puhuu</strong> suomea...</span> <span class="english">The man who <strong>speaks</strong> Finnish...</span></p>
                    <p><span class="finnish">Miehet, jotka <strong>puhuvat</strong> suomea...</span> <span class="english">The men who <strong>speak</strong> Finnish...</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>RESTRICTIVE AND NON-RESTRICTIVE RELATIVE CLAUSES</h3>
            
            <div class="grammar-content">
                <p>Finnish distinguishes between two types of relative clauses:</p>
                
                <h4>1. Restrictive relative clauses</h4>
                <p>These provide essential information that identifies the antecedent. Without this information, it would be unclear which person or thing is being referred to:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies, joka puhuu suomea, on opettajani.</span> <span class="english">The man who speaks Finnish is my teacher. (Identifies which man)</span></p>
                </div>
                
                <h4>2. Non-restrictive relative clauses</h4>
                <p>These provide additional, non-essential information about an already identified antecedent:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Helsingin yliopisto, joka on Suomen vanhin yliopisto, perustettiin vuonna 1640.</span> <span class="english">The University of Helsinki, which is the oldest university in Finland, was founded in 1640. (Additional information)</span></p>
                </div>
                
                <p>In Finnish, both types of relative clauses are typically separated by commas, unlike in English where only non-restrictive clauses use commas.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON PATTERNS AND EXPRESSIONS</h3>
            
            <div class="grammar-content">
                <h4>1. Se, joka... (The one who...)</h4>
                <p>This construction is used to refer to a person or thing not yet mentioned:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Se, joka</strong> tulee ensimmäisenä, saa palkinnon.</span> <span class="english"><strong>The one who</strong> comes first gets the prize.</span></p>
                    <p><span class="finnish"><strong>Se, mitä</strong> sanoit, ei ole totta.</span> <span class="english"><strong>What</strong> you said is not true.</span></p>
                </div>
                
                <h4>2. Sellainen, joka... (Such/The kind that...)</h4>
                <p>Used to describe a type or category:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Etsin <strong>sellaista kirjaa, joka</strong> kertoo Suomen historiasta.</span> <span class="english">I'm looking for <strong>a book that</strong> tells about Finnish history.</span></p>
                </div>
                
                <h4>3. Kaikki, jotka... (All who...)</h4>
                <p>Used to refer to an entire group:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Kaikki, jotka</strong> haluavat osallistua, voivat ilmoittautua.</span> <span class="english"><strong>All who</strong> want to participate can sign up.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















