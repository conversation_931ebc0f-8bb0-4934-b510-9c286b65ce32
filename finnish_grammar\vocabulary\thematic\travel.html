﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel and Transportation - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="travel-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="travel-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="travel-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Travel and Transportation</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Travel and Transportation in Finnish</h2>
            <p>This page covers essential Finnish vocabulary related to travel, transportation, and getting around in Finland. Whether you're using public transport, traveling between cities, or asking for directions, these terms will help you navigate Finland with confidence.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>General Travel Terms</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>matka</td>
                    <td class="pronunciation">mat-ka</td>
                    <td>trip, journey</td>
                </tr>
                <tr>
                    <td>matkustaa</td>
                    <td class="pronunciation">mat-kus-taa</td>
                    <td>to travel</td>
                </tr>
                <tr>
                    <td>matkustaja</td>
                    <td class="pronunciation">mat-kus-ta-ja</td>
                    <td>passenger, traveler</td>
                </tr>
                <tr>
                    <td>matkalippu</td>
                    <td class="pronunciation">mat-ka-lip-pu</td>
                    <td>ticket</td>
                </tr>
                <tr>
                    <td>matkatavarat</td>
                    <td class="pronunciation">mat-ka-ta-va-rat</td>
                    <td>luggage</td>
                </tr>
                <tr>
                    <td>laukku</td>
                    <td class="pronunciation">lauk-ku</td>
                    <td>bag</td>
                </tr>
                <tr>
                    <td>matkalaukku</td>
                    <td class="pronunciation">mat-ka-lauk-ku</td>
                    <td>suitcase</td>
                </tr>
                <tr>
                    <td>reppu</td>
                    <td class="pronunciation">rep-pu</td>
                    <td>backpack</td>
                </tr>
                <tr>
                    <td>passi</td>
                    <td class="pronunciation">pas-si</td>
                    <td>passport</td>
                </tr>
                <tr>
                    <td>henkilökortti</td>
                    <td class="pronunciation">hen-ki-lö-kort-ti</td>
                    <td>ID card</td>
                </tr>
                <tr>
                    <td>viisumi</td>
                    <td class="pronunciation">vii-su-mi</td>
                    <td>visa</td>
                </tr>
                <tr>
                    <td>kartta</td>
                    <td class="pronunciation">kart-ta</td>
                    <td>map</td>
                </tr>
                <tr>
                    <td>opas</td>
                    <td class="pronunciation">o-pas</td>
                    <td>guide</td>
                </tr>
                <tr>
                    <td>matkaopas</td>
                    <td class="pronunciation">mat-ka-o-pas</td>
                    <td>travel guide</td>
                </tr>
                <tr>
                    <td>varaus</td>
                    <td class="pronunciation">va-ra-us</td>
                    <td>reservation, booking</td>
                </tr>
                <tr>
                    <td>varata</td>
                    <td class="pronunciation">va-ra-ta</td>
                    <td>to book, to reserve</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Tarvitsen uuden passin matkaa varten.</p>
                <p><strong>English:</strong> I need a new passport for the trip.</p>
                <p><strong>Finnish:</strong> Olen varannut hotellihuoneen kahdeksi yöksi.</p>
                <p><strong>English:</strong> I have booked a hotel room for two nights.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Transportation Methods</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>julkinen liikenne</td>
                    <td class="pronunciation">jul-ki-nen lii-ken-ne</td>
                    <td>public transportation</td>
                </tr>
                <tr>
                    <td>bussi</td>
                    <td class="pronunciation">bus-si</td>
                    <td>bus</td>
                </tr>
                <tr>
                    <td>juna</td>
                    <td class="pronunciation">ju-na</td>
                    <td>train</td>
                </tr>
                <tr>
                    <td>metro</td>
                    <td class="pronunciation">met-ro</td>
                    <td>subway, metro</td>
                </tr>
                <tr>
                    <td>raitiovaunu</td>
                    <td class="pronunciation">rai-tio-vau-nu</td>
                    <td>tram</td>
                </tr>
                <tr>
                    <td>taksi</td>
                    <td class="pronunciation">tak-si</td>
                    <td>taxi</td>
                </tr>
                <tr>
                    <td>auto</td>
                    <td class="pronunciation">au-to</td>
                    <td>car</td>
                </tr>
                <tr>
                    <td>vuokra-auto</td>
                    <td class="pronunciation">vuok-ra-au-to</td>
                    <td>rental car</td>
                </tr>
                <tr>
                    <td>polkupyörä</td>
                    <td class="pronunciation">pol-ku-pyö-rä</td>
                    <td>bicycle</td>
                </tr>
                <tr>
                    <td>moottoripyörä</td>
                    <td class="pronunciation">moot-to-ri-pyö-rä</td>
                    <td>motorcycle</td>
                </tr>
                <tr>
                    <td>laiva</td>
                    <td class="pronunciation">lai-va</td>
                    <td>ship</td>
                </tr>
                <tr>
                    <td>lentokone</td>
                    <td class="pronunciation">len-to-ko-ne</td>
                    <td>airplane</td>
                </tr>
                <tr>
                    <td>kävellä</td>
                    <td class="pronunciation">kä-vel-lä</td>
                    <td>to walk</td>
                </tr>
                <tr>
                    <td>pyöräillä</td>
                    <td class="pronunciation">pyö-räil-lä</td>
                    <td>to cycle</td>
                </tr>
                <tr>
                    <td>ajaa</td>
                    <td class="pronunciation">a-jaa</td>
                    <td>to drive</td>
                </tr>
                <tr>
                    <td>lentää</td>
                    <td class="pronunciation">len-tää</td>
                    <td>to fly</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Finland has an excellent public transportation system, especially in larger cities. In Helsinki, the HSL (Helsingin seudun liikenne) system includes buses, trams, metro, commuter trains, and ferries.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Places and Stations</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>asema</td>
                    <td class="pronunciation">a-se-ma</td>
                    <td>station</td>
                </tr>
                <tr>
                    <td>rautatieasema</td>
                    <td class="pronunciation">rau-ta-tie-a-se-ma</td>
                    <td>railway station</td>
                </tr>
                <tr>
                    <td>bussiasema</td>
                    <td class="pronunciation">bus-si-a-se-ma</td>
                    <td>bus station</td>
                </tr>
                <tr>
                    <td>bussipysäkki</td>
                    <td class="pronunciation">bus-si-py-säk-ki</td>
                    <td>bus stop</td>
                </tr>
                <tr>
                    <td>metroasema</td>
                    <td class="pronunciation">met-ro-a-se-ma</td>
                    <td>metro station</td>
                </tr>
                <tr>
                    <td>raitiovaunupysäkki</td>
                    <td class="pronunciation">rai-tio-vau-nu-py-säk-ki</td>
                    <td>tram stop</td>
                </tr>
                <tr>
                    <td>lentokenttä</td>
                    <td class="pronunciation">len-to-kent-tä</td>
                    <td>airport</td>
                </tr>
                <tr>
                    <td>satama</td>
                    <td class="pronunciation">sa-ta-ma</td>
                    <td>harbor, port</td>
                </tr>
                <tr>
                    <td>terminaali</td>
                    <td class="pronunciation">ter-mi-naa-li</td>
                    <td>terminal</td>
                </tr>
                <tr>
                    <td>laituri</td>
                    <td class="pronunciation">lai-tu-ri</td>
                    <td>platform, pier</td>
                </tr>
                <tr>
                    <td>lähtöportti</td>
                    <td class="pronunciation">läh-tö-port-ti</td>
                    <td>departure gate</td>
                </tr>
                <tr>
                    <td>taksitolppa</td>
                    <td class="pronunciation">tak-si-tolp-pa</td>
                    <td>taxi stand</td>
                </tr>
                <tr>
                    <td>parkkipaikka</td>
                    <td class="pronunciation">park-ki-paik-ka</td>
                    <td>parking lot</td>
                </tr>
                <tr>
                    <td>pysäköintialue</td>
                    <td class="pronunciation">py-sä-köin-ti-a-lu-e</td>
                    <td>parking area</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Juna lähtee raiteelta kolme.</p>
                <p><strong>English:</strong> The train departs from platform three.</p>
                <p><strong>Finnish:</strong> Lähin bussipysäkki on kadun toisella puolella.</p>
                <p><strong>English:</strong> The nearest bus stop is on the other side of the street.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Tickets and Travel Cards</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>lippu</td>
                    <td class="pronunciation">lip-pu</td>
                    <td>ticket</td>
                </tr>
                <tr>
                    <td>kertalippu</td>
                    <td class="pronunciation">ker-ta-lip-pu</td>
                    <td>single ticket</td>
                </tr>
                <tr>
                    <td>päivälippu</td>
                    <td class="pronunciation">päi-vä-lip-pu</td>
                    <td>day ticket</td>
                </tr>
                <tr>
                    <td>kausilippu</td>
                    <td class="pronunciation">kau-si-lip-pu</td>
                    <td>season ticket</td>
                </tr>
                <tr>
                    <td>matkakortti</td>
                    <td class="pronunciation">mat-ka-kort-ti</td>
                    <td>travel card</td>
                </tr>
                <tr>
                    <td>ladata matkakortti</td>
                    <td class="pronunciation">la-da-ta mat-ka-kort-ti</td>
                    <td>to load/top up a travel card</td>
                </tr>
                <tr>
                    <td>leimata</td>
                    <td class="pronunciation">lei-ma-ta</td>
                    <td>to validate (a ticket)</td>
                </tr>
                <tr>
                    <td>meno-paluu</td>
                    <td class="pronunciation">me-no-pa-luu</td>
                    <td>return ticket</td>
                </tr>
                <tr>
                    <td>menolippu</td>
                    <td class="pronunciation">me-no-lip-pu</td>
                    <td>one-way ticket</td>
                </tr>
                <tr>
                    <td>alennuslippu</td>
                    <td class="pronunciation">a-len-nus-lip-pu</td>
                    <td>discount ticket</td>
                </tr>
                <tr>
                    <td>opiskelijalippu</td>
                    <td class="pronunciation">o-pis-ke-li-ja-lip-pu</td>
                    <td>student ticket</td>
                </tr>
                <tr>
                    <td>lippuautomaatti</td>
                    <td class="pronunciation">lip-pu-au-to-maat-ti</td>
                    <td>ticket machine</td>
                </tr>
                <tr>
                    <td>lipputoimisto</td>
                    <td class="pronunciation">lip-pu-toi-mis-to</td>
                    <td>ticket office</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Helsinki and other major cities, you can use a travel card (matkakortti) for all public transportation. You can buy and load these cards at R-kioski shops, HSL service points, and ticket machines.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Directions and Navigation</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>suunta</td>
                    <td class="pronunciation">suun-ta</td>
                    <td>direction</td>
                </tr>
                <tr>
                    <td>pohjoinen</td>
                    <td class="pronunciation">poh-joi-nen</td>
                    <td>north</td>
                </tr>
                <tr>
                    <td>etelä</td>
                    <td class="pronunciation">e-te-lä</td>
                    <td>south</td>
                </tr>
                <tr>
                    <td>itä</td>
                    <td class="pronunciation">i-tä</td>
                    <td>east</td>
                </tr>
                <tr>
                    <td>länsi</td>
                    <td class="pronunciation">län-si</td>
                    <td>west</td>
                </tr>
                <tr>
                    <td>oikealle</td>
                    <td class="pronunciation">oi-ke-al-le</td>
                    <td>to the right</td>
                </tr>
                <tr>
                    <td>vasemmalle</td>
                    <td class="pronunciation">va-sem-mal-le</td>
                    <td>to the left</td>
                </tr>
                <tr>
                    <td>suoraan</td>
                    <td class="pronunciation">suo-raan</td>
                    <td>straight ahead</td>
                </tr>
                <tr>
                    <td>takaisin</td>
                    <td class="pronunciation">ta-kai-sin</td>
                    <td>back</td>
                </tr>
                <tr>
                    <td>kääntyä</td>
                    <td class="pronunciation">kään-ty-ä</td>
                    <td>to turn</td>
                </tr>
                <tr>
                    <td>risteys</td>
                    <td class="pronunciation">ris-te-ys</td>
                    <td>intersection</td>
                </tr>
                <tr>
                    <td>liikenneympyrä</td>
                    <td class="pronunciation">lii-ken-ne-ym-py-rä</td>
                    <td>roundabout</td>
                </tr>
                <tr>
                    <td>katu</td>
                    <td class="pronunciation">ka-tu</td>
                    <td>street</td>
                </tr>
                <tr>
                    <td>tie</td>
                    <td class="pronunciation">tie</td>
                    <td>road</td>
                </tr>
                <tr>
                    <td>moottoritie</td>
                    <td class="pronunciation">moot-to-ri-tie</td>
                    <td>highway</td>
                </tr>
                <tr>
                    <td>silta</td>
                    <td class="pronunciation">sil-ta</td>
                    <td>bridge</td>
                </tr>
                <tr>
                    <td>tunneli</td>
                    <td class="pronunciation">tun-ne-li</td>
                    <td>tunnel</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Asking for directions:</strong></p>
                <p><strong>A:</strong> Anteeksi, missä on rautatieasema?</p>
                <p><strong>B:</strong> Mene suoraan kaksi korttelia ja käänny sitten vasemmalle. Rautatieasema on oikealla puolella.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Excuse me, where is the railway station?</p>
                <p><strong>B:</strong> Go straight for two blocks and then turn left. The railway station is on the right side.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Travel Phrases</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Missä on lähin bussipysäkki?</td>
                    <td>Where is the nearest bus stop?</td>
                </tr>
                <tr>
                    <td>Milloin seuraava juna lähtee?</td>
                    <td>When does the next train leave?</td>
                </tr>
                <tr>
                    <td>Mihin aikaan bussi saapuu?</td>
                    <td>What time does the bus arrive?</td>
                </tr>
                <tr>
                    <td>Miten pääsen keskustaan?</td>
                    <td>How do I get to the city center?</td>
                </tr>
                <tr>
                    <td>Onko tämä bussi menossa...?</td>
                    <td>Is this bus going to...?</td>
                </tr>
                <tr>
                    <td>Paljonko lippu maksaa?</td>
                    <td>How much does the ticket cost?</td>
                </tr>
                <tr>
                    <td>Mistä voin ostaa lipun?</td>
                    <td>Where can I buy a ticket?</td>
                </tr>
                <tr>
                    <td>Tarvitseeko minun vaihtaa bussia/junaa?</td>
                    <td>Do I need to change buses/trains?</td>
                </tr>
                <tr>
                    <td>Mikä on seuraava pysäkki?</td>
                    <td>What is the next stop?</td>
                </tr>
                <tr>
                    <td>Voisitteko kertoa, kun saavumme...?</td>
                    <td>Could you tell me when we arrive at...?</td>
                </tr>
                <tr>
                    <td>Haluaisin vuokrata auton.</td>
                    <td>I would like to rent a car.</td>
                </tr>
                <tr>
                    <td>Missä on taksiasema?</td>
                    <td>Where is the taxi stand?</td>
                </tr>
                <tr>
                    <td>Viekää minut tähän osoitteeseen, kiitos.</td>
                    <td>Take me to this address, please.</td>
                </tr>
                <tr>
                    <td>Onko tämä oikea suunta...?</td>
                    <td>Is this the right direction for...?</td>
                </tr>
                <tr>
                    <td>Olen eksynyt.</td>
                    <td>I am lost.</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Accommodation</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>hotelli</td>
                    <td class="pronunciation">ho-tel-li</td>
                    <td>hotel</td>
                </tr>
                <tr>
                    <td>hostelli</td>
                    <td class="pronunciation">hos-tel-li</td>
                    <td>hostel</td>
                </tr>
                <tr>
                    <td>majatalo</td>
                    <td class="pronunciation">ma-ja-ta-lo</td>
                    <td>guesthouse</td>
                </tr>
                <tr>
                    <td>mökki</td>
                    <td class="pronunciation">mök-ki</td>
                    <td>cottage</td>
                </tr>
                <tr>
                    <td>leirintäalue</td>
                    <td class="pronunciation">lei-rin-tä-a-lu-e</td>
                    <td>campsite</td>
                </tr>
                <tr>
                    <td>huone</td>
                    <td class="pronunciation">huo-ne</td>
                    <td>room</td>
                </tr>
                <tr>
                    <td>yhden hengen huone</td>
                    <td class="pronunciation">yh-den hen-gen huo-ne</td>
                    <td>single room</td>
                </tr>
                <tr>
                    <td>kahden hengen huone</td>
                    <td class="pronunciation">kah-den hen-gen huo-ne</td>
                    <td>double room</td>
                </tr>
                <tr>
                    <td>vastaanotto</td>
                    <td class="pronunciation">vas-taan-ot-to</td>
                    <td>reception</td>
                </tr>
                <tr>
                    <td>avain</td>
                    <td class="pronunciation">a-vain</td>
                    <td>key</td>
                </tr>
                <tr>
                    <td>varaus</td>
                    <td class="pronunciation">va-ra-us</td>
                    <td>reservation</td>
                </tr>
                <tr>
                    <td>sisäänkirjautuminen</td>
                    <td class="pronunciation">si-sään-kir-jau-tu-mi-nen</td>
                    <td>check-in</td>
                </tr>
                <tr>
                    <td>uloskirjautuminen</td>
                    <td class="pronunciation">u-los-kir-jau-tu-mi-nen</td>
                    <td>check-out</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>At a hotel:</strong></p>
                <p><strong>A:</strong> Hyvää päivää. Minulla on varaus nimellä Virtanen.</p>
                <p><strong>B:</strong> Tervetuloa! Kyllä, löydän varauksenne. Kahden hengen huone kahdeksi yöksi, onko oikein?</p>
                <p><strong>A:</strong> Kyllä, se on oikein.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Good day. I have a reservation under the name Virtanen.</p>
                <p><strong>B:</strong> Welcome! Yes, I can find your reservation. A double room for two nights, is that correct?</p>
                <p><strong>A:</strong> Yes, that's correct.</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












