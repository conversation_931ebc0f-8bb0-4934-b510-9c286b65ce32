<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Most Common Finnish Words - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        /* Column width styles */
        .vocabulary-table th:nth-child(1), 
        .vocabulary-table td:nth-child(1) {
            width: 1%;
            white-space: nowrap;
        }
        
        .vocabulary-table th:nth-child(2), 
        .vocabulary-table td:nth-child(2) {
            width: 15%;
            white-space: nowrap;
        }
        
        .vocabulary-table th:nth-child(3), 
        .vocabulary-table td:nth-child(3) {
            width: auto;
        }
        
        .vocabulary-table th:nth-child(4), 
        .vocabulary-table td:nth-child(4) {
            width: 10%;
            white-space: nowrap;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .word-category {
            font-weight: 500;
            color: #0066cc;
        }
        
        .search-pagination-container {
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .search-container {
            flex: 1;
            min-width: 300px;
        }
        
        .search-input {
            padding: 10px;
            width: 100%;
            max-width: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .filter-container {
            margin: 15px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-button {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 5px 15px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .filter-button:hover, .filter-button.active {
            background-color: #0066cc;
            color: white;
            border-color: #0066cc;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .pagination-button {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 8px 12px;
            margin: 0 2px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            min-width: 40px;
            text-align: center;
        }
        
        .pagination-button.nav-button {
            padding: 8px 15px;
        }
        
        .pagination-button:hover, .pagination-button.active {
            background-color: #0066cc;
            color: white;
            border-color: #0066cc;
        }
        
        .pagination-ellipsis {
            padding: 8px 5px;
            color: #666;
        }
        
        /* Loading indicator */
        .loading-indicator {
            text-align: center;
            padding: 20px;
            font-size: 18px;
            color: #0066cc;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .filter-button {
            background-color: #333;
            border-color: #444;
            color: #ddd;
        }
        
        [data-theme="dark"] .filter-button:hover, [data-theme="dark"] .filter-button.active {
            background-color: #0066cc;
            border-color: #0066cc;
            color: white;
        }
        
        [data-theme="dark"] .pagination-button {
            background-color: #333;
            border-color: #444;
            color: #ddd;
        }
        
        [data-theme="dark"] .pagination-button:hover, [data-theme="dark"] .pagination-button.active {
            background-color: #0066cc;
            border-color: #0066cc;
            color: white;
        }
        
        [data-theme="dark"] .search-input {
            background-color: #333;
            border-color: #444;
            color: #fff;
        }
        
        [data-theme="dark"] .search-pagination-container {
            color: #ddd;
        }
        
        [data-theme="dark"] .pagination-ellipsis {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="common-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="common-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="common-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Most Common Finnish Words</span>
        </div>
        
        <section class="vocabulary-section">
            <h3>Top 2000 Most Common Finnish Words</h3>
            
            <div class="search-pagination-container">
                <div class="search-container">
                    <input type="text" id="word-search" class="search-input" placeholder="Search for a word...">
                </div>
                <div class="pagination">
                    <button class="pagination-button nav-button" id="prev-page"><i class="fas fa-chevron-left"></i> Previous</button>
                    <button class="pagination-button active" data-page="1">1</button>
                    <button class="pagination-button" data-page="2">2</button>
                    <button class="pagination-button" data-page="3">3</button>
                    <button class="pagination-button" data-page="4">4</button>
                    <button class="pagination-button" data-page="5">5</button>
                    <span class="pagination-ellipsis">...</span>
                    <button class="pagination-button" data-page="20">20</button>
                    <button class="pagination-button nav-button" id="next-page">Next <i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
            
            <div class="filter-container" id="filter-container">
                <button class="filter-button active" data-filter="all">All</button>
                <!-- Other filter buttons will be added dynamically -->
            </div>
            
            <div id="loading-indicator" class="loading-indicator">
                <i class="fas fa-spinner fa-spin"></i> Loading vocabulary data...
            </div>
            
            <table class="vocabulary-table" id="words-table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>Word</th>
                        <th>Sentence</th>
                        <th>Category</th>
                    </tr>
                </thead>
                <tbody id="vocabulary-body">
                    <!-- Table content will be dynamically generated -->
                </tbody>
            </table>
        </section>
    </div>

    



<script>
// Global variables
let vocabularyData = [];
let currentPage = 1;
let currentFilter = 'all';
let searchTerm = '';

// Function to fetch vocabulary data
async function fetchVocabularyData() {
    try {
        const response = await fetch('../../../finnish_vocabulary.json');
        if (!response.ok) {
            throw new Error('Failed to fetch vocabulary data');
        }
        vocabularyData = await response.json();
        document.getElementById('loading-indicator').style.display = 'none';
        renderVocabularyTable();
    } catch (error) {
        console.error('Error fetching vocabulary data:', error);
        document.getElementById('loading-indicator').textContent = 'Error loading vocabulary data. Please try again later.';
    }
}

// Function to render the vocabulary table
function renderVocabularyTable() {
    const tableBody = document.getElementById('vocabulary-body');
    tableBody.innerHTML = '';
    
    // Calculate start and end indices for current page
    const startIndex = (currentPage - 1) * 100;
    const endIndex = startIndex + 100;
    
    // Filter data based on current filter and search term
    const filteredData = vocabularyData.filter(item => {
        const matchesFilter = currentFilter === 'all' || 
                             item.category.toLowerCase() === currentFilter.toLowerCase();
        const matchesSearch = searchTerm === '' || 
                             item.finnish.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             item.english.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesFilter && matchesSearch;
    });
    
    // Get data for current page
    const pageData = filteredData.filter(item => 
        item.rank > startIndex && item.rank <= endIndex
    );
    
    // Create table rows
    pageData.forEach(item => {
        const row = document.createElement('tr');
        
        const rankCell = document.createElement('td');
        rankCell.textContent = item.rank;
        
        const wordCell = document.createElement('td');
        wordCell.innerHTML = `${item.finnish}<br>${item.english}`;
        
        const sentenceCell = document.createElement('td');
        sentenceCell.innerHTML = `${item.finnishSentence}<br>${item.englishSentence}`;
        
        const categoryCell = document.createElement('td');
        categoryCell.innerHTML = `<span class="word-category">${item.category}</span>`;
        
        row.appendChild(rankCell);
        row.appendChild(wordCell);
        row.appendChild(sentenceCell);
        row.appendChild(categoryCell);
        
        tableBody.appendChild(row);
    });
    
    // If no results found
    if (pageData.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = 4;
        cell.textContent = 'No matching words found.';
        cell.style.textAlign = 'center';
        cell.style.padding = '20px';
        row.appendChild(cell);
        tableBody.appendChild(row);
    }
}

// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Fetch vocabulary data
    fetchVocabularyData();
    
    // Set up pagination
    const paginationButtons = document.querySelectorAll('.pagination-button[data-page]');
    paginationButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            paginationButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            currentPage = parseInt(this.getAttribute('data-page'));
            renderVocabularyTable();
        });
    });
    
    // Set up previous and next buttons
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');
    
    prevButton.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            updateActivePaginationButton();
            renderVocabularyTable();
        }
    });
    
    nextButton.addEventListener('click', function() {
        if (currentPage < 20) {
            currentPage++;
            updateActivePaginationButton();
            renderVocabularyTable();
        }
    });
    
    function updateActivePaginationButton() {
        paginationButtons.forEach(btn => {
            if (parseInt(btn.getAttribute('data-page')) === currentPage) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
    
    // Set up search functionality
    const searchInput = document.getElementById('word-search');
    searchInput.addEventListener('input', function() {
        searchTerm = this.value.toLowerCase();
        currentPage = 1;
        updateActivePaginationButton();
        renderVocabularyTable();
    });
    
    // Set up filter buttons
    const filterContainer = document.getElementById('filter-container');
    const categories = ['pronouns', 'verbs', 'nouns', 'adjectives', 'adverbs', 'conjunctions', 'prepositions', 'interjection', 'phrase'];
    
    categories.forEach(category => {
        const button = document.createElement('button');
        button.className = 'filter-button';
        button.setAttribute('data-filter', category);
        button.textContent = category.charAt(0).toUpperCase() + category.slice(1);
        
        button.addEventListener('click', function() {
            // Remove active class from all filter buttons
            document.querySelectorAll('.filter-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to clicked button
            this.classList.add('active');
            
            currentFilter = this.getAttribute('data-filter');
            currentPage = 1;
            updateActivePaginationButton();
            renderVocabularyTable();
        });
        
        filterContainer.appendChild(button);
    });
    
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('common-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>