﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reported Speech in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Reported Speech</span>
        </div>
        
        <section class="grammar-section">
            <h2>Reported Speech in Finnish</h2>
            <p>Reported speech (epäsuora esitys) is used to convey what someone else has said without quoting them directly. In Finnish, reported speech involves changes in pronouns, tenses, and word order, and typically uses the conjunction "että" (that). This page explains how to form and use reported speech in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>DIRECT VS. REPORTED SPEECH</h3>
            
            <div class="grammar-content">
                <p>Let's first understand the difference between direct and reported speech:</p>
                
                <h4>1. Direct speech (suora esitys)</h4>
                <p>Quotes the exact words spoken, using quotation marks:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Liisa sanoi: "Minä tulen huomenna."</span> <span class="english">Liisa said: "I will come tomorrow."</span></p>
                    <p><span class="finnish">Hän kysyi: "Missä sinä asut?"</span> <span class="english">He/she asked: "Where do you live?"</span></p>
                </div>
                
                <h4>2. Reported speech (epäsuora esitys)</h4>
                <p>Reports what was said without using the exact words, typically using "että" (that):</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Liisa sanoi, että hän tulee huomenna.</span> <span class="english">Liisa said that she will come tomorrow.</span></p>
                    <p><span class="finnish">Hän kysyi, missä minä asun.</span> <span class="english">He/she asked where I live.</span></p>
                </div>
                
                <p>In Finnish reported speech, the conjunction "että" is used for statements, while question words (missä, milloin, etc.) or the -ko/-kö particle are used for questions.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>REPORTING STATEMENTS</h3>
            
            <div class="grammar-content">
                <p>To report statements in Finnish, use a reporting verb followed by "että" and the reported clause:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Matti sanoi: "Minä olen väsynyt."</span> <span class="english">Matti said: "I am tired."</span></p>
                    <p>Reported: <span class="finnish">Matti sanoi, <strong>että</strong> hän on väsynyt.</span> <span class="english">Matti said <strong>that</strong> he is tired.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Hän kertoi: "Menen Helsinkiin ensi viikolla."</span> <span class="english">He/she told: "I'm going to Helsinki next week."</span></p>
                    <p>Reported: <span class="finnish">Hän kertoi, <strong>että</strong> hän menee Helsinkiin ensi viikolla.</span> <span class="english">He/she told <strong>that</strong> he/she is going to Helsinki next week.</span></p>
                </div>
                
                <p>Common reporting verbs for statements include:</p>
                <ul>
                    <li><span class="finnish">sanoa</span> - to say</li>
                    <li><span class="finnish">kertoa</span> - to tell</li>
                    <li><span class="finnish">ilmoittaa</span> - to announce</li>
                    <li><span class="finnish">väittää</span> - to claim</li>
                    <li><span class="finnish">mainita</span> - to mention</li>
                    <li><span class="finnish">selittää</span> - to explain</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>REPORTING QUESTIONS</h3>
            
            <div class="grammar-content">
                <h4>1. Reporting yes/no questions</h4>
                <p>For yes/no questions (those with -ko/-kö), keep the -ko/-kö particle in the reported question:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Hän kysyi: "Tuletko huomenna?"</span> <span class="english">He/she asked: "Are you coming tomorrow?"</span></p>
                    <p>Reported: <span class="finnish">Hän kysyi, <strong>tulenko</strong> huomenna.</span> <span class="english">He/she asked if I am coming tomorrow.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Opettaja kysyi: "Oletko tehnyt läksyt?"</span> <span class="english">The teacher asked: "Have you done your homework?"</span></p>
                    <p>Reported: <span class="finnish">Opettaja kysyi, <strong>olenko</strong> tehnyt läksyt.</span> <span class="english">The teacher asked if I have done my homework.</span></p>
                </div>
                
                <h4>2. Reporting wh-questions</h4>
                <p>For questions with question words (kuka, mikä, missä, etc.), keep the question word in the reported question:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Hän kysyi: "Missä sinä asut?"</span> <span class="english">He/she asked: "Where do you live?"</span></p>
                    <p>Reported: <span class="finnish">Hän kysyi, <strong>missä</strong> minä asun.</span> <span class="english">He/she asked where I live.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Vierailija kysyi: "Milloin museo avautuu?"</span> <span class="english">The visitor asked: "When does the museum open?"</span></p>
                    <p>Reported: <span class="finnish">Vierailija kysyi, <strong>milloin</strong> museo avautuu.</span> <span class="english">The visitor asked when the museum opens.</span></p>
                </div>
                
                <p>Common reporting verbs for questions include:</p>
                <ul>
                    <li><span class="finnish">kysyä</span> - to ask</li>
                    <li><span class="finnish">tiedustella</span> - to inquire</li>
                    <li><span class="finnish">ihmetellä</span> - to wonder</li>
                    <li><span class="finnish">haluta tietää</span> - to want to know</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PRONOUN CHANGES</h3>
            
            <div class="grammar-content">
                <p>When converting direct speech to reported speech, pronouns often change to reflect the new perspective:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Direct Speech</th>
                        <th>Reported Speech</th>
                    </tr>
                    <tr>
                        <td><span class="finnish">minä</span> (I)</td>
                        <td><span class="finnish">hän</span> (he/she) - if reporting what someone else said<br><span class="finnish">minä</span> (I) - if reporting what was said to you</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">sinä</span> (you)</td>
                        <td><span class="finnish">minä</span> (I) - if you were the addressee<br><span class="finnish">hän</span> (he/she) - if someone else was the addressee</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">hän</span> (he/she)</td>
                        <td>Usually remains <span class="finnish">hän</span> (he/she)</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">me</span> (we)</td>
                        <td><span class="finnish">he</span> (they) - if reporting what others said<br><span class="finnish">me</span> (we) - if you were included in the original "we"</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">te</span> (you plural)</td>
                        <td><span class="finnish">me</span> (we) - if you were among the addressees<br><span class="finnish">he</span> (they) - if others were the addressees</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Liisa sanoi: "<strong>Minä</strong> rakastan <strong>sinua</strong>."</span> <span class="english">Liisa said: "<strong>I</strong> love <strong>you</strong>."</span></p>
                    <p>Reported (by a third person): <span class="finnish">Liisa sanoi, että <strong>hän</strong> rakastaa <strong>häntä</strong>.</span> <span class="english">Liisa said that <strong>she</strong> loves <strong>him/her</strong>.</span></p>
                    <p>Reported (by the addressee): <span class="finnish">Liisa sanoi, että <strong>hän</strong> rakastaa <strong>minua</strong>.</span> <span class="english">Liisa said that <strong>she</strong> loves <strong>me</strong>.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>TIME AND PLACE EXPRESSIONS</h3>
            
            <div class="grammar-content">
                <p>Time and place expressions may change in reported speech to reflect the new context:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Direct Speech</th>
                        <th>Reported Speech</th>
                    </tr>
                    <tr>
                        <td><span class="finnish">tänään</span> (today)</td>
                        <td><span class="finnish">sinä päivänä</span> (that day)</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">huomenna</span> (tomorrow)</td>
                        <td><span class="finnish">seuraavana päivänä</span> (the next day)</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">eilen</span> (yesterday)</td>
                        <td><span class="finnish">edellisenä päivänä</span> (the previous day)</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">täällä</span> (here)</td>
                        <td><span class="finnish">siellä</span> (there)</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">tämä</span> (this)</td>
                        <td><span class="finnish">se</span> (that)</td>
                    </tr>
                    <tr>
                        <td><span class="finnish">nyt</span> (now)</td>
                        <td><span class="finnish">silloin</span> (then)</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Hän sanoi: "Tulen <strong>huomenna</strong> <strong>tänne</strong>."</span> <span class="english">He/she said: "I will come <strong>tomorrow</strong> <strong>here</strong>."</span></p>
                    <p>Reported: <span class="finnish">Hän sanoi, että hän tulee <strong>seuraavana päivänä</strong> <strong>sinne</strong>.</span> <span class="english">He/she said that he/she will come <strong>the next day</strong> <strong>there</strong>.</span></p>
                </div>
                
                <p>However, in Finnish, these changes are not as strict as in some other languages. Often, the original time and place expressions are kept, especially in informal contexts or when the reporting happens soon after the original speech.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>TENSE CHANGES</h3>
            
            <div class="grammar-content">
                <p>Unlike in English, Finnish does not have a strict sequence of tenses rule for reported speech. The tense in reported speech often remains the same as in the direct speech:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Hän sanoi: "Minä <strong>olen</strong> sairas."</span> <span class="english">He/she said: "I <strong>am</strong> sick."</span></p>
                    <p>Reported: <span class="finnish">Hän sanoi, että hän <strong>on</strong> sairas.</span> <span class="english">He/she said that he/she <strong>is</strong> sick.</span></p>
                </div>
                
                <p>However, if the time reference has clearly changed, the tense may be adjusted accordingly:</p>
                
                <div class="grammar-example">
                    <p>Direct (at the time): <span class="finnish">Hän sanoi: "Minä <strong>tulen</strong> huomenna."</span> <span class="english">He/she said: "I <strong>will come</strong> tomorrow."</span></p>
                    <p>Reported (after the event): <span class="finnish">Hän sanoi, että hän <strong>tulisi</strong> seuraavana päivänä.</span> <span class="english">He/she said that he/she <strong>would come</strong> the next day.</span></p>
                </div>
                
                <p>The conditional mood is sometimes used in reported speech to indicate that the speaker is reporting someone else's words without taking responsibility for their accuracy:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Hän väitti: "Minä <strong>olen</strong> viaton."</span> <span class="english">He/she claimed: "I <strong>am</strong> innocent."</span></p>
                    <p>Reported: <span class="finnish">Hän väitti, että hän <strong>olisi</strong> viaton.</span> <span class="english">He/she claimed that he/she <strong>was</strong> innocent. (implying doubt)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>REPORTING COMMANDS</h3>
            
            <div class="grammar-content">
                <p>When reporting commands or requests, Finnish typically uses the conjunction "että" with a conditional form or an infinitive construction:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Opettaja sanoi: "<strong>Lukekaa</strong> tämä kirja."</span> <span class="english">The teacher said: "<strong>Read</strong> this book."</span></p>
                    <p>Reported: <span class="finnish">Opettaja sanoi, että meidän pitäisi <strong>lukea</strong> se kirja.</span> <span class="english">The teacher said that we should <strong>read</strong> that book.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Äiti käski: "<strong>Siivoa</strong> huoneesi!"</span> <span class="english">Mother ordered: "<strong>Clean</strong> your room!"</span></p>
                    <p>Reported: <span class="finnish">Äiti käski minua <strong>siivoamaan</strong> huoneeni.</span> <span class="english">Mother ordered me <strong>to clean</strong> my room.</span></p>
                </div>
                
                <p>Common verbs for reporting commands include:</p>
                <ul>
                    <li><span class="finnish">käskeä</span> - to order, command</li>
                    <li><span class="finnish">pyytää</span> - to ask, request</li>
                    <li><span class="finnish">kehottaa</span> - to urge, advise</li>
                    <li><span class="finnish">neuvoa</span> - to advise</li>
                    <li><span class="finnish">kieltää</span> - to forbid</li>
                </ul>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















