﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food and Drink - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .food-category {
            margin-bottom: 40px;
        }
        
        .food-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .food-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .food-card h4 {
            margin-top: 0;
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .food-card p {
            margin: 5px 0;
            font-size: 0.95em;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .food-card {
            background-color: #2a2a2a;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="food-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="food-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="food-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Food and Drink</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Food and Drink in Finnish</h2>
            <p>Food and drink vocabulary is essential for daily life in Finland. Whether you're shopping for groceries, dining at a restaurant, or discussing Finnish cuisine, knowing these terms will be very helpful. This page covers common food and drink vocabulary in Finnish, along with useful phrases for ordering and discussing meals.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Basic Food Terms</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>ruoka</td>
                    <td class="pronunciation">ruo-ka</td>
                    <td>food</td>
                </tr>
                <tr>
                    <td>juoma</td>
                    <td class="pronunciation">juo-ma</td>
                    <td>drink, beverage</td>
                </tr>
                <tr>
                    <td>ateria</td>
                    <td class="pronunciation">a-te-ri-a</td>
                    <td>meal</td>
                </tr>
                <tr>
                    <td>aamiainen</td>
                    <td class="pronunciation">aa-mi-ai-nen</td>
                    <td>breakfast</td>
                </tr>
                <tr>
                    <td>lounas</td>
                    <td class="pronunciation">lou-nas</td>
                    <td>lunch</td>
                </tr>
                <tr>
                    <td>päivällinen</td>
                    <td class="pronunciation">päi-väl-li-nen</td>
                    <td>dinner</td>
                </tr>
                <tr>
                    <td>illallinen</td>
                    <td class="pronunciation">il-lal-li-nen</td>
                    <td>dinner (evening meal)</td>
                </tr>
                <tr>
                    <td>välipala</td>
                    <td class="pronunciation">vä-li-pa-la</td>
                    <td>snack</td>
                </tr>
                <tr>
                    <td>jälkiruoka</td>
                    <td class="pronunciation">jäl-ki-ruo-ka</td>
                    <td>dessert</td>
                </tr>
                <tr>
                    <td>alkuruoka</td>
                    <td class="pronunciation">al-ku-ruo-ka</td>
                    <td>appetizer, starter</td>
                </tr>
                <tr>
                    <td>pääruoka</td>
                    <td class="pronunciation">pää-ruo-ka</td>
                    <td>main course</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Mitä söisit lounaaksi?</p>
                <p><strong>English:</strong> What would you like to eat for lunch?</p>
                <p><strong>Finnish:</strong> Haluaisin jälkiruokaa.</p>
                <p><strong>English:</strong> I would like some dessert.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Food Categories</h3>
            
            <div class="food-category">
                <h4>Fruits and Vegetables</h4>
                <table class="vocabulary-table">
                    <tr>
                        <th>Finnish</th>
                        <th>Pronunciation</th>
                        <th>English</th>
                    </tr>
                    <tr>
                        <td>hedelmä</td>
                        <td class="pronunciation">he-del-mä</td>
                        <td>fruit</td>
                    </tr>
                    <tr>
                        <td>vihannes</td>
                        <td class="pronunciation">vi-han-nes</td>
                        <td>vegetable</td>
                    </tr>
                    <tr>
                        <td>omena</td>
                        <td class="pronunciation">o-me-na</td>
                        <td>apple</td>
                    </tr>
                    <tr>
                        <td>banaani</td>
                        <td class="pronunciation">ba-naa-ni</td>
                        <td>banana</td>
                    </tr>
                    <tr>
                        <td>appelsiini</td>
                        <td class="pronunciation">ap-pel-sii-ni</td>
                        <td>orange</td>
                    </tr>
                    <tr>
                        <td>mansikka</td>
                        <td class="pronunciation">man-sik-ka</td>
                        <td>strawberry</td>
                    </tr>
                    <tr>
                        <td>peruna</td>
                        <td class="pronunciation">pe-ru-na</td>
                        <td>potato</td>
                    </tr>
                    <tr>
                        <td>tomaatti</td>
                        <td class="pronunciation">to-maat-ti</td>
                        <td>tomato</td>
                    </tr>
                    <tr>
                        <td>kurkku</td>
                        <td class="pronunciation">kurk-ku</td>
                        <td>cucumber</td>
                    </tr>
                    <tr>
                        <td>porkkana</td>
                        <td class="pronunciation">pork-ka-na</td>
                        <td>carrot</td>
                    </tr>
                    <tr>
                        <td>sipuli</td>
                        <td class="pronunciation">si-pu-li</td>
                        <td>onion</td>
                    </tr>
                </table>
            </div>
            
            <div class="food-category">
                <h4>Meat and Fish</h4>
                <table class="vocabulary-table">
                    <tr>
                        <th>Finnish</th>
                        <th>Pronunciation</th>
                        <th>English</th>
                    </tr>
                    <tr>
                        <td>liha</td>
                        <td class="pronunciation">li-ha</td>
                        <td>meat</td>
                    </tr>
                    <tr>
                        <td>kala</td>
                        <td class="pronunciation">ka-la</td>
                        <td>fish</td>
                    </tr>
                    <tr>
                        <td>naudanliha</td>
                        <td class="pronunciation">nau-dan-li-ha</td>
                        <td>beef</td>
                    </tr>
                    <tr>
                        <td>sianliha</td>
                        <td class="pronunciation">si-an-li-ha</td>
                        <td>pork</td>
                    </tr>
                    <tr>
                        <td>kana</td>
                        <td class="pronunciation">ka-na</td>
                        <td>chicken</td>
                    </tr>
                    <tr>
                        <td>lammas</td>
                        <td class="pronunciation">lam-mas</td>
                        <td>lamb</td>
                    </tr>
                    <tr>
                        <td>lohi</td>
                        <td class="pronunciation">lo-hi</td>
                        <td>salmon</td>
                    </tr>
                    <tr>
                        <td>silakka</td>
                        <td class="pronunciation">si-lak-ka</td>
                        <td>Baltic herring</td>
                    </tr>
                    <tr>
                        <td>makkara</td>
                        <td class="pronunciation">mak-ka-ra</td>
                        <td>sausage</td>
                    </tr>
                    <tr>
                        <td>pekoni</td>
                        <td class="pronunciation">pe-ko-ni</td>
                        <td>bacon</td>
                    </tr>
                </table>
            </div>
            
            <div class="food-category">
                <h4>Dairy and Eggs</h4>
                <table class="vocabulary-table">
                    <tr>
                        <th>Finnish</th>
                        <th>Pronunciation</th>
                        <th>English</th>
                    </tr>
                    <tr>
                        <td>maito</td>
                        <td class="pronunciation">mai-to</td>
                        <td>milk</td>
                    </tr>
                    <tr>
                        <td>juusto</td>
                        <td class="pronunciation">juus-to</td>
                        <td>cheese</td>
                    </tr>
                    <tr>
                        <td>jogurtti</td>
                        <td class="pronunciation">jo-gurt-ti</td>
                        <td>yogurt</td>
                    </tr>
                    <tr>
                        <td>voi</td>
                        <td class="pronunciation">voi</td>
                        <td>butter</td>
                    </tr>
                    <tr>
                        <td>kerma</td>
                        <td class="pronunciation">ker-ma</td>
                        <td>cream</td>
                    </tr>
                    <tr>
                        <td>muna</td>
                        <td class="pronunciation">mu-na</td>
                        <td>egg</td>
                    </tr>
                </table>
            </div>
            
            <div class="food-category">
                <h4>Grains and Bread</h4>
                <table class="vocabulary-table">
                    <tr>
                        <th>Finnish</th>
                        <th>Pronunciation</th>
                        <th>English</th>
                    </tr>
                    <tr>
                        <td>leipä</td>
                        <td class="pronunciation">lei-pä</td>
                        <td>bread</td>
                    </tr>
                    <tr>
                        <td>ruisleipä</td>
                        <td class="pronunciation">ruis-lei-pä</td>
                        <td>rye bread</td>
                    </tr>
                    <tr>
                        <td>pulla</td>
                        <td class="pronunciation">pul-la</td>
                        <td>sweet bun</td>
                    </tr>
                    <tr>
                        <td>riisi</td>
                        <td class="pronunciation">rii-si</td>
                        <td>rice</td>
                    </tr>
                    <tr>
                        <td>pasta</td>
                        <td class="pronunciation">pas-ta</td>
                        <td>pasta</td>
                    </tr>
                    <tr>
                        <td>kaurapuuro</td>
                        <td class="pronunciation">kau-ra-puu-ro</td>
                        <td>oatmeal porridge</td>
                    </tr>
                    <tr>
                        <td>mysli</td>
                        <td class="pronunciation">mys-li</td>
                        <td>muesli</td>
                    </tr>
                </table>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Drinks</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>vesi</td>
                    <td class="pronunciation">ve-si</td>
                    <td>water</td>
                </tr>
                <tr>
                    <td>kahvi</td>
                    <td class="pronunciation">kah-vi</td>
                    <td>coffee</td>
                </tr>
                <tr>
                    <td>tee</td>
                    <td class="pronunciation">tee</td>
                    <td>tea</td>
                </tr>
                <tr>
                    <td>mehu</td>
                    <td class="pronunciation">me-hu</td>
                    <td>juice</td>
                </tr>
                <tr>
                    <td>maito</td>
                    <td class="pronunciation">mai-to</td>
                    <td>milk</td>
                </tr>
                <tr>
                    <td>olut</td>
                    <td class="pronunciation">o-lut</td>
                    <td>beer</td>
                </tr>
                <tr>
                    <td>viini</td>
                    <td class="pronunciation">vii-ni</td>
                    <td>wine</td>
                </tr>
                <tr>
                    <td>punaviini</td>
                    <td class="pronunciation">pu-na-vii-ni</td>
                    <td>red wine</td>
                </tr>
                <tr>
                    <td>valkoviini</td>
                    <td class="pronunciation">val-ko-vii-ni</td>
                    <td>white wine</td>
                </tr>
                <tr>
                    <td>limonadi</td>
                    <td class="pronunciation">li-mo-na-di</td>
                    <td>soda, soft drink</td>
                </tr>
                <tr>
                    <td>kivennäisvesi</td>
                    <td class="pronunciation">ki-ven-näis-ve-si</td>
                    <td>mineral water</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Haluaisin lasin vettä, kiitos.</p>
                <p><strong>English:</strong> I would like a glass of water, please.</p>
                <p><strong>Finnish:</strong> Otatko kahvia vai teetä?</p>
                <p><strong>English:</strong> Would you like coffee or tea?</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Traditional Finnish Foods</h3>
            
            <div class="food-grid">
                <div class="food-card">
                    <h4>Karjalanpiirakka</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">kar-ja-lan-pii-rak-ka</span></p>
                    <p>Karelian pie - a traditional pastry with rye crust and rice porridge filling</p>
                </div>
                
                <div class="food-card">
                    <h4>Ruisleipä</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">ruis-lei-pä</span></p>
                    <p>Rye bread - a staple in Finnish cuisine, dark and dense</p>
                </div>
                
                <div class="food-card">
                    <h4>Lohikeitto</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">lo-hi-keit-to</span></p>
                    <p>Salmon soup - creamy soup with salmon, potatoes, and dill</p>
                </div>
                
                <div class="food-card">
                    <h4>Hernekeitto</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">her-ne-keit-to</span></p>
                    <p>Pea soup - traditionally eaten on Thursdays, often with mustard</p>
                </div>
                
                <div class="food-card">
                    <h4>Korvapuusti</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">kor-va-puus-ti</span></p>
                    <p>Cinnamon roll - Finnish-style with cardamom and pearl sugar</p>
                </div>
                
                <div class="food-card">
                    <h4>Mustikkapiirakka</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">mus-tik-ka-pii-rak-ka</span></p>
                    <p>Blueberry pie - made with wild Finnish blueberries</p>
                </div>
                
                <div class="food-card">
                    <h4>Kalakukko</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">ka-la-kuk-ko</span></p>
                    <p>Fish pie - traditional dish with fish baked inside rye bread</p>
                </div>
                
                <div class="food-card">
                    <h4>Mämmi</h4>
                    <p><strong>Pronunciation:</strong> <span class="pronunciation">mäm-mi</span></p>
                    <p>Traditional Easter dessert made from rye flour and malted rye</p>
                </div>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> Finnish cuisine is characterized by simple, fresh ingredients and is heavily influenced by the country's forests, lakes, and seasons. Berries, mushrooms, fish, and game are important elements of traditional Finnish food.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Useful Phrases for Restaurants</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Pöytä kahdelle, kiitos.</td>
                    <td class="pronunciation">pöy-tä kah-del-le, kii-tos</td>
                    <td>A table for two, please.</td>
                </tr>
                <tr>
                    <td>Onko teillä pöytävarausta?</td>
                    <td class="pronunciation">on-ko teil-lä pöy-tä-va-ra-us-ta</td>
                    <td>Do you have a reservation?</td>
                </tr>
                <tr>
                    <td>Saisinko ruokalistan?</td>
                    <td class="pronunciation">sai-sin-ko ruo-ka-lis-tan</td>
                    <td>Could I have the menu?</td>
                </tr>
                <tr>
                    <td>Mitä suosittelette?</td>
                    <td class="pronunciation">mi-tä suo-sit-te-let-te</td>
                    <td>What do you recommend?</td>
                </tr>
                <tr>
                    <td>Olen kasvissyöjä.</td>
                    <td class="pronunciation">o-len kas-vis-syö-jä</td>
                    <td>I am a vegetarian.</td>
                </tr>
                <tr>
                    <td>Minulla on ruoka-allergia.</td>
                    <td class="pronunciation">mi-nul-la on ruo-ka-al-ler-gi-a</td>
                    <td>I have a food allergy.</td>
                </tr>
                <tr>
                    <td>Haluaisin tilata.</td>
                    <td class="pronunciation">ha-lu-ai-sin ti-la-ta</td>
                    <td>I would like to order.</td>
                </tr>
                <tr>
                    <td>Saisinko laskun, kiitos?</td>
                    <td class="pronunciation">sai-sin-ko las-kun, kii-tos</td>
                    <td>Could I have the bill, please?</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>At a restaurant:</strong></p>
                <p><strong>A:</strong> Tervetuloa! Onko teillä pöytävarausta?</p>
                <p><strong>B:</strong> Ei ole. Onko teillä vapaata pöytää kahdelle?</p>
                <p><strong>A:</strong> Kyllä, tätä kautta. Tässä on ruokalista.</p>
                <p><strong>B:</strong> Kiitos. Mitä suosittelette tänään?</p>
                <p><strong>A:</strong> Lohi on erittäin hyvää tänään.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Welcome! Do you have a reservation?</p>
                <p><strong>B:</strong> No. Do you have a free table for two?</p>
                <p><strong>A:</strong> Yes, this way. Here is the menu.</p>
                <p><strong>B:</strong> Thank you. What do you recommend today?</p>
                <p><strong>A:</strong> The salmon is very good today.</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















