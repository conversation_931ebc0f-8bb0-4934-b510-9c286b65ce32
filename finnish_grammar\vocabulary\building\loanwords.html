﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loanwords in Finnish - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .language-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            margin-right: 5px;
            color: white;
        }
        
        .tag-swedish {
            background-color: #006aa7;
        }
        
        .tag-russian {
            background-color: #d52b1e;
        }
        
        .tag-german {
            background-color: #dd0000;
        }
        
        .tag-english {
            background-color: #00247d;
        }
        
        .tag-french {
            background-color: #002654;
        }
        
        .tag-latin {
            background-color: #8b4513;
        }
        
        .tag-greek {
            background-color: #0d5eaf;
        }
        
        .tag-indo-european {
            background-color: #4b0082;
        }
        
        .tag-other {
            background-color: #555555;
        }
        
        .filter-container {
            margin: 15px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-button {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 5px 15px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .filter-button:hover, .filter-button.active {
            background-color: #0066cc;
            color: white;
            border-color: #0066cc;
        }
        
        .timeline {
            position: relative;
            max-width: 1000px;
            margin: 30px auto;
        }
        
        .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background-color: #0066cc;
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -3px;
        }
        
        .timeline-container {
            padding: 10px 40px;
            position: relative;
            background-color: inherit;
            width: 50%;
        }
        
        .timeline-container::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            right: -10px;
            background-color: white;
            border: 4px solid #0066cc;
            top: 15px;
            border-radius: 50%;
            z-index: 1;
        }
        
        .left {
            left: 0;
        }
        
        .right {
            left: 50%;
        }
        
        .left::before {
            content: " ";
            height: 0;
            position: absolute;
            top: 22px;
            width: 0;
            z-index: 1;
            right: 30px;
            border: medium solid #f5f5f5;
            border-width: 10px 0 10px 10px;
            border-color: transparent transparent transparent #f5f5f5;
        }
        
        .right::before {
            content: " ";
            height: 0;
            position: absolute;
            top: 22px;
            width: 0;
            z-index: 1;
            left: 30px;
            border: medium solid #f5f5f5;
            border-width: 10px 10px 10px 0;
            border-color: transparent #f5f5f5 transparent transparent;
        }
        
        .right::after {
            left: -10px;
        }
        
        .timeline-content {
            padding: 20px;
            background-color: #f5f5f5;
            position: relative;
            border-radius: 6px;
        }
        
        .timeline-content h4 {
            margin-top: 0;
            color: #0066cc;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .filter-button {
            background-color: #333;
            border-color: #444;
        }
        
        [data-theme="dark"] .filter-button:hover, [data-theme="dark"] .filter-button.active {
            background-color: #0066cc;
            border-color: #0066cc;
        }
        
        [data-theme="dark"] .timeline-container::after {
            background-color: #333;
        }
        
        [data-theme="dark"] .timeline-content {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .left::before {
            border-color: transparent transparent transparent #2a2a2a;
        }
        
        [data-theme="dark"] .right::before {
            border-color: transparent #2a2a2a transparent transparent;
        }
        
        /* Responsive timeline */
        @media screen and (max-width: 600px) {
            .timeline::after {
                left: 31px;
            }
            
            .timeline-container {
                width: 100%;
                padding-left: 70px;
                padding-right: 25px;
            }
            
            .timeline-container::before {
                left: 60px;
                border: medium solid #f5f5f5;
                border-width: 10px 10px 10px 0;
                border-color: transparent #f5f5f5 transparent transparent;
            }
            
            .left::after, .right::after {
                left: 15px;
            }
            
            .right {
                left: 0%;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="loanwords-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="loanwords-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="loanwords-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../index.html">Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Loanwords</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Loanwords in Finnish</h2>
            <p>Like all languages, Finnish has borrowed words from other languages throughout its history. This page explores the various loanwords in Finnish, their origins, and how they've been adapted to fit Finnish phonology and orthography. Understanding loanwords can help you recognize familiar patterns and expand your vocabulary more easily.</p>
            
            <div class="filter-container">
                <button class="filter-button active" data-filter="all">All Languages</button>
                <button class="filter-button" data-filter="swedish">Swedish</button>
                <button class="filter-button" data-filter="russian">Russian</button>
                <button class="filter-button" data-filter="german">German</button>
                <button class="filter-button" data-filter="english">English</button>
                <button class="filter-button" data-filter="french">French</button>
                <button class="filter-button" data-filter="other">Other</button>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Historical Timeline of Loanwords in Finnish</h3>
            
            <div class="timeline">
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h4>Proto-Indo-European Loanwords (3000-2000 BCE)</h4>
                        <p>The earliest loanwords in Finnish came from Proto-Indo-European languages. These include basic terms like:</p>
                        <p><span class="language-tag tag-indo-european">Indo-European</span> <strong>nimi</strong> (name), <strong>vesi</strong> (water), <strong>mesi</strong> (honey)</p>
                    </div>
                </div>
                <div class="timeline-container right">
                    <div class="timeline-content">
                        <h4>Baltic Loanwords (1500-500 BCE)</h4>
                        <p>Many agricultural and cultural terms came from Baltic languages:</p>
                        <p><span class="language-tag tag-other">Baltic</span> <strong>heinä</strong> (hay), <strong>hammas</strong> (tooth), <strong>sisar</strong> (sister), <strong>kirves</strong> (axe)</p>
                    </div>
                </div>
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h4>Germanic Loanwords (500 BCE-500 CE)</h4>
                        <p>Early Germanic influences brought words related to trade and technology:</p>
                        <p><span class="language-tag tag-german">Germanic</span> <strong>kuningas</strong> (king), <strong>rengas</strong> (ring), <strong>kulta</strong> (gold), <strong>ranta</strong> (shore)</p>
                    </div>
                </div>
                <div class="timeline-container right">
                    <div class="timeline-content">
                        <h4>Slavic Loanwords (500-1000 CE)</h4>
                        <p>Eastern influences brought words like:</p>
                        <p><span class="language-tag tag-russian">Slavic</span> <strong>risti</strong> (cross), <strong>pappi</strong> (priest), <strong>tavara</strong> (goods)</p>
                    </div>
                </div>
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h4>Swedish Loanwords (1100-1900 CE)</h4>
                        <p>During the Swedish rule of Finland (1249-1809), many administrative, legal, and cultural terms entered Finnish:</p>
                        <p><span class="language-tag tag-swedish">Swedish</span> <strong>tuoli</strong> (chair), <strong>koulu</strong> (school), <strong>laki</strong> (law)</p>
                    </div>
                </div>
                <div class="timeline-container right">
                    <div class="timeline-content">
                        <h4>Russian Loanwords (1809-1917)</h4>
                        <p>During the Russian rule (1809-1917), some Russian words entered Finnish:</p>
                        <p><span class="language-tag tag-russian">Russian</span> <strong>sapuska</strong> (food), <strong>toveri</strong> (comrade), <strong>porukka</strong> (group)</p>
                    </div>
                </div>
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h4>Modern Loanwords (1900-Present)</h4>
                        <p>In modern times, English has become the main source of new loanwords, especially in technology, entertainment, and business:</p>
                        <p><span class="language-tag tag-english">English</span> <strong>netti</strong> (internet), <strong>tiimi</strong> (team), <strong>bisnes</strong> (business)</p>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Swedish Loanwords</h3>
            <p>Due to the long period of Swedish rule in Finland (1249-1809), Swedish has contributed the largest number of loanwords to Finnish. These words cover many aspects of daily life, administration, and culture.</p>
            
            <table class="vocabulary-table" id="loanwords-table">
                <tr>
                    <th>Finnish</th>
                    <th>Original Swedish</th>
                    <th>English</th>
                    <th>Category</th>
                </tr>
                <tr class="swedish-word">
                    <td>tuoli</td>
                    <td>stol</td>
                    <td>chair</td>
                    <td>Household</td>
                </tr>
                <tr class="swedish-word">
                    <td>koulu</td>
                    <td>skola</td>
                    <td>school</td>
                    <td>Education</td>
                </tr>
                <tr class="swedish-word">
                    <td>laki</td>
                    <td>lag</td>
                    <td>law</td>
                    <td>Administration</td>
                </tr>
                <tr class="swedish-word">
                    <td>kaupunki</td>
                    <td>köping</td>
                    <td>city</td>
                    <td>Urban</td>
                </tr>
                <tr class="swedish-word">
                    <td>kahvi</td>
                    <td>kaffe</td>
                    <td>coffee</td>
                    <td>Food & Drink</td>
                </tr>
                <tr class="swedish-word">
                    <td>pankki</td>
                    <td>bank</td>
                    <td>bank</td>
                    <td>Finance</td>
                </tr>
                <tr class="swedish-word">
                    <td>lääkäri</td>
                    <td>läkare</td>
                    <td>doctor</td>
                    <td>Professions</td>
                </tr>
                <tr class="swedish-word">
                    <td>kruunu</td>
                    <td>krona</td>
                    <td>crown</td>
                    <td>Government</td>
                </tr>
                <tr class="swedish-word">
                    <td>lusikka</td>
                    <td>sked</td>
                    <td>spoon</td>
                    <td>Household</td>
                </tr>
                <tr class="swedish-word">
                    <td>kinkku</td>
                    <td>skinka</td>
                    <td>ham</td>
                    <td>Food & Drink</td>
                </tr>
                <tr class="swedish-word">
                    <td>hattu</td>
                    <td>hatt</td>
                    <td>hat</td>
                    <td>Clothing</td>
                </tr>
                <tr class="swedish-word">
                    <td>tori</td>
                    <td>torg</td>
                    <td>market square</td>
                    <td>Urban</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Russian Loanwords</h3>
            <p>During the period of Russian rule (1809-1917) and due to geographical proximity, Finnish borrowed various words from Russian.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Original Russian</th>
                    <th>English</th>
                    <th>Category</th>
                </tr>
                <tr class="russian-word">
                    <td>tavara</td>
                    <td>товар (tovar)</td>
                    <td>goods, item</td>
                    <td>Commerce</td>
                </tr>
                <tr class="russian-word">
                    <td>sapuska</td>
                    <td>закуска (zakuska)</td>
                    <td>food, snack</td>
                    <td>Food & Drink</td>
                </tr>
                <tr class="russian-word">
                    <td>porukka</td>
                    <td>порука (poruka)</td>
                    <td>group, gang</td>
                    <td>Social</td>
                </tr>
                <tr class="russian-word">
                    <td>toveri</td>
                    <td>товарищ (tovarishch)</td>
                    <td>comrade</td>
                    <td>Social</td>
                </tr>
                <tr class="russian-word">
                    <td>kapakka</td>
                    <td>кабак (kabak)</td>
                    <td>pub, tavern</td>
                    <td>Food & Drink</td>
                </tr>
                <tr class="russian-word">
                    <td>pakana</td>
                    <td>поганый (poganyi)</td>
                    <td>pagan</td>
                    <td>Religion</td>
                </tr>
                <tr class="russian-word">
                    <td>pappi</td>
                    <td>поп (pop)</td>
                    <td>priest</td>
                    <td>Religion</td>
                </tr>
                <tr class="russian-word">
                    <td>määrä</td>
                    <td>мера (mera)</td>
                    <td>amount, quantity</td>
                    <td>General</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>German Loanwords</h3>
            <p>German influence came through trade, the Hanseatic League, and later through academic and scientific connections.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Original German</th>
                    <th>English</th>
                    <th>Category</th>
                </tr>
                <tr class="german-word">
                    <td>vahtimestari</td>
                    <td>Wachtmeister</td>
                    <td>janitor, caretaker</td>
                    <td>Professions</td>
                </tr>
                <tr class="german-word">
                    <td>maalari</td>
                    <td>Maler</td>
                    <td>painter</td>
                    <td>Professions</td>
                </tr>
                <tr class="german-word">
                    <td>konsti</td>
                    <td>Kunst</td>
                    <td>trick, art</td>
                    <td>Arts</td>
                </tr>
                <tr class="german-word">
                    <td>peruna</td>
                    <td>Grundbirne</td>
                    <td>potato</td>
                    <td>Food & Drink</td>
                </tr>
                <tr class="german-word">
                    <td>tanssata</td>
                    <td>tanzen</td>
                    <td>to dance</td>
                    <td>Arts</td>
                </tr>
                <tr class="german-word">
                    <td>paraati</td>
                    <td>Parade</td>
                    <td>parade</td>
                    <td>Military</td>
                </tr>
                <tr class="german-word">
                    <td>pormestari</td>
                    <td>Bürgermeister</td>
                    <td>mayor</td>
                    <td>Administration</td>
                </tr>
                <tr class="german-word">
                    <td>lasi</td>
                    <td>Glas</td>
                    <td>glass</td>
                    <td>Household</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>English Loanwords</h3>
            <p>In recent decades, English has become the main source of new loanwords in Finnish, especially in technology, entertainment, business, and youth culture.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Original English</th>
                    <th>Category</th>
                </tr>
                <tr class="english-word">
                    <td>netti</td>
                    <td>internet</td>
                    <td>Technology</td>
                </tr>
                <tr class="english-word">
                    <td>tiimi</td>
                    <td>team</td>
                    <td>Business</td>
                </tr>
                <tr class="english-word">
                    <td>bisnes</td>
                    <td>business</td>
                    <td>Business</td>
                </tr>
                <tr class="english-word">
                    <td>budjetti</td>
                    <td>budget</td>
                    <td>Finance</td>
                </tr>
                <tr class="english-word">
                    <td>fani</td>
                    <td>fan</td>
                    <td>Entertainment</td>
                </tr>
                <tr class="english-word">
                    <td>bändi</td>
                    <td>band</td>
                    <td>Music</td>
                </tr>
                <tr class="english-word">
                    <td>meili</td>
                    <td>mail (email)</td>
                    <td>Technology</td>
                </tr>
                <tr class="english-word">
                    <td>softa</td>
                    <td>software</td>
                    <td>Technology</td>
                </tr>
                <tr class="english-word">
                    <td>chatti</td>
                    <td>chat</td>
                    <td>Technology</td>
                </tr>
                <tr class="english-word">
                    <td>luuseri</td>
                    <td>loser</td>
                    <td>Slang</td>
                </tr>
                <tr class="english-word">
                    <td>pleikkari</td>
                    <td>PlayStation</td>
                    <td>Technology</td>
                </tr>
                <tr class="english-word">
                    <td>fiilis</td>
                    <td>feeling</td>
                    <td>Emotions</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>French Loanwords</h3>
            <p>French has contributed words related to culture, cuisine, and fashion, often via Swedish.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Original French</th>
                    <th>English</th>
                    <th>Category</th>
                </tr>
                <tr class="french-word">
                    <td>parfyymi</td>
                    <td>parfum</td>
                    <td>perfume</td>
                    <td>Fashion</td>
                </tr>
                <tr class="french-word">
                    <td>baletti</td>
                    <td>ballet</td>
                    <td>ballet</td>
                    <td>Arts</td>
                </tr>
                <tr class="french-word">
                    <td>ravintola</td>
                    <td>restaurant</td>
                    <td>restaurant</td>
                    <td>Food & Drink</td>
                </tr>
                <tr class="french-word">
                    <td>hotelli</td>
                    <td>hôtel</td>
                    <td>hotel</td>
                    <td>Travel</td>
                </tr>
                <tr class="french-word">
                    <td>sohva</td>
                    <td>sofa</td>
                    <td>sofa</td>
                    <td>Household</td>
                </tr>
                <tr class="french-word">
                    <td>artisti</td>
                    <td>artiste</td>
                    <td>artist</td>
                    <td>Arts</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Latin and Greek Loanwords</h3>
            <p>Latin and Greek have contributed many scientific, academic, and religious terms, often via other European languages.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Original</th>
                    <th>English</th>
                    <th>Category</th>
                </tr>
                <tr class="latin-word">
                    <td>filosofia</td>
                    <td>philosophia (Latin/Greek)</td>
                    <td>philosophy</td>
                    <td>Academic</td>
                </tr>
                <tr class="latin-word">
                    <td>biologia</td>
                    <td>biologia (Latin/Greek)</td>
                    <td>biology</td>
                    <td>Science</td>
                </tr>
                <tr class="latin-word">
                    <td>matematiikka</td>
                    <td>mathematica (Latin/Greek)</td>
                    <td>mathematics</td>
                    <td>Science</td>
                </tr>
                <tr class="latin-word">
                    <td>kirkko</td>
                    <td>kirkja (from Greek kyriakón)</td>
                    <td>church</td>
                    <td>Religion</td>
                </tr>
                <tr class="greek-word">
                    <td>demokratia</td>
                    <td>demokratia (Greek)</td>
                    <td>democracy</td>
                    <td>Politics</td>
                </tr>
                <tr class="latin-word">
                    <td>museo</td>
                    <td>museum (Latin)</td>
                    <td>museum</td>
                    <td>Culture</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Adaptation of Loanwords in Finnish</h3>
            <p>When foreign words enter Finnish, they typically undergo changes to fit Finnish phonology and orthography. Here are some common patterns:</p>
            
            <div class="example-box">
                <p><strong>1. Consonant clusters are simplified</strong> - Finnish traditionally doesn't allow consonant clusters at the beginning of words:</p>
                <p>Swedish <em>skola</em> → Finnish <em>koulu</em> (school)</p>
                <p>Swedish <em>strand</em> → Finnish <em>ranta</em> (beach)</p>
                
                <p><strong>2. Foreign sounds are replaced with Finnish equivalents</strong>:</p>
                <p>English <em>business</em> [ˈbɪznəs] → Finnish <em>bisnes</em> [ˈbisnes]</p>
                
                <p><strong>3. Word-final consonants often get a vowel added</strong>:</p>
                <p>Swedish <em>buss</em> → Finnish <em>bussi</em> (bus)</p>
                
                <p><strong>4. Double consonants are often used to maintain syllable stress</strong>:</p>
                <p>English <em>coffee</em> → Finnish <em>kahvi</em></p>
                
                <p><strong>5. Vowel harmony is applied</strong> - If a word contains front vowels (ä, ö, y), suffixes will use front vowels:</p>
                <p>Swedish <em>läkare</em> → Finnish <em>lääkäri</em> (doctor)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> Modern loanwords, especially from English, are increasingly being adopted with fewer phonological changes than in the past. For example, many technical terms maintain more of their original pronunciation and spelling.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Recognizing Loanwords: Tips for Learners</h3>
            
            <div class="example-box">
                <p><strong>1. Look for "un-Finnish" features</strong> - Words with b, c, f, q, w, x, z, or certain consonant clusters are likely loanwords.</p>
                <p><strong>2. Recognize common international words</strong> - Terms for modern technology, science, and culture are often similar across many languages.</p>
                <p><strong>3. Notice patterns in specific domains</strong> - Technical, scientific, and academic vocabulary often comes from Latin or Greek roots.</p>
                <p><strong>4. Learn the common adaptations</strong> - Understanding how foreign words are "Finnicized" can help you recognize the original word.</p>
                <p><strong>5. Use cognates to your advantage</strong> - If you know other European languages, you can often guess the meaning of Finnish loanwords.</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















