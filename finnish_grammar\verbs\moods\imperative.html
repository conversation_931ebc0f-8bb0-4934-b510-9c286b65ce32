﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Imperative Mood - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="imperative-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="imperative-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="imperative-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Imperative Mood</span>
        </div>
        
        <section class="grammar-section">
            <h2>Imperative Mood in Finnish</h2>
            <p>The imperative mood (imperatiivi) in Finnish is used to give commands, make requests, or offer invitations. Finnish has different imperative forms for different persons, though the 2nd person singular (sinä) and 2nd person plural (te) forms are the most commonly used.</p>
        </section>
        
        <section class="grammar-section">
            <h3>2nd Person Singular Imperative</h3>
            <p>The 2nd person singular imperative is used when giving a command to one person. It's formed by removing the final -a/-ä from the infinitive form and making any necessary consonant gradation changes.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Infinitive</th>
                    <th>2nd Person Singular Imperative</th>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>puhua (to speak)</td>
                    <td>puhu</td>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>ottaa (to take)</td>
                    <td>ota</td>
                </tr>
                <tr>
                    <td>Type 2</td>
                    <td>syödä (to eat)</td>
                    <td>syö</td>
                </tr>
                <tr>
                    <td>Type 3</td>
                    <td>tulla (to come)</td>
                    <td>tule</td>
                </tr>
                <tr>
                    <td>Type 4</td>
                    <td>haluta (to want)</td>
                    <td>halua</td>
                </tr>
                <tr>
                    <td>Type 5</td>
                    <td>tarvita (to need)</td>
                    <td>tarvitse</td>
                </tr>
                <tr>
                    <td>Type 6</td>
                    <td>vanheta (to grow old)</td>
                    <td>vanhene</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> For Type 3 verbs, the imperative form adds an -e to the stem.</p>
                <p>For verbs with consonant gradation, the imperative form typically uses the strong grade.</p>
            </div>
            
            <h4>Examples</h4>
            <div class="example-box">
                <p><strong>Puhu</strong> hitaammin! (Speak more slowly!)</p>
                <p><strong>Tule</strong> tänne! (Come here!)</p>
                <p><strong>Syö</strong> aamiaisesi. (Eat your breakfast.)</p>
                <p><strong>Ota</strong> tämä. (Take this.)</p>
                <p><strong>Auta</strong> minua. (Help me.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative 2nd Person Singular Imperative</h3>
            <p>The negative 2nd person singular imperative is formed using the negative imperative marker "älä" followed by the basic form of the verb.</p>
            
            <div class="example-box">
                <p><strong>Älä puhu</strong> niin nopeasti! (Don't speak so fast!)</p>
                <p><strong>Älä tule</strong> myöhässä! (Don't come late!)</p>
                <p><strong>Älä syö</strong> niin paljon! (Don't eat so much!)</p>
                <p><strong>Älä ota</strong> sitä! (Don't take it!)</p>
                <p><strong>Älä mene</strong> sinne! (Don't go there!)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>2nd Person Plural Imperative</h3>
            <p>The 2nd person plural imperative is used when giving a command to multiple people. It's formed by adding -kaa/-kää to the verb stem.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Infinitive</th>
                    <th>2nd Person Plural Imperative</th>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>puhua (to speak)</td>
                    <td>puhukaa</td>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>ottaa (to take)</td>
                    <td>ottakaa</td>
                </tr>
                <tr>
                    <td>Type 2</td>
                    <td>syödä (to eat)</td>
                    <td>syökää</td>
                </tr>
                <tr>
                    <td>Type 3</td>
                    <td>tulla (to come)</td>
                    <td>tulkaa</td>
                </tr>
                <tr>
                    <td>Type 4</td>
                    <td>haluta (to want)</td>
                    <td>halatkaa</td>
                </tr>
                <tr>
                    <td>Type 5</td>
                    <td>tarvita (to need)</td>
                    <td>tarvitsekaa</td>
                </tr>
                <tr>
                    <td>Type 6</td>
                    <td>vanheta (to grow old)</td>
                    <td>vanhetkaa</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> The choice between -kaa and -kää follows vowel harmony rules.</p>
            </div>
            
            <h4>Examples</h4>
            <div class="example-box">
                <p><strong>Puhukaa</strong> hitaammin! (Speak more slowly! [to multiple people])</p>
                <p><strong>Tulkaa</strong> tänne! (Come here! [to multiple people])</p>
                <p><strong>Syökää</strong> aamiaisenne. (Eat your breakfast. [to multiple people])</p>
                <p><strong>Ottakaa</strong> nämä. (Take these. [to multiple people])</p>
                <p><strong>Auttakaa</strong> minua. (Help me. [to multiple people])</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative 2nd Person Plural Imperative</h3>
            <p>The negative 2nd person plural imperative is formed using the negative imperative marker "älkää" followed by the basic form of the verb with the ending -ko/-kö.</p>
            
            <div class="example-box">
                <p><strong>Älkää puhuko</strong> niin nopeasti! (Don't speak so fast! [to multiple people])</p>
                <p><strong>Älkää tulko</strong> myöhässä! (Don't come late! [to multiple people])</p>
                <p><strong>Älkää syökö</strong> niin paljon! (Don't eat so much! [to multiple people])</p>
                <p><strong>Älkää ottako</strong> niitä! (Don't take them! [to multiple people])</p>
                <p><strong>Älkää menkö</strong> sinne! (Don't go there! [to multiple people])</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>1st Person Plural Imperative (Hortative)</h3>
            <p>The 1st person plural imperative, also called the hortative, is used to suggest that "we" do something together. It's formed by adding -kaamme/-käämme to the verb stem.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Infinitive</th>
                    <th>1st Person Plural Imperative</th>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>puhua (to speak)</td>
                    <td>puhukaamme</td>
                </tr>
                <tr>
                    <td>Type 2</td>
                    <td>syödä (to eat)</td>
                    <td>syökäämme</td>
                </tr>
                <tr>
                    <td>Type 3</td>
                    <td>tulla (to come)</td>
                    <td>tulkaamme</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In modern spoken Finnish, this form is often replaced by the passive form or other constructions.</p>
            </div>
            
            <h4>Examples</h4>
            <div class="example-box">
                <p><strong>Menkäämme</strong> kotiin. (Let's go home.)</p>
                <p><strong>Syökäämme</strong> yhdessä. (Let's eat together.)</p>
                <p><strong>Puhukaamme</strong> suomea. (Let's speak Finnish.)</p>
            </div>
            
            <h4>Alternative Forms in Spoken Finnish</h4>
            <div class="example-box">
                <p><strong>Mennään</strong> kotiin. (Let's go home. [passive form])</p>
                <p><strong>Syödään</strong> yhdessä. (Let's eat together. [passive form])</p>
                <p><strong>Puhutaan</strong> suomea. (Let's speak Finnish. [passive form])</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>3rd Person Imperative</h3>
            <p>The 3rd person imperative is used to express that someone else should do something. It's less common in everyday speech but appears in formal contexts.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>3rd person singular</td>
                    <td>-koon/-köön</td>
                    <td>puhukoon</td>
                </tr>
                <tr>
                    <td>3rd person plural</td>
                    <td>-koot/-kööt</td>
                    <td>puhukoot</td>
                </tr>
            </table>
            
            <h4>Examples</h4>
            <div class="example-box">
                <p>Hän <strong>tulkoon</strong> sisään. (Let him/her come in.)</p>
                <p>He <strong>menkööt</strong> pois. (Let them go away.)</p>
                <p>Jokainen <strong>tehköön</strong> parhaansa. (Let everyone do their best.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Polite Requests</h3>
            <p>In Finnish, polite requests are often formed using the conditional mood rather than the imperative, especially in formal situations.</p>
            
            <div class="example-box">
                <p><strong>Voisitko</strong> auttaa minua? (Could you help me?)</p>
                <p><strong>Antaisitko</strong> suolan? (Would you pass the salt?)</p>
                <p><strong>Tulisitteko</strong> tänne? (Would you come here? [to multiple people or formal])</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Common Imperative Expressions</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Tule tänne!</td>
                    <td>Come here!</td>
                </tr>
                <tr>
                    <td>Mene pois!</td>
                    <td>Go away!</td>
                </tr>
                <tr>
                    <td>Odota hetki!</td>
                    <td>Wait a moment!</td>
                </tr>
                <tr>
                    <td>Katso tätä!</td>
                    <td>Look at this!</td>
                </tr>
                <tr>
                    <td>Kuuntele minua!</td>
                    <td>Listen to me!</td>
                </tr>
                <tr>
                    <td>Istu alas!</td>
                    <td>Sit down!</td>
                </tr>
                <tr>
                    <td>Älä huoli!</td>
                    <td>Don't worry!</td>
                </tr>
                <tr>
                    <td>Ole hyvä!</td>
                    <td>Please! / Here you are!</td>
                </tr>
                <tr>
                    <td>Anteeksi, puhukaa hitaammin!</td>
                    <td>Excuse me, speak more slowly! [to multiple people]</td>
                </tr>
                <tr>
                    <td>Mennään!</td>
                    <td>Let's go!</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Type</th>
                </tr>
                <tr>
                    <td>Puhu hitaammin!</td>
                    <td>Speak more slowly!</td>
                    <td>2nd person singular</td>
                </tr>
                <tr>
                    <td>Älä mene sinne!</td>
                    <td>Don't go there!</td>
                    <td>Negative 2nd person singular</td>
                </tr>
                <tr>
                    <td>Tulkaa sisään!</td>
                    <td>Come in! [to multiple people]</td>
                    <td>2nd person plural</td>
                </tr>
                <tr>
                    <td>Älkää unohtako tätä!</td>
                    <td>Don't forget this! [to multiple people]</td>
                    <td>Negative 2nd person plural</td>
                </tr>
                <tr>
                    <td>Syökäämme yhdessä.</td>
                    <td>Let's eat together.</td>
                    <td>1st person plural (formal)</td>
                </tr>
                <tr>
                    <td>Syödään yhdessä.</td>
                    <td>Let's eat together.</td>
                    <td>1st person plural (colloquial)</td>
                </tr>
                <tr>
                    <td>Hän tehköön mitä haluaa.</td>
                    <td>Let him/her do what he/she wants.</td>
                    <td>3rd person singular</td>
                </tr>
                <tr>
                    <td>Voisitko auttaa minua?</td>
                    <td>Could you help me?</td>
                    <td>Polite request (conditional)</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















