<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Colors - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .color-sample {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
            border: 1px solid #ddd;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="colors-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="colors-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="colors-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Colors</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Colors in Finnish</h2>
            <p>Learning color vocabulary is essential for describing the world around you. Finnish has specific words for colors, and like other Finnish nouns, they can be inflected in different cases. This page covers the most common color terms in Finnish, their pronunciation, and how to use them in sentences.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Basic Colors</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Sample</th>
                </tr>
                <tr>
                    <td>punainen</td>
                    <td class="pronunciation">pu-nai-nen</td>
                    <td>red</td>
                    <td><span class="color-sample" style="background-color: #FF0000;"></span></td>
                </tr>
                <tr>
                    <td>sininen</td>
                    <td class="pronunciation">si-ni-nen</td>
                    <td>blue</td>
                    <td><span class="color-sample" style="background-color: #0000FF;"></span></td>
                </tr>
                <tr>
                    <td>keltainen</td>
                    <td class="pronunciation">kel-tai-nen</td>
                    <td>yellow</td>
                    <td><span class="color-sample" style="background-color: #FFFF00;"></span></td>
                </tr>
                <tr>
                    <td>vihreä</td>
                    <td class="pronunciation">vih-re-ä</td>
                    <td>green</td>
                    <td><span class="color-sample" style="background-color: #00FF00;"></span></td>
                </tr>
                <tr>
                    <td>musta</td>
                    <td class="pronunciation">mus-ta</td>
                    <td>black</td>
                    <td><span class="color-sample" style="background-color: #000000;"></span></td>
                </tr>
                <tr>
                    <td>valkoinen</td>
                    <td class="pronunciation">val-koi-nen</td>
                    <td>white</td>
                    <td><span class="color-sample" style="background-color: #FFFFFF;"></span></td>
                </tr>
                <tr>
                    <td>harmaa</td>
                    <td class="pronunciation">har-maa</td>
                    <td>gray</td>
                    <td><span class="color-sample" style="background-color: #808080;"></span></td>
                </tr>
                <tr>
                    <td>ruskea</td>
                    <td class="pronunciation">rus-ke-a</td>
                    <td>brown</td>
                    <td><span class="color-sample" style="background-color: #A52A2A;"></span></td>
                </tr>
                <tr>
                    <td>oranssi</td>
                    <td class="pronunciation">o-rans-si</td>
                    <td>orange</td>
                    <td><span class="color-sample" style="background-color: #FFA500;"></span></td>
                </tr>
                <tr>
                    <td>violetti</td>
                    <td class="pronunciation">vio-let-ti</td>
                    <td>purple</td>
                    <td><span class="color-sample" style="background-color: #800080;"></span></td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Minulla on punainen auto.</p>
                <p><strong>English:</strong> I have a red car.</p>
                <p><strong>Finnish:</strong> Taivas on sininen.</p>
                <p><strong>English:</strong> The sky is blue.</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> Many color words in Finnish end with "-inen" (punainen, sininen, keltainen), which is an adjective ending. These words change form when used in different grammatical cases.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Additional Colors and Shades</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Sample</th>
                </tr>
                <tr>
                    <td>pinkki</td>
                    <td class="pronunciation">pink-ki</td>
                    <td>pink</td>
                    <td><span class="color-sample" style="background-color: #FFC0CB;"></span></td>
                </tr>
                <tr>
                    <td>turkoosi</td>
                    <td class="pronunciation">tur-koo-si</td>
                    <td>turquoise</td>
                    <td><span class="color-sample" style="background-color: #40E0D0;"></span></td>
                </tr>
                <tr>
                    <td>beige</td>
                    <td class="pronunciation">beizh</td>
                    <td>beige</td>
                    <td><span class="color-sample" style="background-color: #F5F5DC;"></span></td>
                </tr>
                <tr>
                    <td>kulta</td>
                    <td class="pronunciation">kul-ta</td>
                    <td>gold</td>
                    <td><span class="color-sample" style="background-color: #FFD700;"></span></td>
                </tr>
                <tr>
                    <td>hopea</td>
                    <td class="pronunciation">ho-pe-a</td>
                    <td>silver</td>
                    <td><span class="color-sample" style="background-color: #C0C0C0;"></span></td>
                </tr>
                <tr>
                    <td>vaaleanpunainen</td>
                    <td class="pronunciation">vaa-le-an-pu-nai-nen</td>
                    <td>light red/pink</td>
                    <td><span class="color-sample" style="background-color: #FFB6C1;"></span></td>
                </tr>
                <tr>
                    <td>vaaleansininen</td>
                    <td class="pronunciation">vaa-le-an-si-ni-nen</td>
                    <td>light blue</td>
                    <td><span class="color-sample" style="background-color: #ADD8E6;"></span></td>
                </tr>
                <tr>
                    <td>tummansininen</td>
                    <td class="pronunciation">tum-man-si-ni-nen</td>
                    <td>dark blue</td>
                    <td><span class="color-sample" style="background-color: #00008B;"></span></td>
                </tr>
                <tr>
                    <td>tummanvihreä</td>
                    <td class="pronunciation">tum-man-vih-re-ä</td>
                    <td>dark green</td>
                    <td><span class="color-sample" style="background-color: #006400;"></span></td>
                </tr>
                <tr>
                    <td>purppura</td>
                    <td class="pronunciation">purp-pu-ra</td>
                    <td>purple/magenta</td>
                    <td><span class="color-sample" style="background-color: #800080;"></span></td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Using Colors in Sentences</h3>
            
            <p>In Finnish, adjectives (including colors) agree with the noun they modify in case and number. Here are some examples of how to use colors in different contexts:</p>
            
            <div class="example-box">
                <p><strong>Basic form:</strong> Punainen talo (A red house)</p>
                <p><strong>Genitive:</strong> Punaisen talon katto (The roof of the red house)</p>
                <p><strong>Partitive:</strong> Pidän punaisesta talosta (I like the red house)</p>
                <p><strong>Inessive:</strong> Punaisessa talossa (In the red house)</p>
            </div>
            
            <p>You can also create compound colors by combining words:</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>sinivihreä</td>
                    <td class="pronunciation">si-ni-vih-re-ä</td>
                    <td>blue-green</td>
                </tr>
                <tr>
                    <td>punamusta</td>
                    <td class="pronunciation">pu-na-mus-ta</td>
                    <td>red-black</td>
                </tr>
                <tr>
                    <td>keltapunainen</td>
                    <td class="pronunciation">kel-ta-pu-nai-nen</td>
                    <td>yellow-red</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> To describe light or dark shades, use "vaalea" (light) or "tumma" (dark) as a prefix: vaaleansininen (light blue), tummanpunainen (dark red).</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Common Expressions with Colors</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Literal Meaning</th>
                </tr>
                <tr>
                    <td>nähdä punaista</td>
                    <td>to be furious</td>
                    <td>to see red</td>
                </tr>
                <tr>
                    <td>musta lammas</td>
                    <td>black sheep (outcast)</td>
                    <td>black sheep</td>
                </tr>
                <tr>
                    <td>vihreä kortti</td>
                    <td>green card (residence permit)</td>
                    <td>green card</td>
                </tr>
                <tr>
                    <td>sininen hetki</td>
                    <td>the blue moment (twilight in winter)</td>
                    <td>blue moment</td>
                </tr>
                <tr>
                    <td>kultatukka</td>
                    <td>blonde (hair)</td>
                    <td>gold hair</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









