<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Words in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Question Words</span>
        </div>
        
        <section class="grammar-section">
            <h2>Question Words in Finnish</h2>
            <p>Finnish uses a variety of question words (interrogative pronouns and adverbs) to form open-ended questions. These words help to ask for specific information about people, things, places, times, reasons, and more. This page explains the most common Finnish question words and how to use them.</p>
        </section>

        <section class="grammar-category">
            <h3>COMMON QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <p>Here are the most common question words in Finnish:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Finnish</th>
                        <th>English</th>
                        <th>Used to ask about</th>
                    </tr>
                    <tr>
                        <td>kuka, ketkä</td>
                        <td>who</td>
                        <td>people (singular, plural)</td>
                    </tr>
                    <tr>
                        <td>mikä, mitkä</td>
                        <td>what, which</td>
                        <td>things, identification (singular, plural)</td>
                    </tr>
                    <tr>
                        <td>missä</td>
                        <td>where (location)</td>
                        <td>place (static location)</td>
                    </tr>
                    <tr>
                        <td>mihin</td>
                        <td>where (direction)</td>
                        <td>place (movement to)</td>
                    </tr>
                    <tr>
                        <td>mistä</td>
                        <td>where from</td>
                        <td>place (movement from)</td>
                    </tr>
                    <tr>
                        <td>milloin</td>
                        <td>when</td>
                        <td>time</td>
                    </tr>
                    <tr>
                        <td>miksi</td>
                        <td>why</td>
                        <td>reason</td>
                    </tr>
                    <tr>
                        <td>miten</td>
                        <td>how</td>
                        <td>manner</td>
                    </tr>
                    <tr>
                        <td>kuinka</td>
                        <td>how</td>
                        <td>manner, degree</td>
                    </tr>
                    <tr>
                        <td>montako</td>
                        <td>how many</td>
                        <td>quantity (countable)</td>
                    </tr>
                    <tr>
                        <td>paljonko</td>
                        <td>how much</td>
                        <td>quantity (uncountable)</td>
                    </tr>
                    <tr>
                        <td>kenen</td>
                        <td>whose</td>
                        <td>possession</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>KUKA (WHO) AND MIKÄ (WHAT)</h3>
            
            <div class="grammar-content">
                <p>"Kuka" (who) and "mikä" (what) are the most basic question words in Finnish. They can be inflected in all cases:</p>
                
                <h4>1. Kuka (who)</h4>
                <p>Used to ask about people:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Kuka sinä olet?</span> <span class="english">Who are you?</span></p>
                    <p><span class="finnish">Kuka tulee mukaan?</span> <span class="english">Who is coming along?</span></p>
                </div>
                
                <p>The plural form is "ketkä":</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Ketkä tulevat juhliin?</span> <span class="english">Who (plural) are coming to the party?</span></p>
                </div>
                
                <h4>2. Mikä (what)</h4>
                <p>Used to ask about things or to identify something:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mikä tämä on?</span> <span class="english">What is this?</span></p>
                    <p><span class="finnish">Mikä sinun nimesi on?</span> <span class="english">What is your name?</span></p>
                </div>
                
                <p>The plural form is "mitkä":</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mitkä ovat sinun harrastuksesi?</span> <span class="english">What are your hobbies?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CASE FORMS OF QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <p>Finnish question words can be inflected in all cases. Here are some common case forms:</p>
                
                <h4>1. Kuka (who) in different cases</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Form</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>kuka</td>
                        <td><span class="finnish">Kuka soitti?</span> <span class="english">Who called?</span></td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>kenen</td>
                        <td><span class="finnish">Kenen kirja tämä on?</span> <span class="english">Whose book is this?</span></td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>ketä</td>
                        <td><span class="finnish">Ketä sinä odotat?</span> <span class="english">Who are you waiting for?</span></td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>kenessä</td>
                        <td><span class="finnish">Kenessä on flunssa?</span> <span class="english">Who has the flu?</span></td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>kenestä</td>
                        <td><span class="finnish">Kenestä te puhutte?</span> <span class="english">Who are you talking about?</span></td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>keneen</td>
                        <td><span class="finnish">Keneen sinä luotat?</span> <span class="english">Who do you trust?</span></td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>kenellä</td>
                        <td><span class="finnish">Kenellä on auto?</span> <span class="english">Who has a car?</span></td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>keneltä</td>
                        <td><span class="finnish">Keneltä sait kirjeen?</span> <span class="english">From whom did you get the letter?</span></td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>kenelle</td>
                        <td><span class="finnish">Kenelle annoit lahjan?</span> <span class="english">To whom did you give the gift?</span></td>
                    </tr>
                </table>
                
                <h4>2. Mikä (what) in different cases</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Form</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>mikä</td>
                        <td><span class="finnish">Mikä tämä on?</span> <span class="english">What is this?</span></td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>minkä</td>
                        <td><span class="finnish">Minkä kirjan ostit?</span> <span class="english">Which book did you buy?</span></td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>mitä</td>
                        <td><span class="finnish">Mitä sinä teet?</span> <span class="english">What are you doing?</span></td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>missä</td>
                        <td><span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>mistä</td>
                        <td><span class="finnish">Mistä sinä tulet?</span> <span class="english">Where do you come from?</span></td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>mihin</td>
                        <td><span class="finnish">Mihin sinä menet?</span> <span class="english">Where are you going?</span></td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>millä</td>
                        <td><span class="finnish">Millä sinä matkustat?</span> <span class="english">By what means do you travel?</span></td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>miltä</td>
                        <td><span class="finnish">Miltä ruoka maistuu?</span> <span class="english">How does the food taste?</span></td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>mille</td>
                        <td><span class="finnish">Mille kadulle käännymme?</span> <span class="english">Onto which street do we turn?</span></td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>LOCATION QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <p>Finnish has specific question words for asking about location:</p>
                
                <h4>1. Missä (where - static location)</h4>
                <p>Used to ask about a static location (where something is):</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p><span class="finnish">Missä kirja on?</span> <span class="english">Where is the book?</span></p>
                </div>
                
                <h4>2. Mihin (where to - direction)</h4>
                <p>Used to ask about movement to a location:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mihin sinä menet?</span> <span class="english">Where are you going?</span></p>
                    <p><span class="finnish">Mihin laitoit avaimet?</span> <span class="english">Where did you put the keys?</span></p>
                </div>
                
                <h4>3. Mistä (where from - origin)</h4>
                <p>Used to ask about movement from a location:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mistä sinä tulet?</span> <span class="english">Where do you come from?</span></p>
                    <p><span class="finnish">Mistä löysit sen?</span> <span class="english">Where did you find it?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>TIME AND MANNER QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <h4>1. Milloin (when)</h4>
                <p>Used to ask about time:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Milloin tulet?</span> <span class="english">When are you coming?</span></p>
                    <p><span class="finnish">Milloin elokuva alkaa?</span> <span class="english">When does the movie start?</span></p>
                </div>
                
                <h4>2. Miten and Kuinka (how)</h4>
                <p>Used to ask about manner or method:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Miten pääsen rautatieasemalle?</span> <span class="english">How do I get to the train station?</span></p>
                    <p><span class="finnish">Kuinka vanha sinä olet?</span> <span class="english">How old are you?</span></p>
                </div>
                
                <p>"Miten" and "kuinka" are often interchangeable, but "kuinka" is more commonly used with adjectives to ask about degree:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Kuinka pitkä matka on?</span> <span class="english">How long is the journey?</span></p>
                    <p><span class="finnish">Kuinka paljon tämä maksaa?</span> <span class="english">How much does this cost?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>REASON AND QUANTITY QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <h4>1. Miksi (why)</h4>
                <p>Used to ask about reason:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Miksi olet myöhässä?</span> <span class="english">Why are you late?</span></p>
                    <p><span class="finnish">Miksi et tullut eilen?</span> <span class="english">Why didn't you come yesterday?</span></p>
                </div>
                
                <h4>2. Montako (how many) and Paljonko (how much)</h4>
                <p>"Montako" is used for countable items:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Montako ihmistä tulee?</span> <span class="english">How many people are coming?</span></p>
                    <p><span class="finnish">Montako kirjaa sinulla on?</span> <span class="english">How many books do you have?</span></p>
                </div>
                
                <p>"Paljonko" is used for uncountable quantities or prices:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Paljonko kello on?</span> <span class="english">What time is it? (How much is the clock?)</span></p>
                    <p><span class="finnish">Paljonko tämä maksaa?</span> <span class="english">How much does this cost?</span></p>
                    <p><span class="finnish">Paljonko vettä tarvitset?</span> <span class="english">How much water do you need?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>WORD ORDER IN QUESTIONS WITH QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <p>In Finnish, questions with question words typically follow this word order:</p>
                
                <ol>
                    <li>Question word</li>
                    <li>Verb</li>
                    <li>Subject (if not included in the verb form)</li>
                    <li>Other elements</li>
                </ol>
                
                <div class="grammar-example">
                    <p><span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p><span class="finnish">Mitä hän tekee?</span> <span class="english">What is he/she doing?</span></p>
                    <p><span class="finnish">Milloin elokuva alkaa?</span> <span class="english">When does the movie start?</span></p>
                </div>
                
                <p>However, as with other Finnish sentences, the word order can be varied for emphasis:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Sinä missä asut?</span> <span class="english">YOU where do live? (emphasis on "you")</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









