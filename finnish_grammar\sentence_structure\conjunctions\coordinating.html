﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coordinating Conjunctions in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Coordinating Conjunctions</span>
        </div>
        
        <section class="grammar-section">
            <h2>Coordinating Conjunctions in Finnish</h2>
            <p>Coordinating conjunctions connect words, phrases, or clauses of equal grammatical rank. In Finnish, these conjunctions help create compound sentences by joining independent clauses. This page explains the most common coordinating conjunctions in Finnish and how to use them.</p>
        </section>

        <section class="grammar-category">
            <h3>COMMON COORDINATING CONJUNCTIONS</h3>
            
            <div class="grammar-content">
                <p>Finnish has several coordinating conjunctions that serve different purposes:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Finnish</th>
                        <th>English</th>
                        <th>Function</th>
                    </tr>
                    <tr>
                        <td>ja</td>
                        <td>and</td>
                        <td>Connects items in a positive sense</td>
                    </tr>
                    <tr>
                        <td>sekä</td>
                        <td>and, as well as</td>
                        <td>Similar to "ja" but often used in more formal contexts</td>
                    </tr>
                    <tr>
                        <td>tai</td>
                        <td>or</td>
                        <td>Presents alternatives</td>
                    </tr>
                    <tr>
                        <td>vai</td>
                        <td>or (in questions)</td>
                        <td>Used in questions when asking to choose between alternatives</td>
                    </tr>
                    <tr>
                        <td>mutta</td>
                        <td>but</td>
                        <td>Expresses contrast or exception</td>
                    </tr>
                    <tr>
                        <td>vaan</td>
                        <td>but rather</td>
                        <td>Used after negative statements to introduce a correction</td>
                    </tr>
                    <tr>
                        <td>eli</td>
                        <td>in other words, that is</td>
                        <td>Introduces clarification or explanation</td>
                    </tr>
                    <tr>
                        <td>siis</td>
                        <td>so, therefore</td>
                        <td>Indicates a conclusion or result</td>
                    </tr>
                    <tr>
                        <td>sillä</td>
                        <td>for, because</td>
                        <td>Introduces a reason or explanation</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USING "JA" (AND)</h3>
            
            <div class="grammar-content">
                <p>"Ja" is the most common coordinating conjunction in Finnish, used to connect similar elements:</p>
                
                <h4>1. Connecting nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Matti ja Liisa menevät kauppaan.</span> <span class="english">Matti and Liisa go to the store.</span></p>
                    <p><span class="finnish">Ostin leipää ja maitoa.</span> <span class="english">I bought bread and milk.</span></p>
                </div>
                
                <h4>2. Connecting adjectives</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Talo on suuri ja kaunis.</span> <span class="english">The house is big and beautiful.</span></p>
                    <p><span class="finnish">Hän on älykäs ja hauska.</span> <span class="english">He/she is intelligent and funny.</span></p>
                </div>
                
                <h4>3. Connecting verbs</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Hän lukee ja kirjoittaa.</span> <span class="english">He/she reads and writes.</span></p>
                    <p><span class="finnish">Me syömme ja juomme.</span> <span class="english">We eat and drink.</span></p>
                </div>
                
                <h4>4. Connecting clauses</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen kirjaa ja hän katsoo televisiota.</span> <span class="english">I read a book and he/she watches television.</span></p>
                    <p><span class="finnish">Sataa vettä ja tuulee kovasti.</span> <span class="english">It's raining and the wind is blowing strongly.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USING "TAI" AND "VAI" (OR)</h3>
            
            <div class="grammar-content">
                <p>Finnish has two words for "or" with different uses:</p>
                
                <h4>1. "Tai" - used in statements</h4>
                <p>"Tai" presents alternatives in statements or commands:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Voin tulla tänään tai huomenna.</span> <span class="english">I can come today or tomorrow.</span></p>
                    <p><span class="finnish">Ota kahvia tai teetä.</span> <span class="english">Take coffee or tea.</span></p>
                    <p><span class="finnish">Hän puhuu suomea tai ruotsia.</span> <span class="english">He/she speaks Finnish or Swedish.</span></p>
                </div>
                
                <h4>2. "Vai" - used in questions</h4>
                <p>"Vai" is used in questions when asking to choose between alternatives:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Haluatko kahvia vai teetä?</span> <span class="english">Do you want coffee or tea?</span></p>
                    <p><span class="finnish">Tuletko tänään vai huomenna?</span> <span class="english">Are you coming today or tomorrow?</span></p>
                    <p><span class="finnish">Puhutko suomea vai ruotsia?</span> <span class="english">Do you speak Finnish or Swedish?</span></p>
                </div>
                
                <p>Note: "Vai" is only used in direct questions where a choice is expected. In indirect questions or statements, "tai" is used.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USING "MUTTA" AND "VAAN" (BUT)</h3>
            
            <div class="grammar-content">
                <p>Finnish has two main words for "but" with different uses:</p>
                
                <h4>1. "Mutta" - general contrast</h4>
                <p>"Mutta" expresses a general contrast or exception, similar to English "but":</p>
                <div class="grammar-example">
                    <p><span class="finnish">Hän on nuori, mutta viisas.</span> <span class="english">He/she is young but wise.</span></p>
                    <p><span class="finnish">Haluan tulla, mutta minulla ei ole aikaa.</span> <span class="english">I want to come, but I don't have time.</span></p>
                    <p><span class="finnish">Sataa, mutta menemme silti ulos.</span> <span class="english">It's raining, but we're going out anyway.</span></p>
                </div>
                
                <h4>2. "Vaan" - correction after negation</h4>
                <p>"Vaan" is used after negative statements to introduce a correction or replacement:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Hän ei ole opettaja, vaan lääkäri.</span> <span class="english">He/she is not a teacher, but (rather) a doctor.</span></p>
                    <p><span class="finnish">En puhu ruotsia, vaan suomea.</span> <span class="english">I don't speak Swedish, but (rather) Finnish.</span></p>
                    <p><span class="finnish">Emme mene kotiin, vaan kauppaan.</span> <span class="english">We're not going home, but (rather) to the store.</span></p>
                </div>
                
                <p>The key difference is that "vaan" is used only after negative statements when introducing a replacement or alternative, while "mutta" is used more generally to express contrast.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>OTHER COORDINATING CONJUNCTIONS</h3>
            
            <div class="grammar-content">
                <h4>1. "Eli" (that is, in other words)</h4>
                <p>"Eli" introduces clarification or explanation:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Hän on lingvisti, eli kielitieteilijä.</span> <span class="english">He/she is a linguist, that is, a language scientist.</span></p>
                    <p><span class="finnish">Tulen kello 18, eli kuudelta.</span> <span class="english">I'll come at 18:00, in other words, at six o'clock.</span></p>
                </div>
                
                <h4>2. "Siis" (so, therefore)</h4>
                <p>"Siis" indicates a conclusion or result:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Sataa, siis otan sateenvarjon.</span> <span class="english">It's raining, so I'll take an umbrella.</span></p>
                    <p><span class="finnish">Hän ei vastaa, siis hän on varmaankin kiireinen.</span> <span class="english">He/she doesn't answer, therefore he/she is probably busy.</span></p>
                </div>
                
                <h4>3. "Sillä" (for, because)</h4>
                <p>"Sillä" introduces a reason or explanation:</p>
                <div class="grammar-example">
                    <p><span class="finnish">En tule, sillä olen sairas.</span> <span class="english">I won't come, for I am sick.</span></p>
                    <p><span class="finnish">Hän opiskelee ahkerasti, sillä hän haluaa menestyä.</span> <span class="english">He/she studies diligently, because he/she wants to succeed.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PUNCTUATION WITH COORDINATING CONJUNCTIONS</h3>
            
            <div class="grammar-content">
                <p>In Finnish, the use of commas with coordinating conjunctions follows these general rules:</p>
                
                <h4>1. Between independent clauses</h4>
                <p>A comma is typically used before a coordinating conjunction that connects two independent clauses:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen kirjaa, ja hän katsoo televisiota.</span> <span class="english">I read a book, and he/she watches television.</span></p>
                    <p><span class="finnish">Sataa vettä, mutta menemme silti ulos.</span> <span class="english">It's raining, but we're going out anyway.</span></p>
                </div>
                
                <h4>2. In lists</h4>
                <p>When listing three or more items, commas are used between items, but not usually before the final "ja" or "tai":</p>
                <div class="grammar-example">
                    <p><span class="finnish">Ostin leipää, maitoa ja juustoa.</span> <span class="english">I bought bread, milk and cheese.</span></p>
                    <p><span class="finnish">Voit tulla maanantaina, tiistaina tai keskiviikkona.</span> <span class="english">You can come on Monday, Tuesday or Wednesday.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












