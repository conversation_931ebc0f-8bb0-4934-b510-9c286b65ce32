﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Life - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="daily-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="daily-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="daily-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Daily Life</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Daily Life Vocabulary in Finnish</h2>
            <p>This page covers essential Finnish vocabulary for everyday activities, routines, and common objects you'll encounter in daily life. Learning these words will help you navigate everyday situations in Finland and communicate about your daily activities.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Morning Routine</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>herätä</td>
                    <td class="pronunciation">he-rä-tä</td>
                    <td>to wake up</td>
                </tr>
                <tr>
                    <td>herätyskello</td>
                    <td class="pronunciation">he-rä-tys-kel-lo</td>
                    <td>alarm clock</td>
                </tr>
                <tr>
                    <td>nousta</td>
                    <td class="pronunciation">nous-ta</td>
                    <td>to get up</td>
                </tr>
                <tr>
                    <td>peseytyä</td>
                    <td class="pronunciation">pe-sey-ty-ä</td>
                    <td>to wash oneself</td>
                </tr>
                <tr>
                    <td>suihku</td>
                    <td class="pronunciation">suih-ku</td>
                    <td>shower</td>
                </tr>
                <tr>
                    <td>käydä suihkussa</td>
                    <td class="pronunciation">käy-dä suih-kus-sa</td>
                    <td>to take a shower</td>
                </tr>
                <tr>
                    <td>pestä hampaat</td>
                    <td class="pronunciation">pes-tä ham-paat</td>
                    <td>to brush teeth</td>
                </tr>
                <tr>
                    <td>hammasharja</td>
                    <td class="pronunciation">ham-mas-har-ja</td>
                    <td>toothbrush</td>
                </tr>
                <tr>
                    <td>hammastahna</td>
                    <td class="pronunciation">ham-mas-tah-na</td>
                    <td>toothpaste</td>
                </tr>
                <tr>
                    <td>pukeutua</td>
                    <td class="pronunciation">pu-keu-tu-a</td>
                    <td>to get dressed</td>
                </tr>
                <tr>
                    <td>aamiainen</td>
                    <td class="pronunciation">aa-mi-ai-nen</td>
                    <td>breakfast</td>
                </tr>
                <tr>
                    <td>syödä aamiaista</td>
                    <td class="pronunciation">syö-dä aa-mi-ais-ta</td>
                    <td>to eat breakfast</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Herään yleensä seitsemältä ja käyn suihkussa.</p>
                <p><strong>English:</strong> I usually wake up at seven and take a shower.</p>
                <p><strong>Finnish:</strong> Syön aamiaista ja lähden töihin kahdeksalta.</p>
                <p><strong>English:</strong> I eat breakfast and leave for work at eight.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Home and Household</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>koti</td>
                    <td class="pronunciation">ko-ti</td>
                    <td>home</td>
                </tr>
                <tr>
                    <td>talo</td>
                    <td class="pronunciation">ta-lo</td>
                    <td>house</td>
                </tr>
                <tr>
                    <td>asunto</td>
                    <td class="pronunciation">a-sun-to</td>
                    <td>apartment</td>
                </tr>
                <tr>
                    <td>kerrostalo</td>
                    <td class="pronunciation">ker-ros-ta-lo</td>
                    <td>apartment building</td>
                </tr>
                <tr>
                    <td>omakotitalo</td>
                    <td class="pronunciation">o-ma-ko-ti-ta-lo</td>
                    <td>detached house</td>
                </tr>
                <tr>
                    <td>huone</td>
                    <td class="pronunciation">huo-ne</td>
                    <td>room</td>
                </tr>
                <tr>
                    <td>olohuone</td>
                    <td class="pronunciation">o-lo-huo-ne</td>
                    <td>living room</td>
                </tr>
                <tr>
                    <td>makuuhuone</td>
                    <td class="pronunciation">ma-kuu-huo-ne</td>
                    <td>bedroom</td>
                </tr>
                <tr>
                    <td>keittiö</td>
                    <td class="pronunciation">keit-ti-ö</td>
                    <td>kitchen</td>
                </tr>
                <tr>
                    <td>kylpyhuone</td>
                    <td class="pronunciation">kyl-py-huo-ne</td>
                    <td>bathroom</td>
                </tr>
                <tr>
                    <td>sauna</td>
                    <td class="pronunciation">sau-na</td>
                    <td>sauna</td>
                </tr>
                <tr>
                    <td>parveke</td>
                    <td class="pronunciation">par-ve-ke</td>
                    <td>balcony</td>
                </tr>
                <tr>
                    <td>sänky</td>
                    <td class="pronunciation">sän-ky</td>
                    <td>bed</td>
                </tr>
                <tr>
                    <td>pöytä</td>
                    <td class="pronunciation">pöy-tä</td>
                    <td>table</td>
                </tr>
                <tr>
                    <td>tuoli</td>
                    <td class="pronunciation">tuo-li</td>
                    <td>chair</td>
                </tr>
                <tr>
                    <td>sohva</td>
                    <td class="pronunciation">soh-va</td>
                    <td>sofa</td>
                </tr>
                <tr>
                    <td>jääkaappi</td>
                    <td class="pronunciation">jää-kaap-pi</td>
                    <td>refrigerator</td>
                </tr>
                <tr>
                    <td>liesi</td>
                    <td class="pronunciation">lie-si</td>
                    <td>stove</td>
                </tr>
                <tr>
                    <td>uuni</td>
                    <td class="pronunciation">uu-ni</td>
                    <td>oven</td>
                </tr>
                <tr>
                    <td>pesukone</td>
                    <td class="pronunciation">pe-su-ko-ne</td>
                    <td>washing machine</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finland, most apartments and houses have a sauna. Sauna is an important part of Finnish culture and daily life.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Daily Activities</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>syödä</td>
                    <td class="pronunciation">syö-dä</td>
                    <td>to eat</td>
                </tr>
                <tr>
                    <td>juoda</td>
                    <td class="pronunciation">juo-da</td>
                    <td>to drink</td>
                </tr>
                <tr>
                    <td>nukkua</td>
                    <td class="pronunciation">nuk-ku-a</td>
                    <td>to sleep</td>
                </tr>
                <tr>
                    <td>mennä nukkumaan</td>
                    <td class="pronunciation">men-nä nuk-ku-maan</td>
                    <td>to go to sleep</td>
                </tr>
                <tr>
                    <td>lukea</td>
                    <td class="pronunciation">lu-ke-a</td>
                    <td>to read</td>
                </tr>
                <tr>
                    <td>kirjoittaa</td>
                    <td class="pronunciation">kir-joit-taa</td>
                    <td>to write</td>
                </tr>
                <tr>
                    <td>katsoa televisiota</td>
                    <td class="pronunciation">kat-so-a te-le-vi-si-o-ta</td>
                    <td>to watch television</td>
                </tr>
                <tr>
                    <td>kuunnella musiikkia</td>
                    <td class="pronunciation">kuun-nel-la mu-siik-ki-a</td>
                    <td>to listen to music</td>
                </tr>
                <tr>
                    <td>käydä kävelyllä</td>
                    <td class="pronunciation">käy-dä kä-ve-lyl-lä</td>
                    <td>to go for a walk</td>
                </tr>
                <tr>
                    <td>siivota</td>
                    <td class="pronunciation">sii-vo-ta</td>
                    <td>to clean</td>
                </tr>
                <tr>
                    <td>pestä pyykkiä</td>
                    <td class="pronunciation">pes-tä pyyk-ki-ä</td>
                    <td>to do laundry</td>
                </tr>
                <tr>
                    <td>laittaa ruokaa</td>
                    <td class="pronunciation">lait-taa ruo-kaa</td>
                    <td>to cook</td>
                </tr>
                <tr>
                    <td>käydä kaupassa</td>
                    <td class="pronunciation">käy-dä kau-pas-sa</td>
                    <td>to go shopping</td>
                </tr>
                <tr>
                    <td>käydä töissä</td>
                    <td class="pronunciation">käy-dä töis-sä</td>
                    <td>to go to work</td>
                </tr>
                <tr>
                    <td>opiskella</td>
                    <td class="pronunciation">o-pis-kel-la</td>
                    <td>to study</td>
                </tr>
                <tr>
                    <td>harrastaa liikuntaa</td>
                    <td class="pronunciation">har-ras-taa lii-kun-taa</td>
                    <td>to exercise</td>
                </tr>
                <tr>
                    <td>tavata ystäviä</td>
                    <td class="pronunciation">ta-va-ta ys-tä-vi-ä</td>
                    <td>to meet friends</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Viikonloppuna siivoan kotia, pesen pyykkiä ja käyn kaupassa.</p>
                <p><strong>English:</strong> On the weekend, I clean the house, do laundry, and go shopping.</p>
                <p><strong>Finnish:</strong> Illalla laitan ruokaa ja katson televisiota.</p>
                <p><strong>English:</strong> In the evening, I cook and watch television.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Time Expressions</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>aamu</td>
                    <td class="pronunciation">aa-mu</td>
                    <td>morning</td>
                </tr>
                <tr>
                    <td>aamulla</td>
                    <td class="pronunciation">aa-mul-la</td>
                    <td>in the morning</td>
                </tr>
                <tr>
                    <td>päivä</td>
                    <td class="pronunciation">päi-vä</td>
                    <td>day</td>
                </tr>
                <tr>
                    <td>päivällä</td>
                    <td class="pronunciation">päi-väl-lä</td>
                    <td>during the day</td>
                </tr>
                <tr>
                    <td>ilta</td>
                    <td class="pronunciation">il-ta</td>
                    <td>evening</td>
                </tr>
                <tr>
                    <td>illalla</td>
                    <td class="pronunciation">il-lal-la</td>
                    <td>in the evening</td>
                </tr>
                <tr>
                    <td>yö</td>
                    <td class="pronunciation">yö</td>
                    <td>night</td>
                </tr>
                <tr>
                    <td>yöllä</td>
                    <td class="pronunciation">yöl-lä</td>
                    <td>at night</td>
                </tr>
                <tr>
                    <td>tänään</td>
                    <td class="pronunciation">tä-nään</td>
                    <td>today</td>
                </tr>
                <tr>
                    <td>huomenna</td>
                    <td class="pronunciation">huo-men-na</td>
                    <td>tomorrow</td>
                </tr>
                <tr>
                    <td>eilen</td>
                    <td class="pronunciation">ei-len</td>
                    <td>yesterday</td>
                </tr>
                <tr>
                    <td>viikonloppu</td>
                    <td class="pronunciation">vii-kon-lop-pu</td>
                    <td>weekend</td>
                </tr>
                <tr>
                    <td>arkipäivä</td>
                    <td class="pronunciation">ar-ki-päi-vä</td>
                    <td>weekday</td>
                </tr>
                <tr>
                    <td>joka päivä</td>
                    <td class="pronunciation">jo-ka päi-vä</td>
                    <td>every day</td>
                </tr>
                <tr>
                    <td>usein</td>
                    <td class="pronunciation">u-sein</td>
                    <td>often</td>
                </tr>
                <tr>
                    <td>joskus</td>
                    <td class="pronunciation">jos-kus</td>
                    <td>sometimes</td>
                </tr>
                <tr>
                    <td>harvoin</td>
                    <td class="pronunciation">har-voin</td>
                    <td>rarely</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Personal Items</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>puhelin</td>
                    <td class="pronunciation">pu-he-lin</td>
                    <td>phone</td>
                </tr>
                <tr>
                    <td>älypuhelin</td>
                    <td class="pronunciation">ä-ly-pu-he-lin</td>
                    <td>smartphone</td>
                </tr>
                <tr>
                    <td>tietokone</td>
                    <td class="pronunciation">tie-to-ko-ne</td>
                    <td>computer</td>
                </tr>
                <tr>
                    <td>kannettava tietokone</td>
                    <td class="pronunciation">kan-net-ta-va tie-to-ko-ne</td>
                    <td>laptop</td>
                </tr>
                <tr>
                    <td>lompakko</td>
                    <td class="pronunciation">lom-pak-ko</td>
                    <td>wallet</td>
                </tr>
                <tr>
                    <td>avaimet</td>
                    <td class="pronunciation">a-vai-met</td>
                    <td>keys</td>
                </tr>
                <tr>
                    <td>laukku</td>
                    <td class="pronunciation">lauk-ku</td>
                    <td>bag</td>
                </tr>
                <tr>
                    <td>reppu</td>
                    <td class="pronunciation">rep-pu</td>
                    <td>backpack</td>
                </tr>
                <tr>
                    <td>kello</td>
                    <td class="pronunciation">kel-lo</td>
                    <td>watch, clock</td>
                </tr>
                <tr>
                    <td>silmälasit</td>
                    <td class="pronunciation">sil-mä-la-sit</td>
                    <td>glasses</td>
                </tr>
                <tr>
                    <td>sateenvarjo</td>
                    <td class="pronunciation">sa-teen-var-jo</td>
                    <td>umbrella</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Missä avaimeni ovat? En löydä niitä.</p>
                <p><strong>English:</strong> Where are my keys? I can't find them.</p>
                <p><strong>Finnish:</strong> Tarvitsen puhelimeni ja lompakkoni.</p>
                <p><strong>English:</strong> I need my phone and my wallet.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Common Phrases for Daily Life</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Hyvää huomenta!</td>
                    <td class="pronunciation">hy-vää huo-men-ta</td>
                    <td>Good morning!</td>
                </tr>
                <tr>
                    <td>Hyvää päivää!</td>
                    <td class="pronunciation">hy-vää päi-vää</td>
                    <td>Good day!</td>
                </tr>
                <tr>
                    <td>Hyvää iltaa!</td>
                    <td class="pronunciation">hy-vää il-taa</td>
                    <td>Good evening!</td>
                </tr>
                <tr>
                    <td>Hyvää yötä!</td>
                    <td class="pronunciation">hy-vää yö-tä</td>
                    <td>Good night!</td>
                </tr>
                <tr>
                    <td>Mitä kuuluu?</td>
                    <td class="pronunciation">mi-tä kuu-luu</td>
                    <td>How are you?</td>
                </tr>
                <tr>
                    <td>Kiitos, hyvää.</td>
                    <td class="pronunciation">kii-tos, hy-vää</td>
                    <td>Thanks, good.</td>
                </tr>
                <tr>
                    <td>Entä sinulle?</td>
                    <td class="pronunciation">en-tä si-nul-le</td>
                    <td>And you?</td>
                </tr>
                <tr>
                    <td>Nähdään myöhemmin!</td>
                    <td class="pronunciation">näh-dään myö-hem-min</td>
                    <td>See you later!</td>
                </tr>
                <tr>
                    <td>Hyvää ruokahalua!</td>
                    <td class="pronunciation">hy-vää ruo-ka-ha-lu-a</td>
                    <td>Enjoy your meal!</td>
                </tr>
                <tr>
                    <td>Kippis!</td>
                    <td class="pronunciation">kip-pis</td>
                    <td>Cheers!</td>
                </tr>
                <tr>
                    <td>Onnea!</td>
                    <td class="pronunciation">on-ne-a</td>
                    <td>Good luck!</td>
                </tr>
                <tr>
                    <td>Hyvää viikonloppua!</td>
                    <td class="pronunciation">hy-vää vii-kon-lop-pu-a</td>
                    <td>Have a good weekend!</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















