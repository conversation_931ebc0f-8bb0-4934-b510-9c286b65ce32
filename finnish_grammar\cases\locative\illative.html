﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Illative Case - Finnish Grammar - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Cases</a>
            <span class="separator">></span>
            <span>Illative Case</span>
        </div>
        
        <section class="grammar-section">
            <h2>Illative Case (Illatiivi)</h2>
            <p>The illative case in Finnish is used to express movement into something. It answers the question "to where?" (mihin?) and has several different endings depending on the word.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMATION OF THE ILLATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The illative case is one of the more complex cases to form in Finnish. It has three main endings:</p>
                <ul>
                    <li>-Vn (where V is the last vowel of the word doubled)</li>
                    <li>-hVn (where V is the last vowel of the word)</li>
                    <li>-seen (for words ending in -e)</li>
                </ul>
                
                <p>Examples of words in the illative case:</p>
                <ul>
                    <li>talo (house) → taloon (into the house)</li>
                    <li>kissa (cat) → kissaan (into the cat)</li>
                    <li>pöytä (table) → pöytään (into the table)</li>
                    <li>maa (country) → maahan (into the country)</li>
                    <li>perhe (family) → perheeseen (into the family)</li>
                </ul>
                
                <p>The illative plural is formed by adding -ihin or -iin to the plural stem:</p>
                <ul>
                    <li>talo → taloihin (into the houses)</li>
                    <li>kissa → kissoihin (into the cats)</li>
                    <li>pöytä → pöytiin (into the tables)</li>
                    <li>maa → maihin (into the countries)</li>
                    <li>perhe → perheisiin (into the families)</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Menen taloon.</span> <span class="english">I go into the house.</span></p>
                    <p><span class="finnish">Laitan kirjat laukkuun.</span> <span class="english">I put the books into the bag.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>RULES FOR FORMING THE ILLATIVE</h3>
            
            <div class="grammar-content">
                <p>The rules for forming the illative case are as follows:</p>
                
                <h4>1. Words ending in a vowel (except -e)</h4>
                <p>Double the final vowel and add -n:</p>
                <div class="grammar-example">
                    <p><span class="finnish">talo → taloon</span> <span class="english">house → into the house</span></p>
                    <p><span class="finnish">kissa → kissaan</span> <span class="english">cat → into the cat</span></p>
                    <p><span class="finnish">pöytä → pöytään</span> <span class="english">table → into the table</span></p>
                </div>
                
                <h4>2. Words ending in -e</h4>
                <p>Add -seen:</p>
                <div class="grammar-example">
                    <p><span class="finnish">huone → huoneeseen</span> <span class="english">room → into the room</span></p>
                    <p><span class="finnish">perhe → perheeseen</span> <span class="english">family → into the family</span></p>
                </div>
                
                <h4>3. Words ending in -i with two syllables</h4>
                <p>Change -i to -een:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kivi → kiveen</span> <span class="english">stone → into the stone</span></p>
                    <p><span class="finnish">ovi → oveen</span> <span class="english">door → into the door</span></p>
                </div>
                
                <h4>4. Words with consonant gradation</h4>
                <p>Apply consonant gradation before adding the ending:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kukka → kukkaan</span> <span class="english">flower → into the flower</span></p>
                    <p><span class="finnish">ranta → rantaan</span> <span class="english">shore → into the shore</span></p>
                </div>
                
                <h4>5. Words ending in -as, -äs, -is, -us, -ys, etc.</h4>
                <p>Add -Vkseen (where V is the last vowel):</p>
                <div class="grammar-example">
                    <p><span class="finnish">vastaus → vastaukseen</span> <span class="english">answer → into the answer</span></p>
                    <p><span class="finnish">kysymys → kysymykseen</span> <span class="english">question → into the question</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF THE ILLATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The illative case is used in the following situations:</p>
                
                <h4>1. To express movement into something</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Menen Suomeen.</span> <span class="english">I go to Finland.</span></p>
                    <p><span class="finnish">Laitan kirjan laukkuun.</span> <span class="english">I put the book into the bag.</span></p>
                </div>
                
                <h4>2. To express entering a state or condition</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Menen uneen.</span> <span class="english">I fall asleep (go into sleep).</span></p>
                    <p><span class="finnish">Hän rakastui minuun.</span> <span class="english">He/she fell in love with me.</span></p>
                </div>
                
                <h4>3. With certain verbs that require the illative</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Luotan sinuun.</span> <span class="english">I trust you.</span></p>
                    <p><span class="finnish">Tutustun kaupunkiin.</span> <span class="english">I get to know the city.</span></p>
                </div>
                
                <h4>4. To express the end point of time</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Jään tänne iltaan asti.</span> <span class="english">I'll stay here until evening.</span></p>
                    <p><span class="finnish">Kokous kestää aamusta iltaan.</span> <span class="english">The meeting lasts from morning to evening.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ILLATIVE VS. OTHER LOCATIVE CASES</h3>
            
            <div class="grammar-content">
                <p>The illative case is part of the "internal locative cases" in Finnish, which express relationships with the inside of something:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Ending</th>
                        <th>Meaning</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>-ssa/-ssä</td>
                        <td>In, inside (static)</td>
                        <td>talossa (in the house)</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>-sta/-stä</td>
                        <td>From inside (movement out)</td>
                        <td>talosta (from the house)</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>-Vn, -hVn, -seen</td>
                        <td>Into (movement in)</td>
                        <td>taloon (into the house)</td>
                    </tr>
                </table>
                
                <p>Compare with the "external locative cases" which express relationships with the surface or vicinity of something:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Ending</th>
                        <th>Meaning</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>-lla/-llä</td>
                        <td>On, at (static)</td>
                        <td>pöydällä (on the table)</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>-lta/-ltä</td>
                        <td>From on/at (movement away)</td>
                        <td>pöydältä (from the table)</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>-lle</td>
                        <td>Onto, to (movement to)</td>
                        <td>pöydälle (onto the table)</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Olen talossa.</span> <span class="english">I am in the house. (Inessive - static)</span></p>
                    <p><span class="finnish">Tulen talosta.</span> <span class="english">I come from the house. (Elative - movement out)</span></p>
                    <p><span class="finnish">Menen taloon.</span> <span class="english">I go into the house. (Illative - movement in)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXPRESSIONS</h3>
            
            <div class="grammar-content">
                <p>Some common expressions using the illative case:</p>
                
                <h4>1. Verbs that take the illative</h4>
                <div class="grammar-example">
                    <p><span class="finnish">luottaa johonkin</span> <span class="english">to trust something</span></p>
                    <p><span class="finnish">tutustua johonkin</span> <span class="english">to get to know something</span></p>
                    <p><span class="finnish">rakastua johonkin</span> <span class="english">to fall in love with something</span></p>
                </div>
                
                <h4>2. Expressions of change</h4>
                <div class="grammar-example">
                    <p><span class="finnish">muuttua joksikin</span> <span class="english">to change into something</span></p>
                    <p><span class="finnish">vaihtaa johonkin</span> <span class="english">to change to something</span></p>
                </div>
                
                <h4>3. Fixed expressions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">mennä naimisiin</span> <span class="english">to get married</span></p>
                    <p><span class="finnish">ottaa osaa johonkin</span> <span class="english">to take part in something</span></p>
                    <p><span class="finnish">vastata kysymykseen</span> <span class="english">to answer a question</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















