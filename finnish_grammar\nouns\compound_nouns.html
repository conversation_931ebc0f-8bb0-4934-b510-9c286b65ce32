﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compound Nouns - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../grammar.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../grammar.html">Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="index.html">Nouns</a>
            <span class="separator">></span>
            <span>Compound Nouns</span>
        </div>
        
        <section class="grammar-section">
            <h2>Compound Nouns in Finnish</h2>
            <p>Finnish is known for its long compound words. Compound nouns (yhdyssanat) are formed by combining two or more words into a single unit. This page explains how to form and use compound nouns in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMING COMPOUND NOUNS</h3>
            
            <div class="grammar-content">
                <p>Finnish compound nouns are typically formed by combining two or more words, with the first part usually in the genitive case (without the final -n) and the second part in its basic form:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">kirja (book) + kauppa (shop) → kirjakauppa</span> <span class="english">bookshop</span></p>
                    <p><span class="finnish">koulu (school) + päivä (day) → koulupäivä</span> <span class="english">school day</span></p>
                    <p><span class="finnish">kesä (summer) + loma (vacation) → kesäloma</span> <span class="english">summer vacation</span></p>
                </div>
                
                <p>The main characteristics of Finnish compound nouns are:</p>
                <ul>
                    <li>They are written as one word (no spaces)</li>
                    <li>The first part modifies or specifies the second part</li>
                    <li>The second part determines the grammatical properties of the whole compound</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>TYPES OF COMPOUND NOUNS</h3>
            
            <div class="grammar-content">
                <p>There are several types of compound nouns in Finnish:</p>
                
                <h4>1. Noun + Noun</h4>
                <p>The most common type, where both parts are nouns:</p>
                <div class="grammar-example">
                    <p><span class="finnish">jää (ice) + kaappi (cabinet) → jääkaappi</span> <span class="english">refrigerator</span></p>
                    <p><span class="finnish">pöytä (table) + liina (cloth) → pöytäliina</span> <span class="english">tablecloth</span></p>
                </div>
                
                <h4>2. Adjective + Noun</h4>
                <p>Where the first part is an adjective:</p>
                <div class="grammar-example">
                    <p><span class="finnish">iso (big) + äiti (mother) → isoäiti</span> <span class="english">grandmother</span></p>
                    <p><span class="finnish">uusi (new) + vuosi (year) → uusivuosi</span> <span class="english">New Year</span></p>
                </div>
                
                <h4>3. Verb + Noun</h4>
                <p>Where the first part is derived from a verb:</p>
                <div class="grammar-example">
                    <p><span class="finnish">uida (to swim) + allas (pool) → uima-allas</span> <span class="english">swimming pool</span></p>
                    <p><span class="finnish">lukea (to read) + sali (hall) → lukusali</span> <span class="english">reading room</span></p>
                </div>
                
                <h4>4. Numeral + Noun</h4>
                <p>Where the first part is a numeral:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kolme (three) + pyörä (wheel) → kolmipyörä</span> <span class="english">tricycle</span></p>
                    <p><span class="finnish">viisi (five) + kulma (corner) → viisikulma</span> <span class="english">pentagon</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL FORMS IN COMPOUND NOUNS</h3>
            
            <div class="grammar-content">
                <p>When forming compound nouns, the first part may take special forms:</p>
                
                <h4>1. Genitive stem</h4>
                <p>The most common form is the genitive stem (without the final -n):</p>
                <div class="grammar-example">
                    <p><span class="finnish">lapsi (child) → lapse- + huone (room) → lapsenhuone</span> <span class="english">child's room</span></p>
                    <p><span class="finnish">työ (work) → työ- + paikka (place) → työpaikka</span> <span class="english">workplace</span></p>
                </div>
                
                <h4>2. Nominative form</h4>
                <p>Sometimes the first part appears in its basic form:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kivi (stone) + talo (house) → kivitalo</span> <span class="english">stone house</span></p>
                    <p><span class="finnish">puu (wood) + lelu (toy) → puulelu</span> <span class="english">wooden toy</span></p>
                </div>
                
                <h4>3. Special connecting elements</h4>
                <p>Some compounds use connecting elements:</p>
                <div class="grammar-example">
                    <p><span class="finnish">maanantai (Monday) + ilta (evening) → maanantai-ilta</span> <span class="english">Monday evening</span></p>
                    <p><span class="finnish">uida (to swim) + allas (pool) → uima-allas</span> <span class="english">swimming pool</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>INFLECTING COMPOUND NOUNS</h3>
            
            <div class="grammar-content">
                <p>When inflecting compound nouns, only the last part changes:</p>
                
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">kirjakauppa</span> (bookshop)</p>
                    <p>Genitive: <span class="finnish">kirjakaupan</span> (of the bookshop)</p>
                    <p>Partitive: <span class="finnish">kirjakauppaa</span> (bookshop, partitive)</p>
                    <p>Inessive: <span class="finnish">kirjakaupassa</span> (in the bookshop)</p>
                </div>
                
                <p>The first part remains unchanged throughout all inflections.</p>
                
                <h4>Consonant gradation in compound nouns</h4>
                <p>If the last part of the compound undergoes consonant gradation, it follows the normal rules:</p>
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">työpaikka</span> (workplace)</p>
                    <p>Genitive: <span class="finnish">työpaikan</span> (of the workplace) - note the kk → k change</p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON COMPOUND NOUN PATTERNS</h3>
            
            <div class="grammar-content">
                <p>Finnish has some common patterns for forming compound nouns:</p>
                
                <h4>1. Material compounds</h4>
                <p>Indicating what something is made of:</p>
                <div class="grammar-example">
                    <p><span class="finnish">puu (wood) + talo (house) → puutalo</span> <span class="english">wooden house</span></p>
                    <p><span class="finnish">nahka (leather) + takki (jacket) → nahkatakki</span> <span class="english">leather jacket</span></p>
                </div>
                
                <h4>2. Purpose compounds</h4>
                <p>Indicating what something is used for:</p>
                <div class="grammar-example">
                    <p><span class="finnish">ruoka (food) + pöytä (table) → ruokapöytä</span> <span class="english">dining table</span></p>
                    <p><span class="finnish">kahvi (coffee) + kuppi (cup) → kahvikuppi</span> <span class="english">coffee cup</span></p>
                </div>
                
                <h4>3. Location compounds</h4>
                <p>Indicating where something is located:</p>
                <div class="grammar-example">
                    <p><span class="finnish">meri (sea) + näkymä (view) → merinäkymä</span> <span class="english">sea view</span></p>
                    <p><span class="finnish">katu (street) + kahvila (café) → katukahvila</span> <span class="english">street café</span></p>
                </div>
                
                <h4>4. Time compounds</h4>
                <p>Indicating when something happens:</p>
                <div class="grammar-example">
                    <p><span class="finnish">aamu (morning) + kahvi (coffee) → aamukahvi</span> <span class="english">morning coffee</span></p>
                    <p><span class="finnish">kesä (summer) + loma (vacation) → kesäloma</span> <span class="english">summer vacation</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















