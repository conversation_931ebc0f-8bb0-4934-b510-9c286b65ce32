<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        /* CSS Reset for mobile menu */
        @media (max-width: 767px) {
            .nav-links {
                all: initial;
                * {
                    all: unset;
                }
            }
        }
        
        /* Unified Mobile Menu Styles */
        @media (max-width: 767px) {
            /* Common mobile menu toggle button styles */
            .mobile-menu-toggle {
                display: flex !important;
                align-items: center;
                justify-content: center;
                width: 44px;
                height: 44px;
                background-color: rgba(0, 0, 0, 0.05);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 4px;
                color: var(--text-color, #333);
                font-size: 1.5rem;
                cursor: pointer;
                z-index: 10000;
                transition: all 0.3s ease;
                margin-left: 10px;
            }
            
            .mobile-menu-toggle:hover,
            .mobile-menu-toggle:focus {
                background-color: rgba(0, 0, 0, 0.1);
                color: var(--primary-color, #003580);
            }
            
            .mobile-menu-toggle:active,
            .mobile-menu-toggle.active {
                transform: scale(0.95);
                background-color: var(--primary-color, #003580) !important;
                color: white !important;
                border-color: var(--primary-color, #003580) !important;
            }
            
            /* Dark mode mobile menu toggle */
            .dark-mode .mobile-menu-toggle {
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                color: var(--text-color, #e0e0e0);
            }
            
            .dark-mode .mobile-menu-toggle:hover,
            .dark-mode .mobile-menu-toggle:focus {
                background-color: rgba(255, 255, 255, 0.1);
                color: var(--primary-color, #1a5fb4);
            }
            
            /* Mobile theme toggle positioning */
            .theme-toggle-container.mobile-only-theme-toggle,
            .mobile-only-theme-toggle {
                display: flex !important;
                z-index: 1001;
                margin-left: auto;
            }
            
            /* Unified nav-links styles */
            .nav-links {
                display: none !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
            }
            
            /* Show nav-links when active */
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                animation: fadeIn 0.3s ease-in-out !important;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            /* Nav links list items */
            .nav-links li {
                margin: 0 !important;
                width: 100% !important;
                text-align: left !important;
                border-bottom: 1px solid #eee !important;
                box-sizing: border-box !important;
            }
            
            .nav-links li:last-child {
                border-bottom: none !important;
            }
            
            /* Nav links anchors */
            .nav-links a {
                padding: 15px !important;
                width: 100% !important;
                display: block !important;
                color: #333 !important;
                font-size: 16px !important;
                transition: all 0.2s ease !important;
            }
            
            .nav-links a:hover, 
            .nav-links a:focus,
            .nav-links .dropdown .dropbtn:hover,
            .nav-links .dropdown .dropbtn:focus {
                background-color: rgba(0, 0, 0, 0.05) !important;
                color: #003580 !important;
            }
            
            /* Dropdown content */
            .dropdown-content {
                position: static !important;
                display: none !important;
                box-shadow: none !important;
                width: 100% !important;
                padding: 0 !important;
                background-color: rgba(0, 0, 0, 0.02) !important;
                border-top: 1px solid #eee !important;
            }
            
            .dropdown-content a {
                padding: 12px 15px 12px 30px !important;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
            }
            
            .dropdown-content a:last-child {
                border-bottom: none !important;
            }
            
            /* Active dropdown */
            .dropdown.active .dropdown-content {
                display: block !important;
            }
            
            /* Dropdown button styling */
            .nav-links .dropdown .dropbtn {
                position: relative !important;
                display: flex !important;
                justify-content: flex-start !important;
                align-items: center !important;
                width: 100% !important;
                padding: 15px !important;
                padding-right: 40px !important; /* Extra space for the arrow */
                color: #333 !important;
                font-size: 16px !important;
                transition: all 0.2s ease !important;
                box-sizing: border-box !important;
            }
            
            .nav-links .dropdown .dropbtn:after {
                content: '\f107' !important; /* Font Awesome down arrow */
                font-family: 'Font Awesome 5 Free' !important;
                font-weight: 900 !important;
                position: absolute !important;
                right: 15px !important;
                transition: transform 0.3s ease !important;
                display: inline-block !important;
            }
            
            .nav-links .dropdown.active .dropbtn:after {
                transform: rotate(180deg) !important;
                display: inline-block !important;
            }
            
            /* Dark mode adjustments */
            .dark-mode .nav-links {
                background-color: #121212 !important;
                border-top: 1px solid #333 !important;
            }
            
            .dark-mode .nav-links .dropdown .dropbtn {
                color: #e0e0e0 !important;
            }
            
            .dark-mode .nav-links .dropdown .dropbtn:after {
                color: #e0e0e0 !important;
            }
            
            .dark-mode .nav-links li {
                border-bottom: 1px solid #333 !important;
            }
            
            .dark-mode .nav-links a:hover,
            .dark-mode .nav-links a:focus,
            .dark-mode .nav-links .dropdown .dropbtn:hover,
            .dark-mode .nav-links .dropdown .dropbtn:focus {
                background-color: rgba(255, 255, 255, 0.05) !important;
                color: #1a5fb4 !important;
            }
            
            .dark-mode .dropdown-content {
                background-color: rgba(255, 255, 255, 0.03) !important;
                border-top: 1px solid #333 !important;
            }
            
            .dark-mode .dropdown-content a {
                border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
            }
        }
        
        /* Make sure mobile menu toggle is visible and clickable */
        .mobile-menu-toggle {
            display: none;
            background-color: rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            padding: 0.5rem;
            margin-left: 0.5rem;
            position: relative;
            z-index: 1001;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            align-items: center;
            justify-content: center;
        }
        
        @media (max-width: 767px) {
            .mobile-menu-toggle {
                display: flex !important;
            }
            
            .mobile-only-theme-toggle {
                display: flex !important;
            }
            
            /* Hide the regular nav-links and desktop theme toggle on mobile */
            .nav-links,
            .theme-toggle-container:not(.mobile-only-theme-toggle) {
                display: none !important;
            }
            
            /* Ensure nav container has correct positioning */
            .nav-container {
                position: relative !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                padding: 1rem !important;
                width: 100% !important;
            }
            
            /* Hide nav links by default on mobile */
            .nav-links {
                display: none !important;
                position: absolute !important;
                top: 100% !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                padding: 0 !important;
                margin: 0 !important;
                visibility: hidden !important;
                opacity: 0 !important;
                transition: visibility 0.3s, opacity 0.3s !important;
            }
        }
        
        /* Mobile dropdown menu styles */
        @media (max-width: 767px) {
            .dropdown-content {
                position: static;
                display: none;
                width: 100%;
                box-shadow: none;
                margin: 0.5rem 0;
                padding-left: 1rem;
                background-color: rgba(0, 0, 0, 0.03);
                border-left: 2px solid #0066cc;
            }
            
            .dropdown.active .dropdown-content {
                display: block;
            }
            
            .dropdown-content a {
                padding: 10px 15px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                display: block;
                width: 100%;
                text-align: left;
                font-size: 0.95rem;
                position: relative;
                z-index: 1001;
            }
            
            .dropdown-content a:last-child {
                border-bottom: none;
            }
            
            .dropdown:hover .dropdown-content {
                display: none;
            }
            
            /* Important: This style must override any other styles */
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 100% !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                padding: 1rem 0 !important;
                margin-top: 0 !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            .nav-links li {
                margin: 0.5rem 0;
                width: 100%;
                text-align: left;
                padding: 0 1rem;
            }
            
            .nav-links a {
                display: block;
                padding: 0.5rem 0;
                width: 100%;
            }
            
            /* Dropdown styles moved to the unified mobile menu section above */
            
            .dark-mode .nav-links.show {
                background-color: #252525;
            }
            
            .dark-mode .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05);
                border-left: 2px solid #0066cc;
            }
            
            /* Dark mode dropdown styles moved to the unified mobile menu section */
            
            .dark-mode .dropdown-content a {
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }
        }
        
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .grammar-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .grammar-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .grammar-card h3 {
            color: #0066cc;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .grammar-card p {
            margin-bottom: 15px;
            color: #555;
        }
        
        .grammar-card a {
            display: inline-block;
            color: #0066cc;
            text-decoration: none;
            font-weight: 500;
        }
        
        .grammar-card a:hover {
            text-decoration: underline;
        }
        
        .grammar-card-link {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .grammar-category-title {
            font-size: 1.2em;
            color: #333;
            margin-top: 30px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        
        [data-theme="dark"] .grammar-category-title {
            color: #ddd;
            border-bottom-color: #444;
        }
        
        /* Grammar tabs */
        .grammar-tabs {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 30px;
            border-bottom: 2px solid #0066cc;
        }
        
        .grammar-tab {
            padding: 12px 24px;
            background-color: #f5f5f5;
            color: #333;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }
        
        .grammar-tab:hover {
            background-color: #e0e0e0;
        }
        
        .grammar-tab.active {
            background-color: #0066cc;
            color: white;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-card {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-card p {
            color: #bbb;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-tab {
            background-color: #333;
            color: #ddd;
        }
        
        [data-theme="dark"] .grammar-tab:hover {
            background-color: #444;
        }
        
        [data-theme="dark"] .grammar-tab.active {
            background-color: #0066cc;
            color: white;
        }
        
        /* Breadcrumb styles */
        .breadcrumb {
            margin-bottom: 20px;
            padding: 10px 0;
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        [data-theme="dark"] .breadcrumb {
            color: #aaa;
        }
        
        [data-theme="dark"] .breadcrumb a {
            color: #4d94ff;
        }
        
        /* Related content styles */
        .related-content {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .related-content h3 {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .related-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .related-links a {
            display: inline-block;
            padding: 8px 15px;
            background-color: #f5f5f5;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .related-links a:hover {
            background-color: #e0e0e0;
            transform: translateY(-2px);
        }
        
        [data-theme="dark"] .related-content {
            border-top-color: #333;
        }
        
        [data-theme="dark"] .related-content h3 {
            color: #aaa;
        }
        
        [data-theme="dark"] .related-links a {
            background-color: #333;
            color: #ddd;
        }
        
        [data-theme="dark"] .related-links a:hover {
            background-color: #444;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container" style="display: flex; align-items: center; justify-content: space-between; padding: 1rem;">
            <div class="logo">
                <a href="../index.html">Opiskelen Suomea</a>
            </div>
            <div class="nav-controls" style="display: flex; align-items: center; justify-content: flex-end; margin-left: auto; position: relative; height: 44px;">
                <button class="theme-toggle-button mobile-only-theme-toggle" id="grammar-toggle-dark-mobile" style="display: none; width: 44px; height: 44px; align-items: center; justify-content: center; background-color: rgba(0, 0, 0, 0.05); border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 4px; cursor: pointer; color: var(--text-color); padding: 0; z-index: 10000;"><i class="fas fa-moon"></i></button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" style="width: 44px; height: 44px; align-items: center; justify-content: center; background-color: rgba(0, 0, 0, 0.05); border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 4px; cursor: pointer; color: var(--text-color); padding: 0; z-index: 10000; margin-left: 10px;">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <ul class="nav-links" id="nav-links">
                <li><a href="../index.html">Home</a></li>
                <li><a href="../audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../index.html#daily-life">Daily Life</a>
                        <a href="../index.html#web-development">Web Development</a>
                        <a href="../index.html#cleaner">Cleaner</a>
                        <a href="../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumb">
            <a href="../index.html">Home</a> &gt; 
            <a href="index.html">Grammar</a> &gt; 
            <span id="current-category">Verbs</span>
        </div>
        
        <div class="grammar-tabs">
            <button onclick="openGrammarTab('overview')" class="grammar-tab">Overview</button>
            <button onclick="openGrammarTab('verbs')" class="grammar-tab active">Verbs</button>
            <button onclick="openGrammarTab('nouns')" class="grammar-tab">Nouns</button>
            <button onclick="openGrammarTab('cases')" class="grammar-tab">Cases</button>
            <button onclick="openGrammarTab('sentence_structure')" class="grammar-tab">Sentence Structure</button>
            <button onclick="openGrammarTab('vocabulary')" class="grammar-tab">Vocabulary</button>
        </div>

        <div id="overview" class="grammar-tab-content">
            <section class="grammar-category">
                <h3>MAIN GRAMMAR CATEGORIES</h3>
                
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="#" onclick="openGrammarTab('verbs'); return false;" class="grammar-card-link"></a>
                        <h3>Verb Conjugation</h3>
                        <p>Learn about Finnish verb types, tenses, moods, and other verb-related topics.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="#" onclick="openGrammarTab('nouns'); return false;" class="grammar-card-link"></a>
                        <h3>Noun Inflection</h3>
                        <p>Discover how Finnish nouns change form based on their role in a sentence.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="#" onclick="openGrammarTab('cases'); return false;" class="grammar-card-link"></a>
                        <h3>Finnish Cases</h3>
                        <p>Master the 15 cases in Finnish that show the relationship between words.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="#" onclick="openGrammarTab('sentence_structure'); return false;" class="grammar-card-link"></a>
                        <h3>Sentence Structure</h3>
                        <p>Learn about Finnish word order, questions, negation, and complex sentences.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="#" onclick="openGrammarTab('vocabulary'); return false;" class="grammar-card-link"></a>
                        <h3>Vocabulary</h3>
                        <p>Build your Finnish vocabulary with organized word lists and examples.</p>
                    </div>
                </div>
            </section>
            
            <section class="related-content">
                <h3>RELATED CATEGORIES</h3>
                <div class="related-links">
                    <a href="#" onclick="openGrammarTab('verbs'); return false;">Verbs</a>
                    <a href="#" onclick="openGrammarTab('nouns'); return false;">Nouns</a>
                    <a href="#" onclick="openGrammarTab('cases'); return false;">Cases</a>
                    <a href="#" onclick="openGrammarTab('sentence_structure'); return false;">Sentence Structure</a>
                    <a href="#" onclick="openGrammarTab('vocabulary'); return false;">Vocabulary</a>
                </div>
            </section>
        </div>

        <div id="verbs" class="grammar-tab-content" style="display:none">
            <section class="grammar-category">
                <h3>VERB CONJUGATION</h3>
                
                <h4 class="grammar-category-title">Verb Types</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./verbs/types/type1.html" class="grammar-card-link"></a>
                        <h3>Type 1 Verbs</h3>
                        <p>Learn about verbs ending in -a/-?? (not preceded by 'o' or 'i').</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/types/type2.html" class="grammar-card-link"></a>
                        <h3>Type 2 Verbs</h3>
                        <p>Master verbs ending in -da/-d??.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/types/type3.html" class="grammar-card-link"></a>
                        <h3>Type 3 Verbs</h3>
                        <p>Learn about verbs ending in -la/-l??, -na/-n??, -ra/-r??, -sta/-st??.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/types/type4.html" class="grammar-card-link"></a>
                        <h3>Type 4 Verbs</h3>
                        <p>Understand verbs ending in -ta/-t?? (consonant stem).</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/types/type5.html" class="grammar-card-link"></a>
                        <h3>Type 5 Verbs</h3>
                        <p>Master verbs ending in -ita/-it??.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/types/type6.html" class="grammar-card-link"></a>
                        <h3>Type 6 Verbs</h3>
                        <p>Learn about verbs ending in -eta/-et??.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Tenses</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./verbs/tenses/present.html" class="grammar-card-link"></a>
                        <h3>Present Tense</h3>
                        <p>Master the present tense conjugation patterns for all verb types.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/tenses/past.html" class="grammar-card-link"></a>
                        <h3>Past Tense</h3>
                        <p>Learn how to form and use the imperfect tense in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/tenses/future.html" class="grammar-card-link"></a>
                        <h3>Future Expressions</h3>
                        <p>Understand how to express future actions in Finnish.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Moods</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./verbs/moods/conditional.html" class="grammar-card-link"></a>
                        <h3>Conditional Mood</h3>
                        <p>Understand how to express hypothetical situations using the conditional mood.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/moods/imperative.html" class="grammar-card-link"></a>
                        <h3>Imperative Mood</h3>
                        <p>Learn how to give commands and make requests in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/moods/potential.html" class="grammar-card-link"></a>
                        <h3>Potential Mood</h3>
                        <p>Discover how to express possibility or probability in Finnish.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Other Verb Forms</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./verbs/passive/index.html" class="grammar-card-link"></a>
                        <h3>Passive Voice</h3>
                        <p>Learn how to form and use the passive voice in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/infinitives/index.html" class="grammar-card-link"></a>
                        <h3>Infinitives</h3>
                        <p>Master the different infinitive forms in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/participles/index.html" class="grammar-card-link"></a>
                        <h3>Participles</h3>
                        <p>Understand how to form and use participles in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/consonant_gradation.html" class="grammar-card-link"></a>
                        <h3>Consonant Gradation</h3>
                        <p>Learn about the consonant changes that occur in Finnish verbs.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./verbs/negation.html" class="grammar-card-link"></a>
                        <h3>Negation</h3>
                        <p>Master how to form negative verb forms in Finnish.</p>
                    </div>
                </div>
            </section>
            
            <section class="related-content">
                <h3>RELATED CATEGORIES</h3>
                <div class="related-links">
                    <a href="#" onclick="openGrammarTab('overview'); return false;">Overview</a>
                    <a href="#" onclick="openGrammarTab('nouns'); return false;">Nouns</a>
                    <a href="#" onclick="openGrammarTab('cases'); return false;">Cases</a>
                    <a href="#" onclick="openGrammarTab('sentence_structure'); return false;">Sentence Structure</a>
                    <a href="#" onclick="openGrammarTab('vocabulary'); return false;">Vocabulary</a>
                </div>
            </section>
        </div>

        <div id="nouns" class="grammar-tab-content" style="display:none">
            <section class="grammar-category">
                <h3>NOUN INFLECTION</h3>
                
                <h4 class="grammar-category-title">Noun Types</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./nouns/types/type1.html" class="grammar-card-link"></a>
                        <h3>Type 1 Nouns</h3>
                        <p>Learn about nouns ending in a vowel with no change in the stem.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/types/type2.html" class="grammar-card-link"></a>
                        <h3>Type 2 Nouns</h3>
                        <p>Master nouns ending in -i with stem ending in -e.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/types/type3.html" class="grammar-card-link"></a>
                        <h3>Type 3 Nouns</h3>
                        <p>Learn about nouns ending in -i, -o, -??, -u, -y with consonant stem.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/types/type4.html" class="grammar-card-link"></a>
                        <h3>Type 4 Nouns</h3>
                        <p>Understand nouns ending in -nen.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/types/type5.html" class="grammar-card-link"></a>
                        <h3>Type 5 Nouns</h3>
                        <p>Master nouns ending in -e with consonant stem.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Plural Forms</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./nouns/plurals/nominative.html" class="grammar-card-link"></a>
                        <h3>Nominative Plural</h3>
                        <p>Learn how to form the basic plural form of Finnish nouns.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/plurals/genitive.html" class="grammar-card-link"></a>
                        <h3>Genitive Plural</h3>
                        <p>Master the genitive plural forms in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/plurals/partitive.html" class="grammar-card-link"></a>
                        <h3>Partitive Plural</h3>
                        <p>Understand how to form and use the partitive plural in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/plurals/other_cases.html" class="grammar-card-link"></a>
                        <h3>Other Plural Cases</h3>
                        <p>Learn about the formation of other cases in the plural.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Other Noun Features</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./nouns/possessives/index.html" class="grammar-card-link"></a>
                        <h3>Possessive Suffixes</h3>
                        <p>Learn how to indicate possession using suffixes in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/consonant_gradation.html" class="grammar-card-link"></a>
                        <h3>Consonant Gradation</h3>
                        <p>Understand the consonant changes that occur in Finnish nouns.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/compound_nouns.html" class="grammar-card-link"></a>
                        <h3>Compound Nouns</h3>
                        <p>Learn how to form and use compound nouns in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./nouns/derivation.html" class="grammar-card-link"></a>
                        <h3>Noun Derivation</h3>
                        <p>Discover how new nouns are formed from other words in Finnish.</p>
                    </div>
                </div>
            </section>
            
            <section class="related-content">
                <h3>RELATED CATEGORIES</h3>
                <div class="related-links">
                    <a href="#" onclick="openGrammarTab('overview'); return false;">Overview</a>
                    <a href="#" onclick="openGrammarTab('verbs'); return false;">Verbs</a>
                    <a href="#" onclick="openGrammarTab('cases'); return false;">Cases</a>
                    <a href="#" onclick="openGrammarTab('sentence_structure'); return false;">Sentence Structure</a>
                    <a href="#" onclick="openGrammarTab('vocabulary'); return false;">Vocabulary</a>
                </div>
            </section>
        </div>

        <div id="cases" class="grammar-tab-content" style="display:none">
            <section class="grammar-category">
                <h3>FINNISH CASES</h3>
                
                <h4 class="grammar-category-title">Grammatical Cases</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./cases/grammatical/nominative.html" class="grammar-card-link"></a>
                        <h3>Nominative Case</h3>
                        <p>Learn about the basic form of nouns used as subjects.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/grammatical/genitive.html" class="grammar-card-link"></a>
                        <h3>Genitive Case</h3>
                        <p>Understand how to express possession and relationships.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/grammatical/partitive.html" class="grammar-card-link"></a>
                        <h3>Partitive Case</h3>
                        <p>Learn when and how to use the partitive case in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/grammatical/accusative.html" class="grammar-card-link"></a>
                        <h3>Accusative Case</h3>
                        <p>Understand how to mark direct objects in Finnish.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Locative Cases - Internal</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./cases/locative/inessive.html" class="grammar-card-link"></a>
                        <h3>Inessive Case (-ssa/-ss??)</h3>
                        <p>Learn how to express "inside" or "within" something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/locative/elative.html" class="grammar-card-link"></a>
                        <h3>Elative Case (-sta/-st??)</h3>
                        <p>Understand how to express movement out of something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/locative/illative.html" class="grammar-card-link"></a>
                        <h3>Illative Case (-Vn, -hVn, -seen)</h3>
                        <p>Learn how to express movement into something.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Locative Cases - External</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./cases/locative/adessive.html" class="grammar-card-link"></a>
                        <h3>Adessive Case (-lla/-ll??)</h3>
                        <p>Learn how to express "on" or "at" something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/locative/ablative.html" class="grammar-card-link"></a>
                        <h3>Ablative Case (-lta/-lt??)</h3>
                        <p>Understand how to express movement off or away from something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/locative/allative.html" class="grammar-card-link"></a>
                        <h3>Allative Case (-lle)</h3>
                        <p>Learn how to express movement onto something.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Other Cases</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./cases/other/essive.html" class="grammar-card-link"></a>
                        <h3>Essive Case (-na/-n??)</h3>
                        <p>Understand how to express temporary states or roles.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/other/translative.html" class="grammar-card-link"></a>
                        <h3>Translative Case (-ksi)</h3>
                        <p>Learn how to express change of state or becoming something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/other/abessive.html" class="grammar-card-link"></a>
                        <h3>Abessive Case (-tta/-tt??)</h3>
                        <p>Understand how to express "without" something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/other/comitative.html" class="grammar-card-link"></a>
                        <h3>Comitative Case (-ine)</h3>
                        <p>Learn how to express "with" or "accompanied by" something.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./cases/other/instructive.html" class="grammar-card-link"></a>
                        <h3>Instructive Case (-n)</h3>
                        <p>Understand how to express "by means of" something.</p>
                    </div>
                </div>
            </section>
            
            <section class="related-content">
                <h3>RELATED CATEGORIES</h3>
                <div class="related-links">
                    <a href="#" onclick="openGrammarTab('overview'); return false;">Overview</a>
                    <a href="#" onclick="openGrammarTab('verbs'); return false;">Verbs</a>
                    <a href="#" onclick="openGrammarTab('nouns'); return false;">Nouns</a>
                    <a href="#" onclick="openGrammarTab('sentence_structure'); return false;">Sentence Structure</a>
                    <a href="#" onclick="openGrammarTab('vocabulary'); return false;">Vocabulary</a>
                </div>
            </section>
        </div>

        <div id="sentence_structure" class="grammar-tab-content" style="display:none">
            <section class="grammar-category">
                <h3>FINNISH SENTENCE STRUCTURE</h3>
                
                <h4 class="grammar-category-title">Word Order</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./sentence_structure/word_order/basic.html" class="grammar-card-link"></a>
                        <h3>Basic Word Order</h3>
                        <p>Learn about the basic word order patterns in Finnish sentences.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/word_order/subject_predicate.html" class="grammar-card-link"></a>
                        <h3>Subject and Predicate</h3>
                        <p>Understand the relationship between subjects and predicates in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/word_order/objects.html" class="grammar-card-link"></a>
                        <h3>Objects</h3>
                        <p>Learn about the placement of objects in Finnish sentences.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/word_order/adverbials.html" class="grammar-card-link"></a>
                        <h3>Adverbials</h3>
                        <p>Master the placement of adverbials in Finnish sentences.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Questions</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./sentence_structure/questions/yes_no.html" class="grammar-card-link"></a>
                        <h3>Yes/No Questions</h3>
                        <p>Learn how to form questions that can be answered with yes or no.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/questions/question_words.html" class="grammar-card-link"></a>
                        <h3>Question Words</h3>
                        <p>Master the use of Finnish question words (who, what, where, etc.).</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/questions/indirect.html" class="grammar-card-link"></a>
                        <h3>Indirect Questions</h3>
                        <p>Understand how to form indirect or embedded questions in Finnish.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Negation</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./sentence_structure/negation/basic.html" class="grammar-card-link"></a>
                        <h3>Basic Negation</h3>
                        <p>Learn the fundamental patterns of negation in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/negation/questions.html" class="grammar-card-link"></a>
                        <h3>Negative Questions</h3>
                        <p>Understand how to form negative questions in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/negation/double.html" class="grammar-card-link"></a>
                        <h3>Double Negation</h3>
                        <p>Learn about double negation patterns in Finnish.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Conjunctions</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./sentence_structure/conjunctions/coordinating.html" class="grammar-card-link"></a>
                        <h3>Coordinating Conjunctions</h3>
                        <p>Learn about words that connect elements of equal grammatical rank.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/conjunctions/subordinating.html" class="grammar-card-link"></a>
                        <h3>Subordinating Conjunctions</h3>
                        <p>Master conjunctions that introduce dependent clauses.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/conjunctions/relative.html" class="grammar-card-link"></a>
                        <h3>Relative Clauses</h3>
                        <p>Understand how to form and use relative clauses in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./sentence_structure/conjunctions/reported_speech.html" class="grammar-card-link"></a>
                        <h3>Reported Speech</h3>
                        <p>Learn how to report what someone else has said in Finnish.</p>
                    </div>
                </div>
            </section>
            
            <section class="related-content">
                <h3>RELATED CATEGORIES</h3>
                <div class="related-links">
                    <a href="#" onclick="openGrammarTab('overview'); return false;">Overview</a>
                    <a href="#" onclick="openGrammarTab('verbs'); return false;">Verbs</a>
                    <a href="#" onclick="openGrammarTab('nouns'); return false;">Nouns</a>
                    <a href="#" onclick="openGrammarTab('cases'); return false;">Cases</a>
                    <a href="#" onclick="openGrammarTab('vocabulary'); return false;">Vocabulary</a>
                </div>
            </section>
        </div>

        <div id="vocabulary" class="grammar-tab-content" style="display:none">
            <section class="grammar-category">
                <h3>VOCABULARY BUILDING</h3>
                
                <h4 class="grammar-category-title">Common Words</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./vocabulary/common_words/greetings.html" class="grammar-card-link"></a>
                        <h3>Greetings & Expressions</h3>
                        <p>Learn essential Finnish greetings and common expressions.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/common_words/numbers.html" class="grammar-card-link"></a>
                        <h3>Numbers</h3>
                        <p>Master Finnish numbers, counting, and numerical expressions.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/common_words/time.html" class="grammar-card-link"></a>
                        <h3>Time Expressions</h3>
                        <p>Learn how to talk about time, days, months, and dates in Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/common_words/colors.html" class="grammar-card-link"></a>
                        <h3>Colors</h3>
                        <p>Discover Finnish color vocabulary and how to use it.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/common_words/family.html" class="grammar-card-link"></a>
                        <h3>Family Terms</h3>
                        <p>Learn vocabulary related to family members and relationships.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/common_words/food.html" class="grammar-card-link"></a>
                        <h3>Food & Drink</h3>
                        <p>Master essential food and drink vocabulary in Finnish.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Thematic Vocabulary</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./vocabulary/thematic/daily_life.html" class="grammar-card-link"></a>
                        <h3>Daily Life</h3>
                        <p>Learn vocabulary for everyday activities and situations.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/thematic/work.html" class="grammar-card-link"></a>
                        <h3>Work & Professions</h3>
                        <p>Master vocabulary related to jobs, workplaces, and professional life.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/thematic/travel.html" class="grammar-card-link"></a>
                        <h3>Travel & Transportation</h3>
                        <p>Learn essential vocabulary for traveling in Finland.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/thematic/shopping.html" class="grammar-card-link"></a>
                        <h3>Shopping</h3>
                        <p>Master vocabulary for shopping and commercial transactions.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/thematic/health.html" class="grammar-card-link"></a>
                        <h3>Health & Wellness</h3>
                        <p>Learn vocabulary related to health, medicine, and well-being.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/thematic/nature.html" class="grammar-card-link"></a>
                        <h3>Nature & Environment</h3>
                        <p>Discover vocabulary about the natural world and environment.</p>
                    </div>
                </div>
                
                <h4 class="grammar-category-title">Word Building</h4>
                <div class="grammar-grid">
                    <div class="grammar-card">
                        <a href="./vocabulary/building/word_formation.html" class="grammar-card-link"></a>
                        <h3>Word Formation</h3>
                        <p>Understand how new words are created in Finnish through derivation and compounding.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/building/common_words.html" class="grammar-card-link"></a>
                        <h3>Common Word Patterns</h3>
                        <p>Learn about recurring patterns in Finnish vocabulary.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/building/loanwords.html" class="grammar-card-link"></a>
                        <h3>Loanwords</h3>
                        <p>Discover words borrowed from other languages into Finnish.</p>
                    </div>
                    
                    <div class="grammar-card">
                        <a href="./vocabulary/building/slang.html" class="grammar-card-link"></a>
                        <h3>Slang & Colloquial Finnish</h3>
                        <p>Learn common slang expressions and spoken language vocabulary.</p>
                    </div>
                </div>
            </section>
            
            <section class="related-content">
                <h3>RELATED CATEGORIES</h3>
                <div class="related-links">
                    <a href="#" onclick="openGrammarTab('overview'); return false;">Overview</a>
                    <a href="#" onclick="openGrammarTab('verbs'); return false;">Verbs</a>
                    <a href="#" onclick="openGrammarTab('nouns'); return false;">Nouns</a>
                    <a href="#" onclick="openGrammarTab('cases'); return false;">Cases</a>
                    <a href="#" onclick="openGrammarTab('sentence_structure'); return false;">Sentence Structure</a>
                </div>
            </section>
        </div>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    
        <script src="../highlight.js"></script>
        
    <!-- Highlight mode notification -->
    <div id="highlight-notification" class="highlight-notification">
        <i class="fas fa-highlighter"></i> Highlight mode enabled. Select text to highlight it.
    </div>
    
    



<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
<script>
// Function to handle tab switching
function openGrammarTab(tabName) {
    // Hide all tab content
    var tabContents = document.getElementsByClassName('grammar-tab-content');
    for (var i = 0; i < tabContents.length; i++) {
        tabContents[i].style.display = 'none';
    }
    
    // Remove active class from all tabs
    var tabs = document.getElementsByClassName('grammar-tab');
    for (var i = 0; i < tabs.length; i++) {
        tabs[i].classList.remove('active');
    }
    
    // Show the selected tab content
    document.getElementById(tabName).style.display = 'block';
    
    // Add active class to the clicked tab
    var activeTab = document.querySelector('button[onclick="openGrammarTab(\'' + tabName + '\')"]');
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    // Update breadcrumb
    var currentCategory = document.getElementById('current-category');
    if (currentCategory) {
        // Convert tabName to display name (capitalize first letter and replace underscores with spaces)
        var displayName = tabName.charAt(0).toUpperCase() + tabName.slice(1).replace('_', ' ');
        
        // Special case for overview
        if (tabName === 'overview') {
            currentCategory.textContent = 'Overview';
        } else {
            currentCategory.textContent = displayName;
        }
    }
    
    // Update URL hash for bookmarking
    window.location.hash = tabName;
    
    // Scroll to top of the tab content
    window.scrollTo(0, document.querySelector('.grammar-tabs').offsetTop - 20);
}

// Check URL hash on page load to open the correct tab
document.addEventListener('DOMContentLoaded', function() {
    var hash = window.location.hash.substring(1);
    if (hash && document.getElementById(hash)) {
        openGrammarTab(hash);
    } else {
        // Default to overview tab
        openGrammarTab('overview');
    }
    
    // Set the first tab as active by default
    var firstTab = document.querySelector('.grammar-tab');
    if (firstTab) {
        firstTab.classList.add('active');
    }
});
</script>
</body>
</html>

















