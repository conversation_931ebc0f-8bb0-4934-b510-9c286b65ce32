﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conditional Mood - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="conditional-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="conditional-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="conditional-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Conditional Mood</span>
        </div>
        
        <section class="grammar-section">
            <h2>Conditional Mood in Finnish</h2>
            <p>The conditional mood (konditionaali) in Finnish is used to express hypothetical situations, polite requests, and actions that would happen under certain conditions. It's similar to the English "would" construction.</p>
        </section>
        
        <section class="grammar-section">
            <h3>Formation of the Conditional</h3>
            <p>The conditional mood is formed by adding the conditional marker -isi- to the verb stem, followed by personal endings.</p>
            
            <h4>Basic Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-isin</td>
                    <td>puhuisin</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-isit</td>
                    <td>puhuisit</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>-isi</td>
                    <td>puhuisi</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-isimme</td>
                    <td>puhuisimme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-isitte</td>
                    <td>puhuisitte</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-isivat</td>
                    <td>puhuisivat</td>
                </tr>
            </table>
            
            <h4>Formation by Verb Type</h4>
            <p>The formation varies slightly depending on the verb type:</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Infinitive</th>
                    <th>Conditional Stem</th>
                    <th>Example (minä)</th>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>puhua (to speak)</td>
                    <td>puhu + isi</td>
                    <td>puhuisin</td>
                </tr>
                <tr>
                    <td>Type 2</td>
                    <td>syödä (to eat)</td>
                    <td>syö + isi</td>
                    <td>söisin</td>
                </tr>
                <tr>
                    <td>Type 3</td>
                    <td>tulla (to come)</td>
                    <td>tul + isi</td>
                    <td>tulisin</td>
                </tr>
                <tr>
                    <td>Type 4</td>
                    <td>haluta (to want)</td>
                    <td>halua + isi</td>
                    <td>haluaisin</td>
                </tr>
                <tr>
                    <td>Type 5</td>
                    <td>tarvita (to need)</td>
                    <td>tarvitse + isi</td>
                    <td>tarvitsisin</td>
                </tr>
                <tr>
                    <td>Type 6</td>
                    <td>vanheta (to grow old)</td>
                    <td>vanhene + isi</td>
                    <td>vanhenisin</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Some Type 2 verbs undergo vowel changes in the conditional, similar to the past tense:</p>
                <ul>
                    <li>syö- → sö- (ö replaces yö)</li>
                    <li>juo- → jo- (o replaces uo)</li>
                    <li>vie- → ve- (e replaces ie)</li>
                </ul>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Conditional</h3>
            <p>To form the negative conditional, use the negative verb "ei" conjugated for person, followed by the conditional form without the personal ending.</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative verb</th>
                    <th>Conditional stem</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en</td>
                    <td>puhuisi</td>
                    <td>en puhuisi</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et</td>
                    <td>puhuisi</td>
                    <td>et puhuisi</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei</td>
                    <td>puhuisi</td>
                    <td>ei puhuisi</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme</td>
                    <td>puhuisi</td>
                    <td>emme puhuisi</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette</td>
                    <td>puhuisi</td>
                    <td>ette puhuisi</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät</td>
                    <td>puhuisi</td>
                    <td>eivät puhuisi</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Perfect Conditional</h3>
            <p>The perfect conditional is used for hypothetical situations in the past. It's formed using the conditional of "olla" (to be) followed by the past participle of the main verb.</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>olla (conditional)</th>
                    <th>Past participle</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>olisin</td>
                    <td>puhunut</td>
                    <td>olisin puhunut</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>olisit</td>
                    <td>puhunut</td>
                    <td>olisit puhunut</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>olisi</td>
                    <td>puhunut</td>
                    <td>olisi puhunut</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>olisimme</td>
                    <td>puhuneet</td>
                    <td>olisimme puhuneet</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>olisitte</td>
                    <td>puhuneet</td>
                    <td>olisitte puhuneet</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>olisivat</td>
                    <td>puhuneet</td>
                    <td>olisivat puhuneet</td>
                </tr>
            </table>
            
            <h4>Negative Perfect Conditional</h4>
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative olla (conditional)</th>
                    <th>Past participle</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en olisi</td>
                    <td>puhunut</td>
                    <td>en olisi puhunut</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et olisi</td>
                    <td>puhunut</td>
                    <td>et olisi puhunut</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei olisi</td>
                    <td>puhunut</td>
                    <td>ei olisi puhunut</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme olisi</td>
                    <td>puhuneet</td>
                    <td>emme olisi puhuneet</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette olisi</td>
                    <td>puhuneet</td>
                    <td>ette olisi puhuneet</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät olisi</td>
                    <td>puhuneet</td>
                    <td>eivät olisi puhuneet</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Uses of the Conditional Mood</h3>
            
            <h4>1. Hypothetical Situations</h4>
            <div class="example-box">
                <p>Jos minulla olisi rahaa, <strong>matkustaisin</strong> Suomeen. (If I had money, I would travel to Finland.)</p>
                <p>Jos osaisin suomea paremmin, <strong>puhuisin</strong> sitä enemmän. (If I knew Finnish better, I would speak it more.)</p>
            </div>
            
            <h4>2. Polite Requests</h4>
            <div class="example-box">
                <p><strong>Voisitko</strong> auttaa minua? (Could you help me?)</p>
                <p><strong>Haluaisin</strong> kahvin, kiitos. (I would like a coffee, please.)</p>
                <p><strong>Olisiko</strong> sinulla hetki aikaa? (Would you have a moment?)</p>
            </div>
            
            <h4>3. Expressing Wishes</h4>
            <div class="example-box">
                <p><strong>Haluaisin</strong> oppia suomea. (I would like to learn Finnish.)</p>
                <p><strong>Toivoisin</strong>, että tulisit käymään. (I wish you would come to visit.)</p>
            </div>
            
            <h4>4. Giving Advice</h4>
            <div class="example-box">
                <p>Sinun <strong>pitäisi</strong> levätä enemmän. (You should rest more.)</p>
                <p><strong>Olisi hyvä</strong>, jos söisit terveellisemmin. (It would be good if you ate more healthily.)</p>
            </div>
            
            <h4>5. Reporting Unconfirmed Information</h4>
            <div class="example-box">
                <p>Hän <strong>tulisi</strong> huomenna. (He/she would come tomorrow. [as reported by someone])</p>
                <p>Juna <strong>lähtisi</strong> kello 8. (The train would leave at 8 o'clock. [according to what I've heard])</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Conditional in "If" Clauses</h3>
            <p>In Finnish, conditional sentences often use the conjunction "jos" (if) followed by a verb in the conditional mood.</p>
            
            <div class="example-box">
                <p><strong>Jos sataa</strong>, jään kotiin. (If it rains, I'll stay home.) [real possibility, present tense]</p>
                <p><strong>Jos sataisi</strong>, jäisin kotiin. (If it rained, I would stay home.) [hypothetical, conditional]</p>
                <p><strong>Jos olisi satanut</strong>, olisin jäänyt kotiin. (If it had rained, I would have stayed home.) [past hypothetical, perfect conditional]</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Minä puhuisin suomea, jos osaisin.</td>
                    <td>I would speak Finnish if I knew how.</td>
                </tr>
                <tr>
                    <td>Hän tulisi, jos hänellä olisi aikaa.</td>
                    <td>He/she would come if he/she had time.</td>
                </tr>
                <tr>
                    <td>Me matkustaisimme Suomeen ensi vuonna.</td>
                    <td>We would travel to Finland next year.</td>
                </tr>
                <tr>
                    <td>Voisitko auttaa minua?</td>
                    <td>Could you help me?</td>
                </tr>
                <tr>
                    <td>Haluaisin kahvin, kiitos.</td>
                    <td>I would like a coffee, please.</td>
                </tr>
                <tr>
                    <td>Jos olisin rikas, ostaisin ison talon.</td>
                    <td>If I were rich, I would buy a big house.</td>
                </tr>
                <tr>
                    <td>En puhuisi niin nopeasti.</td>
                    <td>I wouldn't speak so fast.</td>
                </tr>
                <tr>
                    <td>Olisimme tulleet aiemmin, jos olisimme tienneet.</td>
                    <td>We would have come earlier if we had known.</td>
                </tr>
                <tr>
                    <td>Mitä sinä tekisit minun tilanteessani?</td>
                    <td>What would you do in my situation?</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















