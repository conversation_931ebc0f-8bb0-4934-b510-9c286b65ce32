<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .grammar-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .grammar-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .grammar-card h3 {
            color: #0066cc;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .grammar-card p {
            margin-bottom: 0;
            color: #555;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-card {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-card p {
            color: #bbb;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        /* Card link behavior */
        .grammar-card {
            text-decoration: none;
            display: block;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../index.html">Grammar</a>
            <span class="separator">></span>
            <span>Vocabulary</span>
        </div>
        
        <section class="grammar-section">
            <h2>Finnish Vocabulary</h2>
            <p>This section provides organized lists of Finnish vocabulary by topic, with pronunciation guides and example sentences.</p>
        </section>

        <section class="grammar-category">
            <h3>COMMON WORDS AND PHRASES</h3>
            
            <div class="grammar-grid">
                <a href="common_words/greetings.html" class="grammar-card">
                    <h3>Greetings and Introductions</h3>
                    <p>Basic greetings, introductions, and polite phrases</p>
                </a>
                
                <a href="common_words/numbers.html" class="grammar-card">
                    <h3>Numbers</h3>
                    <p>Cardinal and ordinal numbers in Finnish</p>
                </a>
                
                <a href="common_words/time.html" class="grammar-card">
                    <h3>Days, Months, and Seasons</h3>
                    <p>Vocabulary related to time, dates, and seasons</p>
                </a>
                
                <a href="common_words/colors.html" class="grammar-card">
                    <h3>Colors</h3>
                    <p>Color vocabulary in Finnish</p>
                </a>
                
                <a href="common_words/family.html" class="grammar-card">
                    <h3>Family Members</h3>
                    <p>Words for family relationships</p>
                </a>
                
                <a href="common_words/food.html" class="grammar-card">
                    <h3>Food and Drink</h3>
                    <p>Common food and drink vocabulary</p>
                </a>
            </div>
        </section>

        <section class="grammar-category">
            <h3>THEMATIC VOCABULARY</h3>
            
            <div class="grammar-grid">
                <a href="thematic/daily_life.html" class="grammar-card">
                    <h3>Daily Life</h3>
                    <p>Vocabulary for everyday activities and objects</p>
                </a>
                
                <a href="thematic/work.html" class="grammar-card">
                    <h3>Work and Professions</h3>
                    <p>Vocabulary related to jobs and the workplace</p>
                </a>
                
                <a href="thematic/travel.html" class="grammar-card">
                    <h3>Travel and Transportation</h3>
                    <p>Words and phrases for traveling in Finland</p>
                </a>
                
                <a href="thematic/shopping.html" class="grammar-card">
                    <h3>Shopping</h3>
                    <p>Vocabulary for shopping and commerce</p>
                </a>
                
                <a href="thematic/health.html" class="grammar-card">
                    <h3>Health and Body</h3>
                    <p>Words related to health, body parts, and medical care</p>
                </a>
                
                <a href="thematic/nature.html" class="grammar-card">
                    <h3>Nature and Weather</h3>
                    <p>Vocabulary for describing nature and weather conditions</p>
                </a>
            </div>
        </section>

        <section class="grammar-category">
            <h3>VOCABULARY BUILDING</h3>
            
            <div class="grammar-grid">
                <a href="building/common_words.html" class="grammar-card">
                    <h3>Most Common Finnish Words</h3>
                    <p>The 1000 most frequently used Finnish words</p>
                </a>
                
                <a href="building/word_formation.html" class="grammar-card">
                    <h3>Word Formation</h3>
                    <p>How to build new words in Finnish</p>
                </a>
                
                <a href="building/loanwords.html" class="grammar-card">
                    <h3>Loanwords</h3>
                    <p>Words borrowed from other languages into Finnish</p>
                </a>
                
                <a href="building/slang.html" class="grammar-card">
                    <h3>Slang and Colloquial Finnish</h3>
                    <p>Common slang expressions and spoken language vocabulary</p>
                </a>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish language resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>











