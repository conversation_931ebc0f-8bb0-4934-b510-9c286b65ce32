<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subordinating Conjunctions in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Subordinating Conjunctions</span>
        </div>
        
        <section class="grammar-section">
            <h2>Subordinating Conjunctions in Finnish</h2>
            <p>Subordinating conjunctions (alistuskonjunktiot) connect a dependent clause to an independent clause, creating complex sentences. They show relationships such as time, cause, condition, purpose, and more. This page explains the most common subordinating conjunctions in Finnish and how to use them.</p>
        </section>

        <section class="grammar-category">
            <h3>WHAT ARE SUBORDINATING CONJUNCTIONS?</h3>
            
            <div class="grammar-content">
                <p>Subordinating conjunctions introduce dependent clauses that cannot stand alone as complete sentences. They create a relationship between the dependent clause and the main clause:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Luen kirjaa, <strong>koska</strong> se on mielenkiintoinen.</span> <span class="english">I'm reading the book <strong>because</strong> it is interesting.</span></p>
                </div>
                
                <p>In this example, "koska" (because) is a subordinating conjunction that introduces the reason for the action in the main clause.</p>
                
                <p>Unlike coordinating conjunctions (which connect elements of equal grammatical rank), subordinating conjunctions create a hierarchy where one clause depends on another.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON SUBORDINATING CONJUNCTIONS</h3>
            
            <div class="grammar-content">
                <p>Here are the most common subordinating conjunctions in Finnish:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Finnish</th>
                        <th>English</th>
                        <th>Relationship</th>
                    </tr>
                    <tr>
                        <td>että</td>
                        <td>that</td>
                        <td>statement, content</td>
                    </tr>
                    <tr>
                        <td>koska</td>
                        <td>because</td>
                        <td>reason, cause</td>
                    </tr>
                    <tr>
                        <td>kun</td>
                        <td>when, as, since</td>
                        <td>time, cause</td>
                    </tr>
                    <tr>
                        <td>jos</td>
                        <td>if</td>
                        <td>condition</td>
                    </tr>
                    <tr>
                        <td>vaikka</td>
                        <td>although, even though</td>
                        <td>concession</td>
                    </tr>
                    <tr>
                        <td>jotta</td>
                        <td>so that, in order to</td>
                        <td>purpose</td>
                    </tr>
                    <tr>
                        <td>kunnes</td>
                        <td>until</td>
                        <td>time (endpoint)</td>
                    </tr>
                    <tr>
                        <td>kuin</td>
                        <td>as, like, than</td>
                        <td>comparison</td>
                    </tr>
                    <tr>
                        <td>mikäli</td>
                        <td>if, in case</td>
                        <td>condition</td>
                    </tr>
                    <tr>
                        <td>ellei</td>
                        <td>unless, if not</td>
                        <td>negative condition</td>
                    </tr>
                    <tr>
                        <td>jollei</td>
                        <td>if not, unless</td>
                        <td>negative condition</td>
                    </tr>
                    <tr>
                        <td>sikäli kuin</td>
                        <td>insofar as, to the extent that</td>
                        <td>limitation</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ETTÄ (THAT)</h3>
            
            <div class="grammar-content">
                <p>"Että" is one of the most common subordinating conjunctions in Finnish. It is used to introduce content clauses, often after verbs of saying, thinking, knowing, etc.:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tiedän, <strong>että</strong> hän tulee huomenna.</span> <span class="english">I know <strong>that</strong> he/she will come tomorrow.</span></p>
                    <p><span class="finnish">Hän sanoi, <strong>että</strong> on sairas.</span> <span class="english">He/she said <strong>that</strong> he/she is sick.</span></p>
                    <p><span class="finnish">On tärkeää, <strong>että</strong> tulet ajoissa.</span> <span class="english">It's important <strong>that</strong> you come on time.</span></p>
                </div>
                
                <p>Unlike in English, "että" is rarely omitted in Finnish, even in informal speech.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>KOSKA AND KUN (BECAUSE, WHEN)</h3>
            
            <div class="grammar-content">
                <h4>1. Koska (because)</h4>
                <p>"Koska" introduces a reason or cause:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">En tule, <strong>koska</strong> olen sairas.</span> <span class="english">I won't come <strong>because</strong> I am sick.</span></p>
                    <p><span class="finnish"><strong>Koska</strong> sataa, jäämme kotiin.</span> <span class="english"><strong>Because</strong> it's raining, we'll stay home.</span></p>
                </div>
                
                <h4>2. Kun (when, as, since)</h4>
                <p>"Kun" primarily indicates time, but can also express cause or condition:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Soitan sinulle, <strong>kun</strong> pääsen kotiin.</span> <span class="english">I'll call you <strong>when</strong> I get home.</span></p>
                    <p><span class="finnish"><strong>Kun</strong> olin lapsi, asuin maalla.</span> <span class="english"><strong>When</strong> I was a child, I lived in the countryside.</span></p>
                    <p><span class="finnish">Olen iloinen, <strong>kun</strong> näen sinut.</span> <span class="english">I'm happy <strong>when/because</strong> I see you.</span></p>
                </div>
                
                <p>The difference between "koska" and "kun" can sometimes be subtle. "Koska" focuses more specifically on cause-effect relationships, while "kun" can express temporal relationships that may imply causality.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>JOS AND MIKÄLI (IF)</h3>
            
            <div class="grammar-content">
                <h4>1. Jos (if)</h4>
                <p>"Jos" introduces conditional clauses:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Jos</strong> sataa, jäämme kotiin.</span> <span class="english"><strong>If</strong> it rains, we'll stay home.</span></p>
                    <p><span class="finnish">Tulen huomenna, <strong>jos</strong> minulla on aikaa.</span> <span class="english">I'll come tomorrow <strong>if</strong> I have time.</span></p>
                </div>
                
                <h4>2. Mikäli (if, in case)</h4>
                <p>"Mikäli" is a more formal alternative to "jos":</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Mikäli</strong> sopimus hyväksytään, työ alkaa ensi viikolla.</span> <span class="english"><strong>If</strong> the agreement is approved, the work will begin next week.</span></p>
                    <p><span class="finnish">Ilmoitamme teille, <strong>mikäli</strong> tilanne muuttuu.</span> <span class="english">We will notify you <strong>if</strong> the situation changes.</span></p>
                </div>
                
                <h4>3. Ellei and Jollei (unless, if not)</h4>
                <p>These are negative conditional conjunctions:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Lähden kotiin, <strong>ellei</strong> hän tule pian.</span> <span class="english">I'll go home <strong>unless</strong> he/she comes soon.</span></p>
                    <p><span class="finnish"><strong>Jollei</strong> sada, menemme ulos.</span> <span class="english"><strong>If it doesn't</strong> rain, we'll go outside.</span></p>
                </div>
                
                <p>Note that "ellei" and "jollei" are contractions of "jos ei" (if not).</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>VAIKKA (ALTHOUGH, EVEN THOUGH)</h3>
            
            <div class="grammar-content">
                <p>"Vaikka" introduces concessive clauses, expressing contrast or unexpected results:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Menen ulos, <strong>vaikka</strong> sataa.</span> <span class="english">I'm going outside <strong>even though</strong> it's raining.</span></p>
                    <p><span class="finnish"><strong>Vaikka</strong> olen väsynyt, teen työni loppuun.</span> <span class="english"><strong>Although</strong> I'm tired, I'll finish my work.</span></p>
                    <p><span class="finnish">Hän ei tullut, <strong>vaikka</strong> lupasi.</span> <span class="english">He/she didn't come <strong>even though</strong> he/she promised.</span></p>
                </div>
                
                <p>"Vaikka" can also be used to mean "even if" in hypothetical situations:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Menen ulos, <strong>vaikka</strong> sataisi.</span> <span class="english">I'll go outside <strong>even if</strong> it rains.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>JOTTA (SO THAT, IN ORDER TO)</h3>
            
            <div class="grammar-content">
                <p>"Jotta" introduces purpose clauses, explaining why an action is performed:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Opiskelen suomea, <strong>jotta</strong> voin puhua suomalaisten kanssa.</span> <span class="english">I study Finnish <strong>so that</strong> I can speak with Finns.</span></p>
                    <p><span class="finnish">Herään aikaisin, <strong>jotta</strong> ehdin junalle.</span> <span class="english">I wake up early <strong>in order to</strong> catch the train.</span></p>
                </div>
                
                <p>Purpose clauses with "jotta" often use the conditional mood in more formal contexts:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tein muistiinpanoja, <strong>jotta</strong> muistaisin kaiken.</span> <span class="english">I took notes <strong>so that</strong> I would remember everything.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>KUNNES (UNTIL)</h3>
            
            <div class="grammar-content">
                <p>"Kunnes" introduces a time clause that indicates the endpoint of the action in the main clause:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Odotan, <strong>kunnes</strong> hän tulee.</span> <span class="english">I'll wait <strong>until</strong> he/she comes.</span></p>
                    <p><span class="finnish">Jatkan lukemista, <strong>kunnes</strong> kirja loppuu.</span> <span class="english">I'll continue reading <strong>until</strong> the book ends.</span></p>
                    <p><span class="finnish">Hän nukkui, <strong>kunnes</strong> herätin hänet.</span> <span class="english">He/she slept <strong>until</strong> I woke him/her up.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>KUIN (AS, LIKE, THAN)</h3>
            
            <div class="grammar-content">
                <p>"Kuin" is used for comparisons:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän on vanhempi <strong>kuin</strong> minä.</span> <span class="english">He/she is older <strong>than</strong> me.</span></p>
                    <p><span class="finnish">Tee <strong>kuin</strong> minä teen.</span> <span class="english">Do <strong>as</strong> I do.</span></p>
                    <p><span class="finnish">Hän juoksee <strong>kuin</strong> tuuli.</span> <span class="english">He/she runs <strong>like</strong> the wind.</span></p>
                </div>
                
                <p>"Kuin" can also be part of compound conjunctions like "niin kuin" (just as) and "ikään kuin" (as if):</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän puhuu <strong>ikään kuin</strong> olisi asiantuntija.</span> <span class="english">He/she speaks <strong>as if</strong> he/she were an expert.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>WORD ORDER WITH SUBORDINATING CONJUNCTIONS</h3>
            
            <div class="grammar-content">
                <p>In Finnish, the subordinate clause can come either before or after the main clause:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Soitan sinulle, <strong>kun</strong> pääsen kotiin.</span> <span class="english">I'll call you <strong>when</strong> I get home.</span></p>
                    <p><span class="finnish"><strong>Kun</strong> pääsen kotiin, soitan sinulle.</span> <span class="english"><strong>When</strong> I get home, I'll call you.</span></p>
                </div>
                
                <p>When the subordinate clause comes first, it's often followed by a comma. The word order within the subordinate clause is typically the same as in a normal statement:</p>
                
                <div class="grammar-example">
                    <p>Main clause: <span class="finnish">Minä tulen huomenna.</span> <span class="english">I will come tomorrow.</span></p>
                    <p>Subordinate clause: <span class="finnish">...että minä tulen huomenna.</span> <span class="english">...that I will come tomorrow.</span></p>
                </div>
                
                <p>However, for emphasis, the word order can be changed:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tiedän, että <strong>huomenna</strong> hän tulee.</span> <span class="english">I know that <strong>tomorrow</strong> he/she will come. (emphasis on "tomorrow")</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









