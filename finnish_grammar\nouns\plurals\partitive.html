<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partitive Plural - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Nouns</a>
            <span class="separator">></span>
            <span>Partitive Plural</span>
        </div>
        
        <section class="grammar-section">
            <h2>Partitive Plural in Finnish</h2>
            <p>The partitive plural is one of the most frequently used forms in Finnish. It is used to express an indefinite quantity of something, partial objects, and in many other contexts. This page explains how to form and use the partitive plural in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMING THE PARTITIVE PLURAL</h3>
            
            <div class="grammar-content">
                <p>The partitive plural in Finnish has two main endings:</p>
                <ul>
                    <li><strong>-ia/-iä</strong> (following vowel harmony)</li>
                    <li><strong>-ita/-itä</strong> (following vowel harmony)</li>
                </ul>
                
                <p>The choice of ending depends on the noun type and its stem:</p>
                
                <h4>1. The -ia/-iä ending</h4>
                <p>Used with most nouns, especially those ending in a vowel:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kala (fish) → kaloja (fishes)</span></p>
                    <p><span class="finnish">päivä (day) → päiviä (days)</span></p>
                    <p><span class="finnish">auto (car) → autoja (cars)</span></p>
                </div>
                
                <h4>2. The -ita/-itä ending</h4>
                <p>Used with certain noun types, especially those with stems ending in -e or certain consonants:</p>
                <div class="grammar-example">
                    <p><span class="finnish">perhe (family) → perheitä (families)</span></p>
                    <p><span class="finnish">avain (key) → avaimia (keys)</span></p>
                    <p><span class="finnish">nainen (woman) → naisia (women)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>STEM CHANGES IN PARTITIVE PLURAL</h3>
            
            <div class="grammar-content">
                <p>When forming the partitive plural, the stem of the noun often changes:</p>
                
                <h4>1. Vowel changes</h4>
                <p>For many nouns ending in -a/-ä, the final vowel changes to -o/-ö before -ja/-jä:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kala (fish) → kaloja</span> (a → o)</p>
                    <p><span class="finnish">päivä (day) → päiviä</span> (ä → i)</p>
                </div>
                
                <h4>2. Consonant gradation</h4>
                <p>Consonant gradation often occurs in the partitive plural:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kukka (flower) → kukkia</span> (strong grade)</p>
                    <p><span class="finnish">poika (boy) → poikia</span> (strong grade)</p>
                </div>
                
                <h4>3. Stem vowel changes</h4>
                <p>Some nouns have stem vowel changes in the partitive plural:</p>
                <div class="grammar-example">
                    <p><span class="finnish">käsi (hand) → käsiä</span></p>
                    <p><span class="finnish">vesi (water) → vesiä</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES BY NOUN TYPE</h3>
            
            <div class="grammar-content">
                <p>Here are examples of partitive plural forms for different noun types:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Noun Type</th>
                        <th>Singular</th>
                        <th>Partitive Plural</th>
                        <th>Notes</th>
                    </tr>
                    <tr>
                        <td>Type 1 (-i)</td>
                        <td>kieli (language)</td>
                        <td>kieliä</td>
                        <td>-iä ending</td>
                    </tr>
                    <tr>
                        <td>Type 2 (-e)</td>
                        <td>perhe (family)</td>
                        <td>perheitä</td>
                        <td>-itä ending</td>
                    </tr>
                    <tr>
                        <td>Type 3 (consonant)</td>
                        <td>avain (key)</td>
                        <td>avaimia</td>
                        <td>-ia ending with stem change</td>
                    </tr>
                    <tr>
                        <td>Type 4 (-a/-ä)</td>
                        <td>kala (fish)</td>
                        <td>kaloja</td>
                        <td>a → o + -ja</td>
                    </tr>
                    <tr>
                        <td>Type 5 (-o/-ö/-u/-y)</td>
                        <td>auto (car)</td>
                        <td>autoja</td>
                        <td>-ja ending</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF THE PARTITIVE PLURAL</h3>
            
            <div class="grammar-content">
                <p>The partitive plural is used in the following situations:</p>
                
                <h4>1. To express an indefinite quantity</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Ostan kirjoja.</span> <span class="english">I'm buying (some) books.</span></p>
                    <p><span class="finnish">Näen taloja.</span> <span class="english">I see (some) houses.</span></p>
                </div>
                
                <h4>2. After numbers (except 1)</h4>
                <div class="grammar-example">
                    <p><span class="finnish">kaksi kirjaa</span> <span class="english">two books</span></p>
                    <p><span class="finnish">viisi taloa</span> <span class="english">five houses</span></p>
                </div>
                
                <h4>3. With negative verbs</h4>
                <div class="grammar-example">
                    <p><span class="finnish">En näe taloja.</span> <span class="english">I don't see houses.</span></p>
                    <p><span class="finnish">Hän ei osta kirjoja.</span> <span class="english">He/she doesn't buy books.</span></p>
                </div>
                
                <h4>4. To express "some" or "any"</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Onko sinulla kyniä?</span> <span class="english">Do you have any pens?</span></p>
                    <p><span class="finnish">Haluan omenoita.</span> <span class="english">I want some apples.</span></p>
                </div>
                
                <h4>5. After certain verbs</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Rakastan kirjoja.</span> <span class="english">I love books.</span></p>
                    <p><span class="finnish">Tarvitsen uusia kenkiä.</span> <span class="english">I need new shoes.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









