﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Numbers - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .number-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .number-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        
        .number-card .number {
            font-size: 1.5em;
            font-weight: 500;
            color: #0066cc;
            margin-bottom: 5px;
        }
        
        .number-card .word {
            font-size: 1.2em;
            margin-bottom: 5px;
        }
        
        .number-card .pronunciation {
            font-size: 0.9em;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .number-card {
            background-color: #2a2a2a;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="numbers-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="numbers-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="numbers-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Numbers</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Numbers in Finnish</h2>
            <p>Learning numbers is essential for everyday communication in Finnish. This page covers cardinal numbers (one, two, three), ordinal numbers (first, second, third), and how to use numbers in various contexts like telling time, discussing money, and more.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Cardinal Numbers 0-20</h3>
            
            <div class="number-grid">
                <div class="number-card">
                    <div class="number">0</div>
                    <div class="word">nolla</div>
                    <div class="pronunciation">nol-la</div>
                </div>
                
                <div class="number-card">
                    <div class="number">1</div>
                    <div class="word">yksi</div>
                    <div class="pronunciation">ük-si</div>
                </div>
                
                <div class="number-card">
                    <div class="number">2</div>
                    <div class="word">kaksi</div>
                    <div class="pronunciation">kak-si</div>
                </div>
                
                <div class="number-card">
                    <div class="number">3</div>
                    <div class="word">kolme</div>
                    <div class="pronunciation">kol-me</div>
                </div>
                
                <div class="number-card">
                    <div class="number">4</div>
                    <div class="word">neljä</div>
                    <div class="pronunciation">nel-yä</div>
                </div>
                
                <div class="number-card">
                    <div class="number">5</div>
                    <div class="word">viisi</div>
                    <div class="pronunciation">vii-si</div>
                </div>
                
                <div class="number-card">
                    <div class="number">6</div>
                    <div class="word">kuusi</div>
                    <div class="pronunciation">kuu-si</div>
                </div>
                
                <div class="number-card">
                    <div class="number">7</div>
                    <div class="word">seitsemän</div>
                    <div class="pronunciation">seit-se-män</div>
                </div>
                
                <div class="number-card">
                    <div class="number">8</div>
                    <div class="word">kahdeksan</div>
                    <div class="pronunciation">kah-dek-san</div>
                </div>
                
                <div class="number-card">
                    <div class="number">9</div>
                    <div class="word">yhdeksän</div>
                    <div class="pronunciation">üh-dek-sän</div>
                </div>
                
                <div class="number-card">
                    <div class="number">10</div>
                    <div class="word">kymmenen</div>
                    <div class="pronunciation">küm-me-nen</div>
                </div>
                
                <div class="number-card">
                    <div class="number">11</div>
                    <div class="word">yksitoista</div>
                    <div class="pronunciation">ük-si-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">12</div>
                    <div class="word">kaksitoista</div>
                    <div class="pronunciation">kak-si-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">13</div>
                    <div class="word">kolmetoista</div>
                    <div class="pronunciation">kol-me-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">14</div>
                    <div class="word">neljätoista</div>
                    <div class="pronunciation">nel-yä-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">15</div>
                    <div class="word">viisitoista</div>
                    <div class="pronunciation">vii-si-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">16</div>
                    <div class="word">kuusitoista</div>
                    <div class="pronunciation">kuu-si-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">17</div>
                    <div class="word">seitsemäntoista</div>
                    <div class="pronunciation">seit-se-män-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">18</div>
                    <div class="word">kahdeksantoista</div>
                    <div class="pronunciation">kah-dek-san-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">19</div>
                    <div class="word">yhdeksäntoista</div>
                    <div class="pronunciation">üh-dek-sän-tois-ta</div>
                </div>
                
                <div class="number-card">
                    <div class="number">20</div>
                    <div class="word">kaksikymmentä</div>
                    <div class="pronunciation">kak-si-küm-men-tä</div>
                </div>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> Numbers 11-19 follow the pattern: [number] + toista (which literally means "of the second" or "of the other").</p>
                <p>For example, "yksitoista" (11) literally means "one of the second [ten]".</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Cardinal Numbers 20-100</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Number</th>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>Pattern</th>
                </tr>
                <tr>
                    <td>20</td>
                    <td>kaksikymmentä</td>
                    <td class="pronunciation">kak-si-küm-men-tä</td>
                    <td>2 × 10</td>
                </tr>
                <tr>
                    <td>21</td>
                    <td>kaksikymmentäyksi</td>
                    <td class="pronunciation">kak-si-küm-men-tä-ük-si</td>
                    <td>2 × 10 + 1</td>
                </tr>
                <tr>
                    <td>22</td>
                    <td>kaksikymmentäkaksi</td>
                    <td class="pronunciation">kak-si-küm-men-tä-kak-si</td>
                    <td>2 × 10 + 2</td>
                </tr>
                <tr>
                    <td>30</td>
                    <td>kolmekymmentä</td>
                    <td class="pronunciation">kol-me-küm-men-tä</td>
                    <td>3 × 10</td>
                </tr>
                <tr>
                    <td>40</td>
                    <td>neljäkymmentä</td>
                    <td class="pronunciation">nel-yä-küm-men-tä</td>
                    <td>4 × 10</td>
                </tr>
                <tr>
                    <td>50</td>
                    <td>viisikymmentä</td>
                    <td class="pronunciation">vii-si-küm-men-tä</td>
                    <td>5 × 10</td>
                </tr>
                <tr>
                    <td>60</td>
                    <td>kuusikymmentä</td>
                    <td class="pronunciation">kuu-si-küm-men-tä</td>
                    <td>6 × 10</td>
                </tr>
                <tr>
                    <td>70</td>
                    <td>seitsemänkymmentä</td>
                    <td class="pronunciation">seit-se-män-küm-men-tä</td>
                    <td>7 × 10</td>
                </tr>
                <tr>
                    <td>80</td>
                    <td>kahdeksankymmentä</td>
                    <td class="pronunciation">kah-dek-san-küm-men-tä</td>
                    <td>8 × 10</td>
                </tr>
                <tr>
                    <td>90</td>
                    <td>yhdeksänkymmentä</td>
                    <td class="pronunciation">üh-dek-sän-küm-men-tä</td>
                    <td>9 × 10</td>
                </tr>
                <tr>
                    <td>100</td>
                    <td>sata</td>
                    <td class="pronunciation">sa-ta</td>
                    <td>-</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>25 = kaksikymmentäviisi (twenty-five)</p>
                <p>38 = kolmekymmentäkahdeksan (thirty-eight)</p>
                <p>42 = neljäkymmentäkaksi (forty-two)</p>
                <p>99 = yhdeksänkymmentäyhdeksän (ninety-nine)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Pattern:</strong> For numbers 21-99, the pattern is [tens number] + [units number].</p>
                <p>For example, "kaksikymmentäyksi" (21) literally means "two-ten-one".</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Larger Numbers</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Number</th>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                </tr>
                <tr>
                    <td>100</td>
                    <td>sata</td>
                    <td class="pronunciation">sa-ta</td>
                </tr>
                <tr>
                    <td>101</td>
                    <td>satayksi</td>
                    <td class="pronunciation">sa-ta-ük-si</td>
                </tr>
                <tr>
                    <td>200</td>
                    <td>kaksisataa</td>
                    <td class="pronunciation">kak-si-sa-taa</td>
                </tr>
                <tr>
                    <td>300</td>
                    <td>kolmesataa</td>
                    <td class="pronunciation">kol-me-sa-taa</td>
                </tr>
                <tr>
                    <td>1,000</td>
                    <td>tuhat</td>
                    <td class="pronunciation">tu-hat</td>
                </tr>
                <tr>
                    <td>2,000</td>
                    <td>kaksituhatta</td>
                    <td class="pronunciation">kak-si-tu-hat-ta</td>
                </tr>
                <tr>
                    <td>10,000</td>
                    <td>kymmenentuhatta</td>
                    <td class="pronunciation">küm-me-nen-tu-hat-ta</td>
                </tr>
                <tr>
                    <td>100,000</td>
                    <td>satatuhatta</td>
                    <td class="pronunciation">sa-ta-tu-hat-ta</td>
                </tr>
                <tr>
                    <td>1,000,000</td>
                    <td>miljoona</td>
                    <td class="pronunciation">mil-joo-na</td>
                </tr>
                <tr>
                    <td>1,000,000,000</td>
                    <td>miljardi</td>
                    <td class="pronunciation">mil-jar-di</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>123 = sata kaksikymmentäkolme (one hundred twenty-three)</p>
                <p>456 = neljäsataa viisikymmentäkuusi (four hundred fifty-six)</p>
                <p>1,234 = tuhat kaksisataa kolmekymmentäneljä (one thousand two hundred thirty-four)</p>
                <p>5,678 = viisituhatta kuusisataa seitsemänkymmentäkahdeksan (five thousand six hundred seventy-eight)</p>
                <p>1,000,000 = miljoona (one million)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finnish, the partitive case is used after numbers 2 and higher. This is why "sata" (100) becomes "sataa" in "kaksisataa" (200), and "tuhat" (1,000) becomes "tuhatta" in "kaksituhatta" (2,000).</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Ordinal Numbers</h3>
            <p>Ordinal numbers (first, second, third, etc.) are formed by adding specific endings to the cardinal numbers.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Number</th>
                    <th>Cardinal</th>
                    <th>Ordinal</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>yksi</td>
                    <td>ensimmäinen</td>
                    <td class="pronunciation">en-sim-mäi-nen</td>
                    <td>first</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>kaksi</td>
                    <td>toinen</td>
                    <td class="pronunciation">toi-nen</td>
                    <td>second</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>kolme</td>
                    <td>kolmas</td>
                    <td class="pronunciation">kol-mas</td>
                    <td>third</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>neljä</td>
                    <td>neljäs</td>
                    <td class="pronunciation">nel-yäs</td>
                    <td>fourth</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>viisi</td>
                    <td>viides</td>
                    <td class="pronunciation">vii-des</td>
                    <td>fifth</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>kuusi</td>
                    <td>kuudes</td>
                    <td class="pronunciation">kuu-des</td>
                    <td>sixth</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>seitsemän</td>
                    <td>seitsemäs</td>
                    <td class="pronunciation">seit-se-mäs</td>
                    <td>seventh</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>kahdeksan</td>
                    <td>kahdeksas</td>
                    <td class="pronunciation">kah-dek-sas</td>
                    <td>eighth</td>
                </tr>
                <tr>
                    <td>9</td>
                    <td>yhdeksän</td>
                    <td>yhdeksäs</td>
                    <td class="pronunciation">üh-dek-säs</td>
                    <td>ninth</td>
                </tr>
                <tr>
                    <td>10</td>
                    <td>kymmenen</td>
                    <td>kymmenes</td>
                    <td class="pronunciation">küm-me-nes</td>
                    <td>tenth</td>
                </tr>
                <tr>
                    <td>11</td>
                    <td>yksitoista</td>
                    <td>yhdestoista</td>
                    <td class="pronunciation">üh-des-tois-ta</td>
                    <td>eleventh</td>
                </tr>
                <tr>
                    <td>12</td>
                    <td>kaksitoista</td>
                    <td>kahdestoista</td>
                    <td class="pronunciation">kah-des-tois-ta</td>
                    <td>twelfth</td>
                </tr>
                <tr>
                    <td>20</td>
                    <td>kaksikymmentä</td>
                    <td>kahdeskymmenes</td>
                    <td class="pronunciation">kah-des-küm-me-nes</td>
                    <td>twentieth</td>
                </tr>
                <tr>
                    <td>21</td>
                    <td>kaksikymmentäyksi</td>
                    <td>kahdeskymmenesensimmäinen</td>
                    <td class="pronunciation">kah-des-küm-me-nes-en-sim-mäi-nen</td>
                    <td>twenty-first</td>
                </tr>
                <tr>
                    <td>100</td>
                    <td>sata</td>
                    <td>sadas</td>
                    <td class="pronunciation">sa-das</td>
                    <td>hundredth</td>
                </tr>
                <tr>
                    <td>1000</td>
                    <td>tuhat</td>
                    <td>tuhannes</td>
                    <td class="pronunciation">tu-han-nes</td>
                    <td>thousandth</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Formation Rules:</strong></p>
                <p>1. The first two ordinals are irregular: "ensimmäinen" (first) and "toinen" (second).</p>
                <p>2. For numbers 3-10, add -s to the stem (with some consonant gradation).</p>
                <p>3. For numbers 11-19, the pattern is [ordinal form of the unit] + toista.</p>
                <p>4. For tens (20, 30, etc.), the pattern is [ordinal form of the multiplier] + kymmenes.</p>
                <p>5. For compound numbers (21, 32, etc.), all parts are in ordinal form.</p>
            </div>
            
            <div class="example-box">
                <p>Asun kolmannessa kerroksessa. (I live on the third floor.)</p>
                <p>Tämä on hänen toinen kirjansa. (This is his/her second book.)</p>
                <p>Suomi on viidenneksi suurin maa Euroopassa. (Finland is the fifth largest country in Europe.)</p>
                <p>Hän tuli kilpailussa kahdeksanneksi. (He/she came eighth in the competition.)</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Using Numbers in Context</h3>
            
            <h4>Telling Time</h4>
            <table class="vocabulary-table">
                <tr>
                    <th>Time</th>
                    <th>Finnish</th>
                    <th>Literal Translation</th>
                </tr>
                <tr>
                    <td>1:00</td>
                    <td>kello yksi</td>
                    <td>clock one</td>
                </tr>
                <tr>
                    <td>2:30</td>
                    <td>puoli kolme</td>
                    <td>half three</td>
                </tr>
                <tr>
                    <td>3:15</td>
                    <td>varttia vaille neljä</td>
                    <td>quarter to four</td>
                </tr>
                <tr>
                    <td>4:45</td>
                    <td>viisitoista yli neljä</td>
                    <td>fifteen past four</td>
                </tr>
                <tr>
                    <td>5:10</td>
                    <td>kymmenen yli viisi</td>
                    <td>ten past five</td>
                </tr>
                <tr>
                    <td>6:50</td>
                    <td>kymmentä vaille seitsemän</td>
                    <td>ten to seven</td>
                </tr>
                <tr>
                    <td>12:00 (noon)</td>
                    <td>keskipäivä / kello kaksitoista</td>
                    <td>midday / clock twelve</td>
                </tr>
                <tr>
                    <td>12:00 (midnight)</td>
                    <td>keskiyö / kello kaksitoista yöllä</td>
                    <td>midnight / clock twelve at night</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finnish, when telling half hours, you refer to the upcoming hour. For example, 2:30 is "puoli kolme" (half three), not "half past two" as in English.</p>
            </div>
            
            <h4>Dates</h4>
            <table class="vocabulary-table">
                <tr>
                    <th>Date</th>
                    <th>Finnish</th>
                </tr>
                <tr>
                    <td>January 1st</td>
                    <td>tammikuun ensimmäinen päivä</td>
                </tr>
                <tr>
                    <td>February 14th</td>
                    <td>helmikuun neljästoista päivä</td>
                </tr>
                <tr>
                    <td>May 1st</td>
                    <td>vappu / toukokuun ensimmäinen päivä</td>
                </tr>
                <tr>
                    <td>December 6th</td>
                    <td>itsenäisyyspäivä / joulukuun kuudes päivä</td>
                </tr>
                <tr>
                    <td>December 24th</td>
                    <td>jouluaatto / joulukuun kahdeskymmenesneljäs päivä</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>Syntymäpäiväni on heinäkuun kahdeskymmenes päivä. (My birthday is on July 20th.)</p>
                <p>Tapaamme toukokuun viidentenätoista päivänä. (We'll meet on May 15th.)</p>
                <p>Suomen itsenäisyyspäivä on joulukuun kuudes päivä. (Finland's Independence Day is December 6th.)</p>
            </div>
            
            <h4>Money</h4>
            <table class="vocabulary-table">
                <tr>
                    <th>Amount</th>
                    <th>Finnish</th>
                </tr>
                <tr>
                    <td>1€</td>
                    <td>yksi euro</td>
                </tr>
                <tr>
                    <td>2€</td>
                    <td>kaksi euroa</td>
                </tr>
                <tr>
                    <td>5.50€</td>
                    <td>viisi euroa viisikymmentä senttiä</td>
                </tr>
                <tr>
                    <td>10.99€</td>
                    <td>kymmenen euroa yhdeksänkymmentäyhdeksän senttiä</td>
                </tr>
                <tr>
                    <td>100€</td>
                    <td>sata euroa</td>
                </tr>
                <tr>
                    <td>1,000€</td>
                    <td>tuhat euroa</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> After numbers 2 and higher, the partitive case is used. That's why "euro" becomes "euroa" and "sentti" becomes "senttiä".</p>
            </div>
            
            <h4>Age</h4>
            <table class="vocabulary-table">
                <tr>
                    <th>Age</th>
                    <th>Finnish</th>
                </tr>
                <tr>
                    <td>I am 20 years old</td>
                    <td>Olen kaksikymmentä vuotta vanha / Olen kaksikymmenvuotias</td>
                </tr>
                <tr>
                    <td>He is 35 years old</td>
                    <td>Hän on kolmekymmentäviisi vuotta vanha / Hän on kolmekymmentäviisivuotias</td>
                </tr>
                <tr>
                    <td>The child is 7 years old</td>
                    <td>Lapsi on seitsemän vuotta vanha / Lapsi on seitsenvuotias</td>
                </tr>
                <tr>
                    <td>How old are you?</td>
                    <td>Kuinka vanha sinä olet? / Minkä ikäinen olet?</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>Olen kaksikymmentäviisi vuotta vanha. (I am 25 years old.)</p>
                <p>Hän täyttää kolmekymmentä ensi kuussa. (He/she will turn 30 next month.)</p>
                <p>Kuinka vanha sinä olet? (How old are you?)</p>
                <p>Olen kolmekymmentäkaksivuotias. (I am 32 years old.)</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Number Inflection</h3>
            <p>In Finnish, numbers inflect (change form) according to case, just like nouns and adjectives. Here are some examples of how numbers change in different cases:</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Number</th>
                    <th>Nominative</th>
                    <th>Genitive</th>
                    <th>Partitive</th>
                    <th>Inessive</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>yksi</td>
                    <td>yhden</td>
                    <td>yhtä</td>
                    <td>yhdessä</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>kaksi</td>
                    <td>kahden</td>
                    <td>kahta</td>
                    <td>kahdessa</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>kolme</td>
                    <td>kolmen</td>
                    <td>kolmea</td>
                    <td>kolmessa</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>neljä</td>
                    <td>neljän</td>
                    <td>neljää</td>
                    <td>neljässä</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>viisi</td>
                    <td>viiden</td>
                    <td>viittä</td>
                    <td>viidessä</td>
                </tr>
                <tr>
                    <td>10</td>
                    <td>kymmenen</td>
                    <td>kymmenen</td>
                    <td>kymmentä</td>
                    <td>kymmenessä</td>
                </tr>
                <tr>
                    <td>100</td>
                    <td>sata</td>
                    <td>sadan</td>
                    <td>sataa</td>
                    <td>sadassa</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>Ostin <strong>yhden</strong> kirjan. (I bought one book.) [Genitive]</p>
                <p>Tarvitsen <strong>kahta</strong> kynää. (I need two pens.) [Partitive]</p>
                <p>Asun <strong>kolmannessa</strong> kerroksessa. (I live on the third floor.) [Inessive of ordinal]</p>
                <p>Odotin <strong>viittä</strong> minuuttia. (I waited for five minutes.) [Partitive]</p>
                <p>Tulen <strong>kymmeneltä</strong>. (I'll come at ten o'clock.) [Ablative]</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> Many Finnish numbers undergo consonant gradation when inflected. For example, "kaksi" (two) becomes "kahden" in the genitive case.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Practice Exercises</h3>
            
            <h4>Exercise 1: Write the following numbers in Finnish</h4>
            <ol>
                <li>7</li>
                <li>13</li>
                <li>24</li>
                <li>38</li>
                <li>42</li>
                <li>99</li>
                <li>101</li>
                <li>250</li>
                <li>1,234</li>
                <li>5,000</li>
            </ol>
            
            <h4>Exercise 2: Write the following ordinal numbers in Finnish</h4>
            <ol>
                <li>1st</li>
                <li>3rd</li>
                <li>5th</li>
                <li>10th</li>
                <li>12th</li>
                <li>20th</li>
                <li>25th</li>
                <li>100th</li>
            </ol>
            
            <h4>Exercise 3: Translate the following phrases into Finnish</h4>
            <ol>
                <li>I am 30 years old.</li>
                <li>The meeting starts at 2:30 PM.</li>
                <li>Today is April 15th.</li>
                <li>This book costs 12.99 euros.</li>
                <li>I need three tickets.</li>
                <li>She lives on the seventh floor.</li>
                <li>The store opens at 9:00 and closes at 21:00.</li>
                <li>My phone number is ************.</li>
            </ol>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















