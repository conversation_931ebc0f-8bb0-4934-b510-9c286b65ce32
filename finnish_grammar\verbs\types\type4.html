<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type 4 Verbs - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="type4-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="type4-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="type4-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Type 4 Verbs</span>
        </div>
        
        <section class="grammar-section">
            <h2>Type 4 Verbs: -ta/-tä</h2>
            <p>Type 4 verbs in Finnish end in -ta or -tä (depending on vowel harmony) and have a vowel before the -ta/-tä ending. These verbs have a special conjugation pattern where the -t- changes to -a-/-ä- in most forms.</p>
            
            <h3>Characteristics</h3>
            <ul>
                <li>End in -ta or -tä preceded by a vowel</li>
                <li>The stem is formed by replacing -ta/-tä with -a/-ä</li>
                <li>Examples: haluta (to want), tavata (to meet), herätä (to wake up)</li>
            </ul>
            
            <div class="example-box">
                <p><strong>haluta</strong> (to want) → stem: <strong>halua-</strong></p>
                <p><strong>tavata</strong> (to meet) → stem: <strong>tapaa-</strong> (note the consonant gradation: v → p)</p>
                <p><strong>herätä</strong> (to wake up) → stem: <strong>herää-</strong></p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The key characteristic of Type 4 verbs is the change from -t- to -a-/-ä- in the stem. Some verbs also undergo consonant gradation.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Present Tense Conjugation</h3>
            <p>To form the present tense, add the personal endings to the stem:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>haluta (to want)</th>
                    <th>tavata (to meet)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-n</td>
                    <td>haluan</td>
                    <td>tapaan</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-t</td>
                    <td>haluat</td>
                    <td>tapaat</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>-</td>
                    <td>haluaa</td>
                    <td>tapaa</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-mme</td>
                    <td>haluamme</td>
                    <td>tapaamme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-tte</td>
                    <td>haluatte</td>
                    <td>tapaatte</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-vat/-vät</td>
                    <td>haluavat</td>
                    <td>tapaavat</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Notice how the -t- in the infinitive form changes to -a-/-ä- in all conjugated forms.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Past Tense Conjugation</h3>
            <p>To form the past tense (imperfect), add -si- to the stem (after removing the final vowel), followed by the personal endings:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>haluta (to want)</th>
                    <th>tavata (to meet)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>halusin</td>
                    <td>tapasin</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>halusit</td>
                    <td>tapasit</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>halusi</td>
                    <td>tapasi</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>halusimme</td>
                    <td>tapasimme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>halusitte</td>
                    <td>tapasitte</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>halusivat</td>
                    <td>tapasivat</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In the past tense, the final -a/-ä of the stem is dropped before adding -si-.</p>
                <p>For example: halua- → halu- + si → halusi-</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Forms</h3>
            <p>To form negative sentences, use the negative verb "ei" conjugated for person, followed by the stem:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative verb</th>
                    <th>haluta (to want)</th>
                    <th>tavata (to meet)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en</td>
                    <td>en halua</td>
                    <td>en tapaa</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et</td>
                    <td>et halua</td>
                    <td>et tapaa</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei</td>
                    <td>ei halua</td>
                    <td>ei tapaa</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme</td>
                    <td>emme halua</td>
                    <td>emme tapaa</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette</td>
                    <td>ette halua</td>
                    <td>ette tapaa</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät</td>
                    <td>eivät halua</td>
                    <td>eivät tapaa</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Consonant Gradation in Type 4 Verbs</h3>
            <p>Many Type 4 verbs undergo consonant gradation. The strong grade appears in the infinitive form, while the weak grade appears in most conjugated forms.</p>
            
            <div class="example-box">
                <p><strong>tavata</strong> (to meet) → <strong>tapaan</strong> (I meet) [v → p]</p>
                <p><strong>pakata</strong> (to pack) → <strong>pakkaan</strong> (I pack) [k → kk]</p>
                <p><strong>hypätä</strong> (to jump) → <strong>hyppään</strong> (I jump) [p → pp]</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Common Type 4 Verbs</h3>
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>haluta</td>
                    <td>to want</td>
                    <td>tavata</td>
                    <td>to meet</td>
                </tr>
                <tr>
                    <td>herätä</td>
                    <td>to wake up</td>
                    <td>hypätä</td>
                    <td>to jump</td>
                </tr>
                <tr>
                    <td>pakata</td>
                    <td>to pack</td>
                    <td>luvata</td>
                    <td>to promise</td>
                </tr>
                <tr>
                    <td>avata</td>
                    <td>to open</td>
                    <td>vastata</td>
                    <td>to answer</td>
                </tr>
                <tr>
                    <td>kävellä</td>
                    <td>to walk</td>
                    <td>pelätä</td>
                    <td>to fear</td>
                </tr>
                <tr>
                    <td>pudota</td>
                    <td>to fall</td>
                    <td>kohdata</td>
                    <td>to encounter</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Example Sentences</h3>
            <div class="example-box">
                <p>Minä <strong>haluan</strong> kahvia. (I want coffee.)</p>
                <p>Hän <strong>tapaa</strong> ystäviään. (He/she meets his/her friends.)</p>
                <p>Me <strong>heräämme</strong> aikaisin. (We wake up early.)</p>
                <p>Lapset <strong>hyppäävät</strong> veteen. (The children jump into the water.)</p>
                <p>Minä en <strong>halua</strong> lähteä. (I don't want to leave.)</p>
                <p>Hän ei <strong>tapaa</strong> ketään. (He/she doesn't meet anyone.)</p>
                <p>Minä <strong>halusin</strong> auttaa. (I wanted to help.)</p>
                <p>He <strong>tapasivat</strong> eilen. (They met yesterday.)</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









