<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Essive Case - Finnish Grammar - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Cases</a>
            <span class="separator">></span>
            <span>Essive Case</span>
        </div>
        
        <section class="grammar-section">
            <h2>Essive Case (Essiivi)</h2>
            <p>The essive case in Finnish is used to express a temporary state or role. It answers the question "as what?" or "in what capacity?" and is formed with the ending -na/-nä.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMATION OF THE ESSIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The essive case is formed by adding -na/-nä to the genitive stem of the word (without the final -n).</p>
                
                <p>Examples of words in the essive case:</p>
                <ul>
                    <li>opettaja (teacher) → opettajana (as a teacher)</li>
                    <li>lapsi (child) → lapsena (as a child)</li>
                    <li>nuori (young) → nuorena (as young, when young)</li>
                    <li>sairas (sick) → sairaana (as sick, while sick)</li>
                    <li>ystävä (friend) → ystävänä (as a friend)</li>
                </ul>
                
                <p>The essive plural is formed by adding -ina/-inä to the plural stem:</p>
                <ul>
                    <li>opettaja → opettajina (as teachers)</li>
                    <li>lapsi → lapsina (as children)</li>
                    <li>nuori → nuorina (as young ones)</li>
                    <li>sairas → sairaina (as sick ones)</li>
                    <li>ystävä → ystävinä (as friends)</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Työskentelen opettajana.</span> <span class="english">I work as a teacher.</span></p>
                    <p><span class="finnish">Lapsina leikimme paljon.</span> <span class="english">As children, we played a lot.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF THE ESSIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The essive case is used in the following situations:</p>
                
                <h4>1. To express a temporary state or role</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Hän työskentelee lääkärinä.</span> <span class="english">He/she works as a doctor.</span></p>
                    <p><span class="finnish">Olin sairaana viime viikolla.</span> <span class="english">I was sick last week.</span></p>
                </div>
                
                <h4>2. To express a time when something happens</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Maanantaina on kokous.</span> <span class="english">On Monday, there is a meeting.</span></p>
                    <p><span class="finnish">Jouluna menen kotiin.</span> <span class="english">At Christmas, I go home.</span></p>
                </div>
                
                <h4>3. To express a stage of life</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Nuorena olin ujo.</span> <span class="english">As a young person (when I was young), I was shy.</span></p>
                    <p><span class="finnish">Lapsena asuin maalla.</span> <span class="english">As a child (when I was a child), I lived in the countryside.</span></p>
                </div>
                
                <h4>4. To express a condition or state</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Pidän kahvin kuumana.</span> <span class="english">I keep the coffee hot.</span></p>
                    <p><span class="finnish">Säilytä ruoka kylmänä.</span> <span class="english">Keep the food cold.</span></p>
                </div>
                
                <h4>5. To express "as" in comparisons</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Käytän sitä esimerkkinä.</span> <span class="english">I use it as an example.</span></p>
                    <p><span class="finnish">Hän toimii todistajana.</span> <span class="english">He/she acts as a witness.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ESSIVE VS. TRANSLATIVE</h3>
            
            <div class="grammar-content">
                <p>The essive and translative cases both deal with states or conditions, but they have different uses:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Ending</th>
                        <th>Meaning</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Essive</td>
                        <td>-na/-nä</td>
                        <td>As, in the capacity of (static state)</td>
                        <td>opettajana (as a teacher)</td>
                    </tr>
                    <tr>
                        <td>Translative</td>
                        <td>-ksi</td>
                        <td>Becoming, changing into (dynamic change)</td>
                        <td>opettajaksi (to become a teacher)</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Työskentelen opettajana.</span> <span class="english">I work as a teacher. (Essive - current state)</span></p>
                    <p><span class="finnish">Opiskelen opettajaksi.</span> <span class="english">I study to become a teacher. (Translative - change of state)</span></p>
                </div>
                
                <p>The essive expresses a temporary or current state, while the translative expresses a change of state or becoming something.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXPRESSIONS</h3>
            
            <div class="grammar-content">
                <p>Some common expressions using the essive case:</p>
                
                <h4>1. Days of the week and holidays</h4>
                <div class="grammar-example">
                    <p><span class="finnish">maanantaina</span> <span class="english">on Monday</span></p>
                    <p><span class="finnish">tiistaina</span> <span class="english">on Tuesday</span></p>
                    <p><span class="finnish">jouluna</span> <span class="english">at Christmas</span></p>
                    <p><span class="finnish">pääsiäisenä</span> <span class="english">at Easter</span></p>
                </div>
                
                <h4>2. Professions and roles</h4>
                <div class="grammar-example">
                    <p><span class="finnish">toimia johtajana</span> <span class="english">to act as a manager</span></p>
                    <p><span class="finnish">työskennellä opettajana</span> <span class="english">to work as a teacher</span></p>
                    <p><span class="finnish">olla sihteerinä</span> <span class="english">to be a secretary</span></p>
                </div>
                
                <h4>3. States and conditions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">olla sairaana</span> <span class="english">to be sick</span></p>
                    <p><span class="finnish">olla terveenä</span> <span class="english">to be healthy</span></p>
                    <p><span class="finnish">olla hereillä</span> <span class="english">to be awake</span></p>
                    <p><span class="finnish">olla humalassa</span> <span class="english">to be drunk</span></p>
                </div>
                
                <h4>4. Fixed expressions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">pitää hauskana</span> <span class="english">to find amusing</span></p>
                    <p><span class="finnish">pitää tärkeänä</span> <span class="english">to consider important</span></p>
                    <p><span class="finnish">säilyttää tuoreena</span> <span class="english">to keep fresh</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









