<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Games - Opiskelen Suomea</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="games/games-styles.css">
    <style>
        /* Mobile menu specific styles */
        @media (max-width: 767px) {
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            .dark-mode .nav-links.show {
                background-color: #252525 !important;
            }
            
            /* Dropdown styles for mobile */
            .dropdown-content {
                display: none !important;
            }
            
            .dropdown.active .dropdown-content {
                display: block !important;
                position: static !important;
                width: 100% !important;
                background-color: rgba(0, 0, 0, 0.03) !important;
                box-shadow: none !important;
                border-top: 1px solid #eee !important;
            }
            
            .dark-mode .dropdown.active .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05) !important;
                border-top: 1px solid #333 !important;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="games-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="index.html">Home</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content">
                        <a href="video.html?channel=kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="video.html?channel=finnishcrashcourse">Finnish Crash Course</a>
                        <a href="video.html?channel=finnishtogo">Finnish To Go</a>
                        <a href="video.html?channel=suomenkurssiyt">Suomen Kurssi</a>
                        <a href="video.html?channel=yleareena">Yle Areena 1</a>
                        <a href="video.html?channel=yleareena2">Yle Areena 2</a>
                        <a href="video.html?channel=yleareena3">Yle Areena 3</a>
                        <a href="video.html?channel=yleareena4">Yle Areena 4</a>
                        <a href="video.html?channel=yleareena5">Yle Areena 5</a>
                        <a href="video.html?channel=pipsapossu">Pipsa Possu</a>
                        <a href="video.html?channel=katchatsfinnish">KatChats Finnish</a>
                        <a href="video.html?channel=kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="video.html?channel=kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="video.html?channel=kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="video.html?channel=kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="audio.html">Audio</a></li>
                <li><a href="finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="index.html#daily-life">Daily Life</a>
                        <a href="index.html#web-development">Web Development</a>
                        <a href="index.html#cleaner">Cleaner</a>
                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="games.html">Games</a>
                    </div>
                </li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="games-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <section class="intro">
            <h2>Games Collection</h2>
            <p>Take a break from learning and have some fun with these simple games. Playing games in Finnish can also help reinforce your language skills!</p>
        </section>

        <section class="games-section">
            <div class="games-grid">
                <!-- Tic Tac Toe (Caro) Game -->
                <div class="game-card">
                    <div class="game-header">
                        <h3>Ristinolla (Gomoku / Caro)</h3>
                    </div>
                    <div class="game-body">
                        <p>Classic Gomoku game (also known as Caro or Five in a Row). Get 5 in a row to win! Play against the computer or with a friend.</p>
                        <div class="game-preview">
                            <i class="fas fa-gamepad"></i>
                        </div>
                    </div>
                    <div class="game-footer">
                        <a href="games/tic-tac-toe.html" class="play-button">Play Now</a>
                    </div>
                </div>

                <!-- Memory Match Game -->
                <div class="game-card">
                    <div class="game-header">
                        <h3>Muistipeli (Memory Game)</h3>
                    </div>
                    <div class="game-body">
                        <p>Test your memory by matching pairs of cards with Finnish vocabulary.</p>
                        <div class="game-preview">
                            <i class="fas fa-brain"></i>
                        </div>
                    </div>
                    <div class="game-footer">
                        <a href="games/memory-game.html" class="play-button">Play Now</a>
                    </div>
                </div>

                <!-- Word Scramble Game -->
                <div class="game-card">
                    <div class="game-header">
                        <h3>Sanapeli (Word Scramble)</h3>
                    </div>
                    <div class="game-body">
                        <p>Unscramble Finnish words to improve your vocabulary and spelling.</p>
                        <div class="game-preview">
                            <i class="fas fa-font"></i>
                        </div>
                    </div>
                    <div class="game-footer">
                        <a href="games/word-scramble.html" class="play-button">Play Now</a>
                    </div>
                </div>

                <!-- Chess Game -->
                <div class="game-card">
                    <div class="game-header">
                        <h3>Shakki (Chess)</h3>
                    </div>
                    <div class="game-body">
                        <p>Classic chess game with multiple difficulty levels. Play against the computer or challenge a friend.</p>
                        <div class="game-preview">
                            <i class="fas fa-chess"></i>
                        </div>
                    </div>
                    <div class="game-footer">
                        <a href="games/chess.html" class="play-button">Play Now</a>
                    </div>
                </div>

                <!-- More games can be added here -->
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Opiskelen Suomea. All rights reserved.</p>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Initialize dark mode and mobile menu
        document.addEventListener("DOMContentLoaded", function() {
            // Mobile menu toggle functionality
            const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            const navLinks = document.getElementById("nav-links");

            console.log("Games page - Mobile menu toggle:", mobileMenuToggle);
            console.log("Games page - Nav links:", navLinks);

            if (mobileMenuToggle && navLinks) {
                console.log("Games page - Adding click event listener to mobile menu toggle");
                // Toggle mobile menu
                mobileMenuToggle.addEventListener("click", function() {
                    console.log("Games page - Mobile menu toggle clicked");
                    navLinks.classList.toggle("show");
                    this.classList.toggle("active");
                    console.log("Games page - Nav links show class:", navLinks.classList.contains("show"));
                });
            }

            // Check for saved dark mode preference
            const darkMode = localStorage.getItem("darkMode");
            
            // Apply dark mode if previously enabled
            if (darkMode === "enabled") {
                document.body.classList.add("dark-mode");
                
                // Update icon to sun
                const darkModeBtn = document.getElementById("games-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Update mobile icon too
                const mobileDarkModeBtn = document.getElementById("games-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            }
            
            // Set up dark mode toggle
            const darkModeToggle = document.getElementById("games-toggle-dark");
            if (darkModeToggle) {
                darkModeToggle.addEventListener("click", toggleDarkMode);
            }
            
            // Set up mobile dark mode toggle
            const mobileDarkModeToggle = document.getElementById("games-toggle-dark-mobile");
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.addEventListener("click", toggleDarkMode);
            }

            // Handle dropdown menus in mobile view
            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach((dropdown) => {
                const dropbtn = dropdown.querySelector(".dropbtn");
                if (dropbtn) {
                    dropbtn.addEventListener("click", function (e) {
                        // Only in mobile view
                        if (window.innerWidth <= 767) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log("Games page - Dropdown button clicked:", dropbtn.textContent);

                            // Close other active dropdowns
                            dropdowns.forEach((otherDropdown) => {
                                if (
                                    otherDropdown !== dropdown &&
                                    otherDropdown.classList.contains("active")
                                ) {
                                    otherDropdown.classList.remove("active");
                                }
                            });

                            // Toggle current dropdown
                            dropdown.classList.toggle("active");
                            console.log("Games page - Dropdown active:", dropdown.classList.contains("active"));
                        }
                    });
                }
            });
        });
        
        // Toggle dark mode function
        function toggleDarkMode() {
            document.body.classList.toggle("dark-mode");
            
            // Store preference in localStorage
            if (document.body.classList.contains("dark-mode")) {
                localStorage.setItem("darkMode", "enabled");
                
                // Change icon to sun
                const darkModeBtn = document.getElementById("games-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Change mobile icon to sun
                const mobileDarkModeBtn = document.getElementById("games-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            } else {
                localStorage.setItem("darkMode", "disabled");
                
                // Change icon back to moon
                const darkModeBtn = document.getElementById("games-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-sun");
                        icon.classList.add("fa-moon");
                    }
                }
                
                // Change mobile icon back to moon
                const mobileDarkModeBtn = document.getElementById("games-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-sun");
                        mobileIcon.classList.add("fa-moon");
                    }
                }
            }
        }
    </script>
</body>
</html>