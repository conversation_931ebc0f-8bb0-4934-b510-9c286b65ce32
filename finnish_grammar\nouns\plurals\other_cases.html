﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Other Plural Cases - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Nouns</a>
            <span class="separator">></span>
            <span>Other Plural Cases</span>
        </div>
        
        <section class="grammar-section">
            <h2>Other Plural Cases in Finnish</h2>
            <p>Besides the nominative, genitive, and partitive plurals, Finnish has several other cases that can be used in the plural form. This page explains how to form and use these other plural cases in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMING OTHER PLURAL CASES</h3>
            
            <div class="grammar-content">
                <p>Most of the other plural cases in Finnish are formed by adding the case ending to the plural stem, which is usually the genitive plural stem without the final -n:</p>
                
                <div class="grammar-example">
                    <p>Genitive plural: <span class="finnish">talojen</span> (houses')</p>
                    <p>Plural stem: <span class="finnish">taloje-</span></p>
                    <p>Inessive plural: <span class="finnish">taloissa</span> (in houses)</p>
                </div>
                
                <p>The main exception is the illative plural, which has its own formation pattern.</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Ending</th>
                        <th>Example (talo - house)</th>
                        <th>Meaning</th>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>-issa/-issä</td>
                        <td>taloissa</td>
                        <td>in houses</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>-ista/-istä</td>
                        <td>taloista</td>
                        <td>from houses</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>-ihin/-iin</td>
                        <td>taloihin</td>
                        <td>into houses</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>-illa/-illä</td>
                        <td>taloilla</td>
                        <td>on houses</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>-ilta/-iltä</td>
                        <td>taloilta</td>
                        <td>from houses</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>-ille</td>
                        <td>taloille</td>
                        <td>to houses</td>
                    </tr>
                    <tr>
                        <td>Essive</td>
                        <td>-ina/-inä</td>
                        <td>taloina</td>
                        <td>as houses</td>
                    </tr>
                    <tr>
                        <td>Translative</td>
                        <td>-iksi</td>
                        <td>taloiksi</td>
                        <td>into houses (becoming)</td>
                    </tr>
                    <tr>
                        <td>Instructive</td>
                        <td>-in</td>
                        <td>taloin</td>
                        <td>with houses (rare)</td>
                    </tr>
                    <tr>
                        <td>Abessive</td>
                        <td>-itta/-ittä</td>
                        <td>taloitta</td>
                        <td>without houses (rare)</td>
                    </tr>
                    <tr>
                        <td>Comitative</td>
                        <td>-ine-</td>
                        <td>taloineen</td>
                        <td>with their houses (rare)</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>LOCAL CASES IN PLURAL</h3>
            
            <div class="grammar-content">
                <p>The local cases (inessive, elative, illative, adessive, ablative, allative) are commonly used in the plural form:</p>
                
                <h4>1. Internal local cases</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Inessive: taloissa</span> <span class="english">in houses</span></p>
                    <p><span class="finnish">Elative: taloista</span> <span class="english">from houses</span></p>
                    <p><span class="finnish">Illative: taloihin</span> <span class="english">into houses</span></p>
                </div>
                
                <div class="grammar-example">
                    <p><span class="finnish">Kirjat ovat laatikoissa.</span> <span class="english">The books are in boxes.</span></p>
                    <p><span class="finnish">Otan kirjat laatikoista.</span> <span class="english">I take the books from boxes.</span></p>
                    <p><span class="finnish">Laitan kirjat laatikoihin.</span> <span class="english">I put the books into boxes.</span></p>
                </div>
                
                <h4>2. External local cases</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Adessive: taloilla</span> <span class="english">on houses</span></p>
                    <p><span class="finnish">Ablative: taloilta</span> <span class="english">from houses</span></p>
                    <p><span class="finnish">Allative: taloille</span> <span class="english">to houses</span></p>
                </div>
                
                <div class="grammar-example">
                    <p><span class="finnish">Kirjat ovat pöydillä.</span> <span class="english">The books are on tables.</span></p>
                    <p><span class="finnish">Otan kirjat pöydiltä.</span> <span class="english">I take the books from tables.</span></p>
                    <p><span class="finnish">Laitan kirjat pöydille.</span> <span class="english">I put the books onto tables.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>GRAMMATICAL CASES IN PLURAL</h3>
            
            <div class="grammar-content">
                <p>The grammatical cases (essive, translative) are also used in the plural form:</p>
                
                <h4>1. Essive (-ina/-inä)</h4>
                <p>Used to express "as" or "in the capacity of":</p>
                <div class="grammar-example">
                    <p><span class="finnish">Käytän niitä työkaluina.</span> <span class="english">I use them as tools.</span></p>
                    <p><span class="finnish">He työskentelevät opettajina.</span> <span class="english">They work as teachers.</span></p>
                </div>
                
                <h4>2. Translative (-iksi)</h4>
                <p>Used to express "becoming" or "changing into":</p>
                <div class="grammar-example">
                    <p><span class="finnish">Muutan sanat lauseiksi.</span> <span class="english">I change the words into sentences.</span></p>
                    <p><span class="finnish">He opiskelevat lääkäreiksi.</span> <span class="english">They study to become doctors.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>RARE CASES IN PLURAL</h3>
            
            <div class="grammar-content">
                <p>Some cases are less commonly used in modern Finnish, but still appear in certain expressions:</p>
                
                <h4>1. Instructive (-in)</h4>
                <p>Used in fixed expressions to indicate means or manner:</p>
                <div class="grammar-example">
                    <p><span class="finnish">omin silmin</span> <span class="english">with one's own eyes</span></p>
                    <p><span class="finnish">suurin kirjaimin</span> <span class="english">in capital letters</span></p>
                </div>
                
                <h4>2. Abessive (-itta/-ittä)</h4>
                <p>Used to express "without":</p>
                <div class="grammar-example">
                    <p><span class="finnish">rahatta</span> <span class="english">without money</span></p>
                    <p><span class="finnish">kirjoitta</span> <span class="english">without books</span></p>
                </div>
                
                <h4>3. Comitative (-ine-)</h4>
                <p>Used to express "with" (always with possessive suffix):</p>
                <div class="grammar-example">
                    <p><span class="finnish">Hän tuli lapsineen.</span> <span class="english">He/she came with his/her children.</span></p>
                    <p><span class="finnish">Talo kalusteineen</span> <span class="english">The house with its furniture</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES BY NOUN TYPE</h3>
            
            <div class="grammar-content">
                <p>Here are examples of plural case forms for different noun types:</p>
                
                <h4>Type 4 noun: kala (fish)</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Plural Form</th>
                        <th>Meaning</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>kalat</td>
                        <td>fishes</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>kalojen</td>
                        <td>of fishes</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>kaloja</td>
                        <td>some fishes</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>kaloissa</td>
                        <td>in fishes</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>kaloista</td>
                        <td>from fishes</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>kaloihin</td>
                        <td>into fishes</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>kaloilla</td>
                        <td>on fishes</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>kaloilta</td>
                        <td>from fishes</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>kaloille</td>
                        <td>to fishes</td>
                    </tr>
                </table>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















