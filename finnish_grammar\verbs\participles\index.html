﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Participles - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="participles-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="participles-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="participles-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Participles</span>
        </div>
        
        <section class="grammar-section">
            <h2>Participles in Finnish</h2>
            <p>Participles in Finnish are verb forms that function as adjectives. They describe nouns based on actions or states. Finnish has several participle forms, each with active and passive variants. Participles are essential for forming compound tenses and various constructions.</p>
        </section>
        
        <section class="grammar-section">
            <h3>Present Participles</h3>
            <p>Present participles describe ongoing or habitual actions. They correspond roughly to English "-ing" forms when used as adjectives.</p>
            
            <h4>Active Present Participle</h4>
            <p>The active present participle is formed by adding -va/-vä to the verb stem.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>Active Present Participle</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhuva</td>
                    <td>speaking</td>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luke-</td>
                    <td>lukeva</td>
                    <td>reading</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syövä</td>
                    <td>eating</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tule-</td>
                    <td>tuleva</td>
                    <td>coming</td>
                </tr>
                <tr>
                    <td>nukkua (to sleep)</td>
                    <td>nukku-</td>
                    <td>nukkuva</td>
                    <td>sleeping</td>
                </tr>
            </table>
            
            <h4>Uses of the Active Present Participle</h4>
            <div class="example-box">
                <p><strong>Puhuva</strong> mies on opettaja. (The speaking man is a teacher.)</p>
                <p><strong>Lukeva</strong> nainen istuu kirjastossa. (The reading woman is sitting in the library.)</p>
                <p><strong>Nukkuva</strong> lapsi on sängyssä. (The sleeping child is in bed.)</p>
                <p><strong>Tuleva</strong> viikko on kiireinen. (The coming week is busy.)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The active present participle can be declined like any adjective:</p>
                <p>puhuva (nominative) → puhuvan (genitive) → puhuvaa (partitive) → puhuvassa (inessive), etc.</p>
            </div>
            
            <h4>Passive Present Participle</h4>
            <p>The passive present participle is formed by adding -ttava/-ttävä to the verb stem.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>Passive Present Participle</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhuttava</td>
                    <td>to be spoken, that should be spoken</td>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luke-</td>
                    <td>luettava</td>
                    <td>to be read, that should be read</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syötävä</td>
                    <td>to be eaten, edible</td>
                </tr>
                <tr>
                    <td>tehdä (to do)</td>
                    <td>teh-</td>
                    <td>tehtävä</td>
                    <td>to be done, task</td>
                </tr>
            </table>
            
            <h4>Uses of the Passive Present Participle</h4>
            <div class="example-box">
                <p>Tämä on <strong>luettava</strong> kirja. (This is a book that should be read.)</p>
                <p>Minulla on paljon <strong>tehtävää</strong>. (I have a lot to do.)</p>
                <p>Onko tämä <strong>syötävää</strong>? (Is this edible?)</p>
                <p>Tässä on <strong>puhuttavaa</strong> asiaa. (Here is a matter to be discussed.)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The passive present participle often implies necessity or obligation ("should be done") and can also function as a noun (tehtävä = task).</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Past Participles</h3>
            <p>Past participles describe completed actions. They are essential for forming perfect tenses.</p>
            
            <h4>Active Past Participle</h4>
            <p>The active past participle is formed by adding -nut/-nyt to the verb stem.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>Active Past Participle</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhunut</td>
                    <td>having spoken</td>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luke-</td>
                    <td>lukenut</td>
                    <td>having read</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syönyt</td>
                    <td>having eaten</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tul-</td>
                    <td>tullut</td>
                    <td>having come</td>
                </tr>
                <tr>
                    <td>mennä (to go)</td>
                    <td>men-</td>
                    <td>mennyt</td>
                    <td>having gone, gone</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> The choice between -nut and -nyt follows vowel harmony rules.</p>
                <p>In plural form, -nut/-nyt changes to -neet: puhunut → puhuneet, syönyt → syöneet</p>
            </div>
            
            <h4>Uses of the Active Past Participle</h4>
            <div class="example-box">
                <p><strong>Puhunut</strong> mies lähti. (The man who had spoken left.)</p>
                <p><strong>Lukenut</strong> nainen tiesi paljon. (The woman who had read knew a lot.)</p>
                <p>Eilen <strong>tullut</strong> vieras on vielä täällä. (The guest who came yesterday is still here.)</p>
                <p><strong>Mennyt</strong> aika ei palaa. (The past time doesn't return.)</p>
            </div>
            
            <h4>Perfect Tense Formation</h4>
            <p>The active past participle is used to form the perfect tense with the auxiliary verb "olla" (to be).</p>
            
            <div class="example-box">
                <p>Minä olen <strong>puhunut</strong>. (I have spoken.)</p>
                <p>Sinä olet <strong>lukenut</strong>. (You have read.)</p>
                <p>Hän on <strong>syönyt</strong>. (He/she has eaten.)</p>
                <p>Me olemme <strong>tulleet</strong>. (We have come.)</p>
                <p>Te olette <strong>menneet</strong>. (You have gone.)</p>
                <p>He ovat <strong>nukkuneet</strong>. (They have slept.)</p>
            </div>
            
            <h4>Passive Past Participle</h4>
            <p>The passive past participle is formed by adding -ttu/-tty to the verb stem.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>Passive Past Participle</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhuttu</td>
                    <td>spoken</td>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luke-</td>
                    <td>luettu</td>
                    <td>read</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syöty</td>
                    <td>eaten</td>
                </tr>
                <tr>
                    <td>tehdä (to do)</td>
                    <td>teh-</td>
                    <td>tehty</td>
                    <td>done</td>
                </tr>
            </table>
            
            <h4>Uses of the Passive Past Participle</h4>
            <div class="example-box">
                <p><strong>Puhuttu</strong> kieli on erilainen kuin kirjoitettu. (Spoken language is different from written.)</p>
                <p>Tämä on hyvin <strong>luettu</strong> kirja. (This is a well-read book.)</p>
                <p>Kaikki ruoka on <strong>syöty</strong>. (All the food is eaten.)</p>
                <p>Työ on <strong>tehty</strong>. (The work is done.)</p>
            </div>
            
            <h4>Passive Perfect Tense Formation</h4>
            <p>The passive past participle is used to form the passive perfect tense with the auxiliary verb "olla" (to be).</p>
            
            <div class="example-box">
                <p>Suomea on <strong>puhuttu</strong> täällä pitkään. (Finnish has been spoken here for a long time.)</p>
                <p>Kirja on <strong>luettu</strong> moneen kertaan. (The book has been read many times.)</p>
                <p>Kaikki on <strong>tehty</strong> valmiiksi. (Everything has been done ready.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Agent Participle</h3>
            <p>The agent participle is used to express who performed an action. It's formed by adding -ma/-mä to the verb stem.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>Agent Participle</th>
                </tr>
                <tr>
                    <td>kirjoittaa (to write)</td>
                    <td>kirjoitta-</td>
                    <td>kirjoittama</td>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luke-</td>
                    <td>lukema</td>
                </tr>
                <tr>
                    <td>tehdä (to do)</td>
                    <td>teke-</td>
                    <td>tekemä</td>
                </tr>
                <tr>
                    <td>lähettää (to send)</td>
                    <td>lähettä-</td>
                    <td>lähettämä</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> The agent participle is always used with a genitive attribute that indicates who performed the action.</p>
            </div>
            
            <h4>Uses of the Agent Participle</h4>
            <div class="example-box">
                <p>Tämä on Mikan <strong>kirjoittama</strong> kirja. (This is a book written by Mika.)</p>
                <p>Luin äidin <strong>lähettämän</strong> kirjeen. (I read the letter sent by mother.)</p>
                <p>Tämä on sinun <strong>tekemäsi</strong> virhe. (This is a mistake made by you.)</p>
                <p>Opettajan <strong>antamat</strong> tehtävät olivat vaikeita. (The tasks given by the teacher were difficult.)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The agent participle often takes a possessive suffix that agrees with the genitive attribute:</p>
                <p>minun tekemäni (made by me), sinun tekemäsi (made by you), hänen tekemänsä (made by him/her)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Participle</h3>
            <p>The negative participle expresses "not having done something." It's formed by adding -maton/-mätön to the verb stem.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>Negative Participle</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luke-</td>
                    <td>lukematon</td>
                    <td>unread</td>
                </tr>
                <tr>
                    <td>tietää (to know)</td>
                    <td>tietä-</td>
                    <td>tietämätön</td>
                    <td>unknowing, ignorant</td>
                </tr>
                <tr>
                    <td>nähdä (to see)</td>
                    <td>näke-</td>
                    <td>näkemätön</td>
                    <td>unseen</td>
                </tr>
                <tr>
                    <td>odottaa (to expect)</td>
                    <td>odotta-</td>
                    <td>odottamaton</td>
                    <td>unexpected</td>
                </tr>
            </table>
            
            <h4>Uses of the Negative Participle</h4>
            <div class="example-box">
                <p>Pöydällä on <strong>lukematon</strong> kirja. (There is an unread book on the table.)</p>
                <p>Tämä oli <strong>odottamaton</strong> yllätys. (This was an unexpected surprise.)</p>
                <p>Hän on <strong>tietämätön</strong> asiasta. (He/she is ignorant of the matter.)</p>
                <p><strong>Näkemätön</strong> vaara uhkaa meitä. (An unseen danger threatens us.)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The negative participle functions as an adjective and can be declined in all cases.</p>
                <p>It often corresponds to English adjectives with prefixes like "un-", "in-", or suffixes like "-less".</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Participles in Compound Tenses</h3>
            
            <h4>Perfect Tense</h4>
            <p>The perfect tense is formed with the present tense of "olla" + active past participle.</p>
            
            <div class="example-box">
                <p>Minä olen <strong>puhunut</strong>. (I have spoken.)</p>
                <p>Sinä olet <strong>lukenut</strong>. (You have read.)</p>
                <p>Hän on <strong>syönyt</strong>. (He/she has eaten.)</p>
            </div>
            
            <h4>Pluperfect Tense</h4>
            <p>The pluperfect tense is formed with the imperfect tense of "olla" + active past participle.</p>
            
            <div class="example-box">
                <p>Minä olin <strong>puhunut</strong>. (I had spoken.)</p>
                <p>Sinä olit <strong>lukenut</strong>. (You had read.)</p>
                <p>Hän oli <strong>syönyt</strong>. (He/she had eaten.)</p>
            </div>
            
            <h4>Passive Perfect Tense</h4>
            <p>The passive perfect tense is formed with "on" + passive past participle.</p>
            
            <div class="example-box">
                <p>Suomea on <strong>puhuttu</strong> täällä pitkään. (Finnish has been spoken here for a long time.)</p>
                <p>Kaikki on <strong>tehty</strong>. (Everything has been done.)</p>
            </div>
            
            <h4>Passive Pluperfect Tense</h4>
            <p>The passive pluperfect tense is formed with "oli" + passive past participle.</p>
            
            <div class="example-box">
                <p>Suomea oli <strong>puhuttu</strong> täällä jo kauan. (Finnish had been spoken here for a long time already.)</p>
                <p>Kaikki oli <strong>tehty</strong> ennen kuin saavuin. (Everything had been done before I arrived.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Special Constructions with Participles</h3>
            
            <h4>Temporal Construction</h4>
            <p>The temporal construction uses the passive past participle + partitive of "se" (sitä) + possessive suffix to express "when" or "after" something happened.</p>
            
            <div class="example-box">
                <p><strong>Luettuani</strong> kirjan menin nukkumaan. (After I had read the book, I went to sleep.)</p>
                <p><strong>Syötyämme</strong> lähdimme kotiin. (After we had eaten, we went home.)</p>
                <p><strong>Tehtyään</strong> työnsä hän lähti. (After he/she had done his/her work, he/she left.)</p>
            </div>
            
            <h4>Reference Construction</h4>
            <p>The reference construction uses the active present participle in the partitive case + possessive suffix to express "while" or "during" an action.</p>
            
            <div class="example-box">
                <p><strong>Lukiessani</strong> kirjaa kuulin äänen. (While reading the book, I heard a sound.)</p>
                <p><strong>Puhuessaan</strong> suomea hän tekee virheitä. (While speaking Finnish, he/she makes mistakes.)</p>
                <p><strong>Syödessämme</strong> keskustelimme. (While eating, we conversed.)</p>
            </div>
            
            <h4>"On oltava" Construction</h4>
            <p>This construction uses "olla" + passive present participle to express necessity or obligation.</p>
            
            <div class="example-box">
                <p>Minun on <strong>mentävä</strong>. (I must go.)</p>
                <p>Sinun on <strong>luettava</strong> tämä kirja. (You must read this book.)</p>
                <p>Hänen on <strong>tehtävä</strong> työnsä. (He/she must do his/her work.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Summary of Finnish Participles</h3>
            
            <table class="example-table">
                <tr>
                    <th>Participle Type</th>
                    <th>Formation</th>
                    <th>Example</th>
                    <th>Usage</th>
                </tr>
                <tr>
                    <td>Active Present</td>
                    <td>stem + -va/-vä</td>
                    <td>puhuva (speaking)</td>
                    <td>Describes ongoing actions</td>
                </tr>
                <tr>
                    <td>Passive Present</td>
                    <td>stem + -ttava/-ttävä</td>
                    <td>puhuttava (to be spoken)</td>
                    <td>Describes necessity or what should be done</td>
                </tr>
                <tr>
                    <td>Active Past</td>
                    <td>stem + -nut/-nyt</td>
                    <td>puhunut (having spoken)</td>
                    <td>Describes completed actions, forms perfect tenses</td>
                </tr>
                <tr>
                    <td>Passive Past</td>
                    <td>stem + -ttu/-tty</td>
                    <td>puhuttu (spoken)</td>
                    <td>Describes completed passive actions</td>
                </tr>
                <tr>
                    <td>Agent</td>
                    <td>stem + -ma/-mä</td>
                    <td>kirjoittama (written by)</td>
                    <td>Indicates who performed an action</td>
                </tr>
                <tr>
                    <td>Negative</td>
                    <td>stem + -maton/-mätön</td>
                    <td>lukematon (unread)</td>
                    <td>Describes not having done something</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Participle Type</th>
                </tr>
                <tr>
                    <td>Puhuva mies on opettaja.</td>
                    <td>The speaking man is a teacher.</td>
                    <td>Active Present</td>
                </tr>
                <tr>
                    <td>Tämä on luettava kirja.</td>
                    <td>This is a book that should be read.</td>
                    <td>Passive Present</td>
                </tr>
                <tr>
                    <td>Eilen tullut vieras on vielä täällä.</td>
                    <td>The guest who came yesterday is still here.</td>
                    <td>Active Past</td>
                </tr>
                <tr>
                    <td>Työ on tehty.</td>
                    <td>The work is done.</td>
                    <td>Passive Past</td>
                </tr>
                <tr>
                    <td>Tämä on Mikan kirjoittama kirja.</td>
                    <td>This is a book written by Mika.</td>
                    <td>Agent</td>
                </tr>
                <tr>
                    <td>Pöydällä on lukematon kirja.</td>
                    <td>There is an unread book on the table.</td>
                    <td>Negative</td>
                </tr>
                <tr>
                    <td>Olen puhunut suomea kaksi vuotta.</td>
                    <td>I have spoken Finnish for two years.</td>
                    <td>Active Past (Perfect Tense)</td>
                </tr>
                <tr>
                    <td>Suomea on puhuttu täällä pitkään.</td>
                    <td>Finnish has been spoken here for a long time.</td>
                    <td>Passive Past (Passive Perfect)</td>
                </tr>
                <tr>
                    <td>Luettuani kirjan menin nukkumaan.</td>
                    <td>After I had read the book, I went to sleep.</td>
                    <td>Temporal Construction</td>
                </tr>
                <tr>
                    <td>Lukiessani kirjaa kuulin äänen.</td>
                    <td>While reading the book, I heard a sound.</td>
                    <td>Reference Construction</td>
                </tr>
                <tr>
                    <td>Minun on mentävä.</td>
                    <td>I must go.</td>
                    <td>"On oltava" Construction</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















