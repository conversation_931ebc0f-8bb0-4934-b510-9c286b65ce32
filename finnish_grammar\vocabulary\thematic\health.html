﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health and Body - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .body-diagram {
            max-width: 600px;
            margin: 20px auto;
            text-align: center;
        }
        
        .body-diagram img {
            max-width: 100%;
            height: auto;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="health-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="health-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="health-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Health and Body</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Health and Body Vocabulary in Finnish</h2>
            <p>This page covers essential Finnish vocabulary related to health, body parts, medical care, and wellness. Whether you need to describe symptoms to a doctor, discuss health concerns, or talk about fitness, these terms will help you communicate effectively about health-related topics in Finnish.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Body Parts</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>keho</td>
                    <td class="pronunciation">ke-ho</td>
                    <td>body</td>
                </tr>
                <tr>
                    <td>vartalo</td>
                    <td class="pronunciation">var-ta-lo</td>
                    <td>body (physique)</td>
                </tr>
                <tr>
                    <td>pää</td>
                    <td class="pronunciation">pää</td>
                    <td>head</td>
                </tr>
                <tr>
                    <td>kasvot</td>
                    <td class="pronunciation">kas-vot</td>
                    <td>face</td>
                </tr>
                <tr>
                    <td>hiukset</td>
                    <td class="pronunciation">hiuk-set</td>
                    <td>hair (on head)</td>
                </tr>
                <tr>
                    <td>silmä</td>
                    <td class="pronunciation">sil-mä</td>
                    <td>eye</td>
                </tr>
                <tr>
                    <td>korva</td>
                    <td class="pronunciation">kor-va</td>
                    <td>ear</td>
                </tr>
                <tr>
                    <td>nenä</td>
                    <td class="pronunciation">ne-nä</td>
                    <td>nose</td>
                </tr>
                <tr>
                    <td>suu</td>
                    <td class="pronunciation">suu</td>
                    <td>mouth</td>
                </tr>
                <tr>
                    <td>huuli</td>
                    <td class="pronunciation">huu-li</td>
                    <td>lip</td>
                </tr>
                <tr>
                    <td>hampaat</td>
                    <td class="pronunciation">ham-paat</td>
                    <td>teeth</td>
                </tr>
                <tr>
                    <td>kieli</td>
                    <td class="pronunciation">kie-li</td>
                    <td>tongue</td>
                </tr>
                <tr>
                    <td>leuka</td>
                    <td class="pronunciation">leu-ka</td>
                    <td>chin, jaw</td>
                </tr>
                <tr>
                    <td>kaula</td>
                    <td class="pronunciation">kau-la</td>
                    <td>neck</td>
                </tr>
                <tr>
                    <td>olkapää</td>
                    <td class="pronunciation">ol-ka-pää</td>
                    <td>shoulder</td>
                </tr>
                <tr>
                    <td>käsivarsi</td>
                    <td class="pronunciation">kä-si-var-si</td>
                    <td>arm</td>
                </tr>
                <tr>
                    <td>kyynärpää</td>
                    <td class="pronunciation">kyy-när-pää</td>
                    <td>elbow</td>
                </tr>
                <tr>
                    <td>ranne</td>
                    <td class="pronunciation">ran-ne</td>
                    <td>wrist</td>
                </tr>
                <tr>
                    <td>käsi</td>
                    <td class="pronunciation">kä-si</td>
                    <td>hand</td>
                </tr>
                <tr>
                    <td>sormi</td>
                    <td class="pronunciation">sor-mi</td>
                    <td>finger</td>
                </tr>
                <tr>
                    <td>peukalo</td>
                    <td class="pronunciation">peu-ka-lo</td>
                    <td>thumb</td>
                </tr>
                <tr>
                    <td>kynsi</td>
                    <td class="pronunciation">kyn-si</td>
                    <td>nail</td>
                </tr>
                <tr>
                    <td>rinta</td>
                    <td class="pronunciation">rin-ta</td>
                    <td>chest</td>
                </tr>
                <tr>
                    <td>selkä</td>
                    <td class="pronunciation">sel-kä</td>
                    <td>back</td>
                </tr>
                <tr>
                    <td>vatsa</td>
                    <td class="pronunciation">vat-sa</td>
                    <td>stomach (external)</td>
                </tr>
                <tr>
                    <td>napa</td>
                    <td class="pronunciation">na-pa</td>
                    <td>navel, belly button</td>
                </tr>
                <tr>
                    <td>lantio</td>
                    <td class="pronunciation">lan-ti-o</td>
                    <td>hip</td>
                </tr>
                <tr>
                    <td>jalka</td>
                    <td class="pronunciation">jal-ka</td>
                    <td>leg, foot</td>
                </tr>
                <tr>
                    <td>reisi</td>
                    <td class="pronunciation">rei-si</td>
                    <td>thigh</td>
                </tr>
                <tr>
                    <td>polvi</td>
                    <td class="pronunciation">pol-vi</td>
                    <td>knee</td>
                </tr>
                <tr>
                    <td>nilkka</td>
                    <td class="pronunciation">nilk-ka</td>
                    <td>ankle</td>
                </tr>
                <tr>
                    <td>jalkaterä</td>
                    <td class="pronunciation">jal-ka-te-rä</td>
                    <td>foot</td>
                </tr>
                <tr>
                    <td>varvas</td>
                    <td class="pronunciation">var-vas</td>
                    <td>toe</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Minulla on kipua selässä.</p>
                <p><strong>English:</strong> I have pain in my back.</p>
                <p><strong>Finnish:</strong> Hän loukkasi polvensa urheillessa.</p>
                <p><strong>English:</strong> He/she injured his/her knee while playing sports.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Internal Organs and Body Systems</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>sydän</td>
                    <td class="pronunciation">sy-dän</td>
                    <td>heart</td>
                </tr>
                <tr>
                    <td>keuhkot</td>
                    <td class="pronunciation">keuh-kot</td>
                    <td>lungs</td>
                </tr>
                <tr>
                    <td>maksa</td>
                    <td class="pronunciation">mak-sa</td>
                    <td>liver</td>
                </tr>
                <tr>
                    <td>munuaiset</td>
                    <td class="pronunciation">mu-nu-ai-set</td>
                    <td>kidneys</td>
                </tr>
                <tr>
                    <td>mahalaukku</td>
                    <td class="pronunciation">ma-ha-lauk-ku</td>
                    <td>stomach (organ)</td>
                </tr>
                <tr>
                    <td>suolisto</td>
                    <td class="pronunciation">suo-lis-to</td>
                    <td>intestines</td>
                </tr>
                <tr>
                    <td>ohutsuoli</td>
                    <td class="pronunciation">o-hut-suo-li</td>
                    <td>small intestine</td>
                </tr>
                <tr>
                    <td>paksusuoli</td>
                    <td class="pronunciation">pak-su-suo-li</td>
                    <td>large intestine</td>
                </tr>
                <tr>
                    <td>aivot</td>
                    <td class="pronunciation">ai-vot</td>
                    <td>brain</td>
                </tr>
                <tr>
                    <td>veri</td>
                    <td class="pronunciation">ve-ri</td>
                    <td>blood</td>
                </tr>
                <tr>
                    <td>luu</td>
                    <td class="pronunciation">luu</td>
                    <td>bone</td>
                </tr>
                <tr>
                    <td>lihakset</td>
                    <td class="pronunciation">li-hak-set</td>
                    <td>muscles</td>
                </tr>
                <tr>
                    <td>nivel</td>
                    <td class="pronunciation">ni-vel</td>
                    <td>joint</td>
                </tr>
                <tr>
                    <td>verisuoni</td>
                    <td class="pronunciation">ve-ri-suo-ni</td>
                    <td>blood vessel</td>
                </tr>
                <tr>
                    <td>hermo</td>
                    <td class="pronunciation">her-mo</td>
                    <td>nerve</td>
                </tr>
                <tr>
                    <td>iho</td>
                    <td class="pronunciation">i-ho</td>
                    <td>skin</td>
                </tr>
                <tr>
                    <td>luusto</td>
                    <td class="pronunciation">luus-to</td>
                    <td>skeleton</td>
                </tr>
                <tr>
                    <td>ruoansulatusjärjestelmä</td>
                    <td class="pronunciation">ruo-an-su-la-tus-jär-jes-tel-mä</td>
                    <td>digestive system</td>
                </tr>
                <tr>
                    <td>verenkiertojärjestelmä</td>
                    <td class="pronunciation">ve-ren-kier-to-jär-jes-tel-mä</td>
                    <td>circulatory system</td>
                </tr>
                <tr>
                    <td>hermosto</td>
                    <td class="pronunciation">her-mos-to</td>
                    <td>nervous system</td>
                </tr>
                <tr>
                    <td>immuunijärjestelmä</td>
                    <td class="pronunciation">im-muu-ni-jär-jes-tel-mä</td>
                    <td>immune system</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Health Conditions and Symptoms</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>kipu</td>
                    <td class="pronunciation">ki-pu</td>
                    <td>pain</td>
                </tr>
                <tr>
                    <td>särky</td>
                    <td class="pronunciation">sär-ky</td>
                    <td>ache</td>
                </tr>
                <tr>
                    <td>päänsärky</td>
                    <td class="pronunciation">pään-sär-ky</td>
                    <td>headache</td>
                </tr>
                <tr>
                    <td>hammassärky</td>
                    <td class="pronunciation">ham-mas-sär-ky</td>
                    <td>toothache</td>
                </tr>
                <tr>
                    <td>vatsakipu</td>
                    <td class="pronunciation">vat-sa-ki-pu</td>
                    <td>stomach ache</td>
                </tr>
                <tr>
                    <td>selkäkipu</td>
                    <td class="pronunciation">sel-kä-ki-pu</td>
                    <td>back pain</td>
                </tr>
                <tr>
                    <td>kuume</td>
                    <td class="pronunciation">kuu-me</td>
                    <td>fever</td>
                </tr>
                <tr>
                    <td>flunssa</td>
                    <td class="pronunciation">fluns-sa</td>
                    <td>cold (illness)</td>
                </tr>
                <tr>
                    <td>yskä</td>
                    <td class="pronunciation">ys-kä</td>
                    <td>cough</td>
                </tr>
                <tr>
                    <td>nuha</td>
                    <td class="pronunciation">nu-ha</td>
                    <td>runny nose</td>
                </tr>
                <tr>
                    <td>kurkkukipu</td>
                    <td class="pronunciation">kurk-ku-ki-pu</td>
                    <td>sore throat</td>
                </tr>
                <tr>
                    <td>pahoinvointi</td>
                    <td class="pronunciation">pa-hoin-voin-ti</td>
                    <td>nausea</td>
                </tr>
                <tr>
                    <td>oksentaa</td>
                    <td class="pronunciation">ok-sen-taa</td>
                    <td>to vomit</td>
                </tr>
                <tr>
                    <td>ripuli</td>
                    <td class="pronunciation">ri-pu-li</td>
                    <td>diarrhea</td>
                </tr>
                <tr>
                    <td>ummetus</td>
                    <td class="pronunciation">um-me-tus</td>
                    <td>constipation</td>
                </tr>
                <tr>
                    <td>huimaus</td>
                    <td class="pronunciation">hui-ma-us</td>
                    <td>dizziness</td>
                </tr>
                <tr>
                    <td>väsymys</td>
                    <td class="pronunciation">vä-sy-mys</td>
                    <td>fatigue, tiredness</td>
                </tr>
                <tr>
                    <td>ihottuma</td>
                    <td class="pronunciation">i-hot-tu-ma</td>
                    <td>rash</td>
                </tr>
                <tr>
                    <td>allergia</td>
                    <td class="pronunciation">al-ler-gi-a</td>
                    <td>allergy</td>
                </tr>
                <tr>
                    <td>haava</td>
                    <td class="pronunciation">haa-va</td>
                    <td>wound</td>
                </tr>
                <tr>
                    <td>murtuma</td>
                    <td class="pronunciation">mur-tu-ma</td>
                    <td>fracture</td>
                </tr>
                <tr>
                    <td>nyrjähdys</td>
                    <td class="pronunciation">nyr-jäh-dys</td>
                    <td>sprain</td>
                </tr>
                <tr>
                    <td>verenvuoto</td>
                    <td class="pronunciation">ve-ren-vuo-to</td>
                    <td>bleeding</td>
                </tr>
                <tr>
                    <td>tulehdus</td>
                    <td class="pronunciation">tu-leh-dus</td>
                    <td>inflammation, infection</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>At the doctor's office:</strong></p>
                <p><strong>Lääkäri:</strong> Mitä oireita sinulla on?</p>
                <p><strong>Potilas:</strong> Minulla on korkea kuume, yskä ja kurkkukipu.</p>
                <p><strong>Lääkäri:</strong> Kuinka kauan oireet ovat jatkuneet?</p>
                <p><strong>Potilas:</strong> Noin kolme päivää.</p>
                <p><em>Translation:</em></p>
                <p><strong>Doctor:</strong> What symptoms do you have?</p>
                <p><strong>Patient:</strong> I have a high fever, cough, and sore throat.</p>
                <p><strong>Doctor:</strong> How long have the symptoms continued?</p>
                <p><strong>Patient:</strong> About three days.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Medical Care and Professionals</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>lääkäri</td>
                    <td class="pronunciation">lää-kä-ri</td>
                    <td>doctor</td>
                </tr>
                <tr>
                    <td>sairaanhoitaja</td>
                    <td class="pronunciation">sai-raan-hoi-ta-ja</td>
                    <td>nurse</td>
                </tr>
                <tr>
                    <td>hammaslääkäri</td>
                    <td class="pronunciation">ham-mas-lää-kä-ri</td>
                    <td>dentist</td>
                </tr>
                <tr>
                    <td>fysioterapeutti</td>
                    <td class="pronunciation">fy-sio-te-ra-peut-ti</td>
                    <td>physiotherapist</td>
                </tr>
                <tr>
                    <td>psykologi</td>
                    <td class="pronunciation">psy-ko-lo-gi</td>
                    <td>psychologist</td>
                </tr>
                <tr>
                    <td>apteekkari</td>
                    <td class="pronunciation">ap-teek-ka-ri</td>
                    <td>pharmacist</td>
                </tr>
                <tr>
                    <td>sairaala</td>
                    <td class="pronunciation">sai-raa-la</td>
                    <td>hospital</td>
                </tr>
                <tr>
                    <td>terveyskeskus</td>
                    <td class="pronunciation">ter-ve-ys-kes-kus</td>
                    <td>health center</td>
                </tr>
                <tr>
                    <td>apteekki</td>
                    <td class="pronunciation">ap-teek-ki</td>
                    <td>pharmacy</td>
                </tr>
                <tr>
                    <td>ensiapuasema</td>
                    <td class="pronunciation">en-si-a-pu-a-se-ma</td>
                    <td>emergency room</td>
                </tr>
                <tr>
                    <td>vastaanotto</td>
                    <td class="pronunciation">vas-taan-ot-to</td>
                    <td>reception, doctor's office</td>
                </tr>
                <tr>
                    <td>ajanvaraus</td>
                    <td class="pronunciation">a-jan-va-ra-us</td>
                    <td>appointment booking</td>
                </tr>
                <tr>
                    <td>lääkärintarkastus</td>
                    <td class="pronunciation">lää-kä-rin-tar-kas-tus</td>
                    <td>medical examination</td>
                </tr>
                <tr>
                    <td>lääke</td>
                    <td class="pronunciation">lää-ke</td>
                    <td>medicine</td>
                </tr>
                <tr>
                    <td>resepti</td>
                    <td class="pronunciation">re-sep-ti</td>
                    <td>prescription</td>
                </tr>
                <tr>
                    <td>rokote</td>
                    <td class="pronunciation">ro-ko-te</td>
                    <td>vaccine</td>
                </tr>
                <tr>
                    <td>leikkaus</td>
                    <td class="pronunciation">leik-ka-us</td>
                    <td>surgery</td>
                </tr>
                <tr>
                    <td>hoito</td>
                    <td class="pronunciation">hoi-to</td>
                    <td>treatment, care</td>
                </tr>
                <tr>
                    <td>kuntoutus</td>
                    <td class="pronunciation">kun-tou-tus</td>
                    <td>rehabilitation</td>
                </tr>
                <tr>
                    <td>ensiapu</td>
                    <td class="pronunciation">en-si-a-pu</td>
                    <td>first aid</td>
                </tr>
                <tr>
                    <td>ambulanssi</td>
                    <td class="pronunciation">am-bu-lans-si</td>
                    <td>ambulance</td>
                </tr>
                <tr>
                    <td>hätänumero</td>
                    <td class="pronunciation">hä-tä-nu-me-ro</td>
                    <td>emergency number</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finland, the emergency number is 112. The public healthcare system is called "julkinen terveydenhuolto" and is available to all residents. For non-urgent health issues, you should first contact your local health center (terveyskeskus).</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Wellness and Healthy Lifestyle</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>terveys</td>
                    <td class="pronunciation">ter-ve-ys</td>
                    <td>health</td>
                </tr>
                <tr>
                    <td>hyvinvointi</td>
                    <td class="pronunciation">hy-vin-voin-ti</td>
                    <td>wellbeing</td>
                </tr>
                <tr>
                    <td>terveellinen</td>
                    <td class="pronunciation">ter-veel-li-nen</td>
                    <td>healthy</td>
                </tr>
                <tr>
                    <td>epäterveellinen</td>
                    <td class="pronunciation">e-pä-ter-veel-li-nen</td>
                    <td>unhealthy</td>
                </tr>
                <tr>
                    <td>liikunta</td>
                    <td class="pronunciation">lii-kun-ta</td>
                    <td>exercise, physical activity</td>
                </tr>
                <tr>
                    <td>urheilu</td>
                    <td class="pronunciation">ur-hei-lu</td>
                    <td>sports</td>
                </tr>
                <tr>
                    <td>kuntosali</td>
                    <td class="pronunciation">kun-to-sa-li</td>
                    <td>gym</td>
                </tr>
                <tr>
                    <td>jooga</td>
                    <td class="pronunciation">joo-ga</td>
                    <td>yoga</td>
                </tr>
                <tr>
                    <td>venyttely</td>
                    <td class="pronunciation">ve-nyt-te-ly</td>
                    <td>stretching</td>
                </tr>
                <tr>
                    <td>kävely</td>
                    <td class="pronunciation">kä-ve-ly</td>
                    <td>walking</td>
                </tr>
                <tr>
                    <td>juoksu</td>
                    <td class="pronunciation">juok-su</td>
                    <td>running</td>
                </tr>
                <tr>
                    <td>uinti</td>
                    <td class="pronunciation">uin-ti</td>
                    <td>swimming</td>
                </tr>
                <tr>
                    <td>pyöräily</td>
                    <td class="pronunciation">pyö-räi-ly</td>
                    <td>cycling</td>
                </tr>
                <tr>
                    <td>ravinto</td>
                    <td class="pronunciation">ra-vin-to</td>
                    <td>nutrition</td>
                </tr>
                <tr>
                    <td>ruokavalio</td>
                    <td class="pronunciation">ruo-ka-va-li-o</td>
                    <td>diet</td>
                </tr>
                <tr>
                    <td>vitamiini</td>
                    <td class="pronunciation">vi-ta-mii-ni</td>
                    <td>vitamin</td>
                </tr>
                <tr>
                    <td>proteiini</td>
                    <td class="pronunciation">pro-te-ii-ni</td>
                    <td>protein</td>
                </tr>
                <tr>
                    <td>hiilihydraatti</td>
                    <td class="pronunciation">hii-li-hyd-raat-ti</td>
                    <td>carbohydrate</td>
                </tr>
                <tr>
                    <td>rasva</td>
                    <td class="pronunciation">ras-va</td>
                    <td>fat</td>
                </tr>
                <tr>
                    <td>uni</td>
                    <td class="pronunciation">u-ni</td>
                    <td>sleep</td>
                </tr>
                <tr>
                    <td>lepo</td>
                    <td class="pronunciation">le-po</td>
                    <td>rest</td>
                </tr>
                <tr>
                    <td>stressi</td>
                    <td class="pronunciation">stres-si</td>
                    <td>stress</td>
                </tr>
                <tr>
                    <td>rentoutuminen</td>
                    <td class="pronunciation">ren-tou-tu-mi-nen</td>
                    <td>relaxation</td>
                </tr>
                <tr>
                    <td>meditaatio</td>
                    <td class="pronunciation">me-di-taa-ti-o</td>
                    <td>meditation</td>
                </tr>
                <tr>
                    <td>sauna</td>
                    <td class="pronunciation">sau-na</td>
                    <td>sauna</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Yritän elää terveellisesti. Syön paljon vihanneksia, liikun säännöllisesti ja nukun riittävästi.</p>
                <p><strong>English:</strong> I try to live healthily. I eat a lot of vegetables, exercise regularly, and sleep enough.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Useful Phrases for Medical Situations</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Haluaisin varata ajan lääkärille.</td>
                    <td>I would like to book an appointment with a doctor.</td>
                </tr>
                <tr>
                    <td>Minulla on kipua täällä.</td>
                    <td>I have pain here.</td>
                </tr>
                <tr>
                    <td>Olen sairas.</td>
                    <td>I am sick.</td>
                </tr>
                <tr>
                    <td>Minulla on flunssa/kuume.</td>
                    <td>I have a cold/fever.</td>
                </tr>
                <tr>
                    <td>Tarvitsen lääkäriä.</td>
                    <td>I need a doctor.</td>
                </tr>
                <tr>
                    <td>Onko teillä särkylääkettä?</td>
                    <td>Do you have pain medication?</td>
                </tr>
                <tr>
                    <td>Olen allerginen...</td>
                    <td>I am allergic to...</td>
                </tr>
                <tr>
                    <td>Minulla on lääkeresepti.</td>
                    <td>I have a prescription.</td>
                </tr>
                <tr>
                    <td>Kuinka usein minun pitää ottaa tätä lääkettä?</td>
                    <td>How often should I take this medicine?</td>
                </tr>
                <tr>
                    <td>Onko tällä lääkkeellä sivuvaikutuksia?</td>
                    <td>Does this medicine have side effects?</td>
                </tr>
                <tr>
                    <td>Milloin minun pitäisi tulla uudestaan?</td>
                    <td>When should I come back?</td>
                </tr>
                <tr>
                    <td>Tarvitsen sairaslomaa.</td>
                    <td>I need sick leave.</td>
                </tr>
                <tr>
                    <td>Tämä on hätätilanne!</td>
                    <td>This is an emergency!</td>
                </tr>
                <tr>
                    <td>Soittakaa ambulanssi!</td>
                    <td>Call an ambulance!</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















