/**
 * YouTube Channels Configuration
 * Define all YouTube channels used in the application
 */

// YouTube channels configuration
// add 1 channel we will follow the below steps:
// Step 1: update list trong youtube-core.js file
// Step 2: update the files:  audio.html, game/index.html, index.html(home page), video.html, chapter.html and finnish_grammar (index.html)
// Step 3: update all files childs của finnish_grammar (reference script to update faster)
// Step 4: update all files childs của categories (reference script to update faster)
// Step 5: update all files childs của game

window.YOUTUBE_CHANNELS = {
  // Kuulostaa Hyvältä channel
  kuulostaahyvalta: {
    id: "UC3-GSYn3Rz-LJ1UDzOjInNA",
    name: "Kuulostaa Hyvältä",
    playlist: "PLhnT-lPAWRlSFp9ucM1USmIWz0iia2O6x",
    description: "Learn Finnish language with Kuulostaa Hyvältä",
    tabId: "kuulostaa-hyvalta-tab",
  },

  // Finnish Crash Course channel
  finnishcrashcourse: {
    id: "UCPdUhp3L-Rxp_wigblquLtA",
    name: "Finnish Crash Course",
    playlist: "",
    description: "Finnish language lessons for beginners",
    tabId: "finnish-crash-course-tab",
  },

  // Finnish To Go channel
  finnishtogo: {
    id: "UCAPUmsVfTtiowWNSPQR8zeQ",
    name: "Finnish To Go",
    playlist: "",
    description: "Learn Finnish on the go",
    tabId: "finnish-to-go-tab",
    handle: "@finnishtogo",
    // Add a direct YouTube URL for fallback
    youtubeUrl: "https://www.youtube.com/@finnishtogo",
  },

  // Suomen Kurssi channel
  suomenkurssiyt: {
    id: "UC3HqCHmhvyJPAyUc-VVUJwQ",
    name: "Suomen Kurssi",
    playlist: "",
    description: "Finnish language course videos",
    tabId: "suomen-kurssi-tab",
    handle: "@SuomenKurssi",
    // Add a direct YouTube URL for fallback
    youtubeUrl: "https://www.youtube.com/@SuomenKurssi",
  },

  // Yle Areena channel 1
  yleareena: {
    id: "UCgzeiUSWTIkwP-pHMz-DCTg",
    name: "Yle Areena 1",
    playlist: "PLiD3QzkMNBd2_jQ9Ubsh-lFJ0ULKybvpu",
    description: "Finnish national broadcasting company's content - Playlist 1",
    tabId: "yle-areena-tab",
    handle: "@yleareena",
    youtubeUrl: "https://www.youtube.com/@yleareena",
  },

  // Yle Areena channel 2
  yleareena2: {
    id: "UCgzeiUSWTIkwP-pHMz-DCTg",
    name: "Yle Areena 2",
    playlist: "PLiD3QzkMNBd1pSjGRhCgvKKhKhSjrqMmT",
    description: "Finnish national broadcasting company's content - Playlist 2",
    tabId: "yle-areena-tab-2",
    handle: "@yleareena",
    youtubeUrl: "https://www.youtube.com/@yleareena",
  },

  // Yle Areena channel 3
  yleareena3: {
    id: "UCgzeiUSWTIkwP-pHMz-DCTg",
    name: "Yle Areena 3",
    playlist: "PLiD3QzkMNBd05cCRHClOI6m6ifUmX5oae",
    description: "Finnish national broadcasting company's content - Playlist 3",
    tabId: "yle-areena-tab-3",
    handle: "@yleareena",
    youtubeUrl: "https://www.youtube.com/@yleareena",
  },

  // Yle Areena channel 4
  yleareena4: {
    id: "UCgzeiUSWTIkwP-pHMz-DCTg",
    name: "Yle Areena 4",
    playlist: "PLiD3QzkMNBd17nwr0NN9yKmNS3zqgsxKb",
    description: "Finnish national broadcasting company's content - Playlist 4",
    tabId: "yle-areena-tab-4",
    handle: "@yleareena",
    youtubeUrl: "https://www.youtube.com/@yleareena",
  },

  // Yle Areena channel 5
  yleareena5: {
    id: "UCgzeiUSWTIkwP-pHMz-DCTg",
    name: "Yle Areena 5",
    playlist: "PLiD3QzkMNBd2RhO13mo9_yTYDJg5qVqlE",
    description: "Finnish national broadcasting company's content - Playlist 5",
    tabId: "yle-areena-tab-5",
    handle: "@yleareena",
    youtubeUrl: "https://www.youtube.com/@yleareena",
  },

  // Pipsa Possu Suomi Virallinen channel
  pipsapossu: {
    id: "UCNyz-57mHiPwW2I1Z43zmsQ",
    name: "Pipsa Possu Suomi Virallinen",
    playlist: "",
    description:
      "Tervetuloa viralliselle Pipsa Possun kanavalle ja Pipsan kotiin YouTubessa! Olemme luoneet Pipsan maailman, jossa on jaksoja sekä koosteita, jotka pitävät uskollisimmatkin Pipsan fanit tyytyväisinä.",
    tabId: "pipsa-possu-tab",
    handle: "@pipsapossusuomivirallinen",
    youtubeUrl: "https://www.youtube.com/@pipsapossusuomivirallinen",
  },

  // KatChats Finnish channel
  katchatsfinnish: {
    id: "UCKDSMJAKwHWEIY5YzhWyd_Q",
    name: "KatChats Finnish",
    playlist: "",
    description:
      "This channel is dedicated to making videos where I attempt to introduce you guys to the Finnish language. I am 100% up for suggestions, if there is something specific you want to learn in Finnish or are having a hard time with.",
    tabId: "katchats-finnish-tab",
    handle: "@katchatsfinnish",
    youtubeUrl: "https://www.youtube.com/@katchatsfinnish",
  },

  // Kaapo - WildBrain channel 1
  kaapowildbrain: {
    id: "UCHMYWV3d5R6UvFmj48yVYCg",
    name: "Kaapo - WildBrain 1",
    playlist: "PLekWYwscVA-HxkeaYzKFAsXp-Fw3IhHBt",
    description:
      "Finnish children's content from Kaapo - WildBrain - Playlist 1",
    tabId: "kaapo-wildbrain-tab",
    handle: "@kaapowildbrain",
    youtubeUrl: "https://www.youtube.com/@kaapowildbrain",
  },

  // Kaapo - WildBrain channel 2
  kaapowildbrain2: {
    id: "UCHMYWV3d5R6UvFmj48yVYCg",
    name: "Kaapo - WildBrain 2",
    playlist: "PLekWYwscVA-FKybMz18Cj3vjSn2smG4gh",
    description:
      "Finnish children's content from Kaapo - WildBrain - Playlist 2",
    tabId: "kaapo-wildbrain-tab-2",
    handle: "@kaapowildbrain",
    youtubeUrl: "https://www.youtube.com/@kaapowildbrain",
  },

  // Kaapo - WildBrain channel 3
  kaapowildbrain3: {
    id: "UCHMYWV3d5R6UvFmj48yVYCg",
    name: "Kaapo - WildBrain 3",
    playlist: "PLekWYwscVA-H4ZBiA1M8eVdQZGRPuX8Ec",
    description:
      "Finnish children's content from Kaapo - WildBrain - Playlist 3",
    tabId: "kaapo-wildbrain-tab-3",
    handle: "@kaapowildbrain",
    youtubeUrl: "https://www.youtube.com/@kaapowildbrain",
  },

  // Kaapo - WildBrain channel 4
  kaapowildbrain4: {
    id: "UCHMYWV3d5R6UvFmj48yVYCg",
    name: "Kaapo - WildBrain 4",
    playlist: "PLekWYwscVA-FMUQjtTVF87SUaihtQ-PEd",
    description:
      "Finnish children's content from Kaapo - WildBrain - Playlist 4",
    tabId: "kaapo-wildbrain-tab-4",
    handle: "@kaapowildbrain",
    youtubeUrl: "https://www.youtube.com/@kaapowildbrain",
  },
};

// You can add more channels by following the same structure
// Example:
/*
window.YOUTUBE_CHANNELS.newchannel = {
    id: "CHANNEL_ID",
    name: "Channel Name",
    playlist: "PLAYLIST_ID", // Optional
    description: "Channel description",
    tabId: "channel-tab-id",
    handle: "@channelhandle", // Optional
    youtubeUrl: "https://www.youtube.com/@channelhandle", // Optional
};
*/

