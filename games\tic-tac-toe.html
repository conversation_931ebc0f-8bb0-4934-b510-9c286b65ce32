﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> (Gomoku / Caro) - <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../styles.css">
    <style>
        /* Mobile menu specific styles */
        @media (max-width: 767px) {
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            .dark-mode .nav-links.show {
                background-color: #252525 !important;
            }
            
            /* Dropdown styles for mobile */
            .dropdown-content {
                display: none !important;
            }
            
            .dropdown.active .dropdown-content {
                display: block !important;
                position: static !important;
                width: 100% !important;
                background-color: rgba(0, 0, 0, 0.03) !important;
                box-shadow: none !important;
                border-top: 1px solid #eee !important;
            }
            
            .dark-mode .dropdown.active .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05) !important;
                border-top: 1px solid #333 !important;
            }
        }
        
        /* Game specific styles */
        .game-container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-wrap: wrap;
        }
        
        .dark-mode .game-container {
            background-color: #1e1e1e;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .game-container .intro {
            width: 100%;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eaeaea;
        }
        
        .dark-mode .game-container .intro {
            border-bottom: 1px solid #333;
        }
        
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 15px;
            position: relative;
        }
        
        .game-title {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .game-sidebar {
            flex: 0 0 250px;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        
        .game-sidebar.left {
            border-right: 1px solid #eaeaea;
        }
        
        .game-sidebar.right {
            border-left: 1px solid #eaeaea;
        }
        
        .dark-mode .game-sidebar.left {
            border-right: 1px solid #333;
        }
        
        .dark-mode .game-sidebar.right {
            border-left: 1px solid #333;
        }
        
        .game-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 0 20px;
            min-width: 0; /* Important for flex items */
            position: relative;
            width: 100%;
        }
        
        .fullscreen-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 9999; /* Higher z-index to ensure it's above everything */
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 20px;
            overflow: auto;
            box-sizing: border-box;
        }

        .fullscreen-container.active {
            display: flex;
        }

        .fullscreen-controls {
            display: flex;
            gap: 15px;
            width: 100%;
            max-width: 600px;
            justify-content: center;
            align-items: center;
        }
        
        .fullscreen-score {
            display: flex;
            gap: 15px;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px 15px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .score-x {
            color: #e74c3c;
        }
        
        .score-o {
            color: #3498db;
        }

        .fullscreen-container .game-button {
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .fullscreen-container .game-button:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-3px);
        }

        @media (max-width: 600px) {
            .fullscreen-header {
                margin-top: 5px;
            }
            
            .fullscreen-controls {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 5px;
                max-width: 100%;
                padding: 0 10px;
            }
            
            .fullscreen-container .game-button {
                flex: 1;
                min-width: 80px;
                padding: 8px 5px;
                font-size: 0.8rem;
                margin-bottom: 0;
            }
            
            .fullscreen-score {
                padding: 6px 10px;
                font-size: 1rem;
                gap: 10px;
            }
        }
        
        .fullscreen-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            font-size: 1.2rem;
        }
        
        .fullscreen-container .exit-button {
            background-color: #e74c3c; /* Red color for better visibility */
            color: white;
            border: none;
        }
        
        .fullscreen-container .exit-button:hover {
            background-color: #c0392b; /* Darker red on hover */
        }
        
        .game-board-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: visible;
            box-sizing: border-box;
            padding: 0;
            position: relative;
        }
        
        .game-board {
            display: grid;
            grid-template-columns: repeat(20, minmax(0, 1fr));
            grid-gap: 1px;
            margin-bottom: 20px;
            width: 100%;
            max-width: 700px;
            aspect-ratio: 1;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            transform: none !important;
            transition: none;
            will-change: transform; /* Optimize for GPU acceleration */
            backface-visibility: hidden; /* Prevent flickering */
            -webkit-backface-visibility: hidden;
            -webkit-transform: translateZ(0) scale(1.0, 1.0);
            transform: translateZ(0) scale(1.0, 1.0);
            border: none;
            padding: 0;
        }

        .fullscreen-container .game-board {
            max-width: min(85vh, 85vw);
            margin-top: 10px;
            width: 100%;
            margin-left: auto;
            margin-right: auto;
            transform: none !important;
            transition: none;
            will-change: transform; /* Optimize for GPU acceleration */
            backface-visibility: hidden; /* Prevent flickering */
            -webkit-backface-visibility: hidden;
            -webkit-transform: translateZ(0) scale(1.0, 1.0);
            transform: translateZ(0) scale(1.0, 1.0);
            border: none;
            padding: 0;
            grid-template-columns: repeat(20, minmax(0, 1fr));
        }
        
        @media (min-height: 800px) {
            .fullscreen-container .game-board {
                margin-top: 30px;
            }
        }
        
        .fullscreen-header {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 10px;
            margin-bottom: 20px;
        }

        /* Removed fullscreen status styles as it's no longer used */
        
        .cell {
            width: 100%;
            aspect-ratio: 1;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: bold;
            cursor: pointer;
            border-radius: 2px;
            transition: background-color 0.2s ease;
            box-sizing: border-box;
            position: relative;
            will-change: transform; /* Optimize for GPU acceleration */
            backface-visibility: hidden; /* Prevent flickering */
            -webkit-backface-visibility: hidden;
            -webkit-transform: translateZ(0) scale(1.0, 1.0);
            transform: translateZ(0) scale(1.0, 1.0);
            border: none;
            padding: 0;
            margin: 0;
            min-width: 0;
            min-height: 0;
        }

        .fullscreen-container .cell {
            font-size: 1.2rem;
        }

        @media (max-width: 480px) {
            .cell {
                font-size: 0.7rem;
            }
        }
        
        /* Ensure grid displays correctly on all screen sizes */
        @media (min-width: 900px) {
            .game-board {
                max-width: 650px;
            }
        }
        
        @media (min-width: 1200px) {
            .game-board {
                max-width: 700px;
            }
        }
        
        @media (min-width: 1400px) {
            .game-board {
                max-width: 750px;
            }
        }
        
        .dark-mode .cell {
            background-color: #333;
            color: #fff;
        }
        
        .cell:hover {
            background-color: #e0e0e0;
            transform: none;
        }
        
        .dark-mode .cell:hover {
            background-color: #444;
            transform: none;
        }
        
        .cell.x {
            color: #e74c3c;
            font-weight: bold;
            transform: none !important;
        }

        .cell.o {
            color: #3498db;
            font-weight: bold;
            transform: none !important;
        }

        .fullscreen-container .cell.x {
            color: #e74c3c;
            font-weight: bold;
            transform: none !important;
        }

        .fullscreen-container .cell.o {
            color: #3498db;
            font-weight: bold;
            transform: none !important;
        }
        
        /* Game status has been removed */
        
        .score-board {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.2rem;
            width: 100%;
        }
        
        .score-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .dark-mode .score-item {
            background-color: #333;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .score-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .score-value {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .player-x .score-value {
            color: #e74c3c;
        }
        
        .player-o .score-value {
            color: #3498db;
        }
        
        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.85);
            color: white;
            padding: 20px 30px;
            border-radius: 12px;
            z-index: 9999;
            font-size: 1.4rem;
            font-weight: bold;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
            text-align: center;
            max-width: 80%;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transform-origin: center center;
            scale: 0.9;
        }
        
        .notification.show {
            opacity: 1;
            scale: 1;
        }
        
        /* Dark mode notification */
        .dark-mode .notification {
            background-color: rgba(40, 40, 40, 0.9);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.6);
            border: 2px solid rgba(255, 255, 255, 0.15);
        }
        
        /* Responsive notification */
        @media (max-width: 480px) {
            .notification {
                font-size: 1.2rem;
                padding: 15px 20px;
                max-width: 90%;
            }
        }
        
        .game-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
            width: 100%;
        }
        
        .game-button {
            padding: 12px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .game-button:hover {
            background-color: #002a66;
            transform: translateY(-2px);
        }
        
        .dark-mode .game-button:hover {
            background-color: #1a4a8f;
        }
        
        .game-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
            width: 100%;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            padding: 8px 0;
        }
        
        /* Center radio buttons on all screen sizes */
        @media (max-width: 900px) {
            .option-label {
                justify-content: center;
                text-align: center;
            }
            
            .option-label input[type="radio"] {
                margin-right: 5px;
            }
        }
        
        .difficulty-options {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eaeaea;
        }
        
        .dark-mode .difficulty-options {
            border-top: 1px solid #333;
        }
        
        .difficulty-options h4 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        @media (max-width: 900px) {
            .game-options h3, .difficulty-options h4 {
                text-align: center;
                width: 100%;
            }
        }
        
        /* Responsive adjustments */
        @media (max-width: 900px) {
            .game-container {
                flex-direction: column;
                padding: 15px;
            }
            
            .game-sidebar {
                flex: 0 0 auto;
                width: 100%;
                border: none !important;
                padding: 10px 0;
            }
            
            .game-sidebar.left {
                order: 1;
            }
            
            .game-main {
                order: 2;
                padding: 15px 0;
                width: 100%;
                box-sizing: border-box;
            }
            
            .game-sidebar.right {
                order: 3;
            }
            
            .score-board {
                flex-direction: row;
                justify-content: center;
            }
            
            .score-item {
                width: auto;
                flex: 1;
                max-width: 150px;
            }
            
            .game-controls {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .game-options {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .game-options h3, .game-options h4 {
                margin-bottom: 10px;
                width: 100%;
            }
            
            .difficulty-options {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 15px;
            }
            
            .option-label {
                margin: 5px 0;
                display: inline-flex;
                justify-content: center;
                width: auto;
            }
            
            .game-options, .difficulty-options {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .game-button {
                width: auto;
                flex: 1;
                min-width: 150px;
            }
            
            .game-board-container {
                width: 100%;
                max-width: 100%;
                overflow: hidden;
            }
            
            .game-board {
                max-width: 100%;
                width: 100%;
            }
        }
        
        @media (max-width: 480px) {
            .game-container {
                padding: 10px;
            }
            
            .game-board {
                grid-gap: 1px;
            }
            
            /* Remove unused game-status style */
            
            .game-title {
                font-size: 1.2rem;
            }
            
            .game-button {
                padding: 8px 12px;
                font-size: 0.9rem;
            }
            
            /* Improve mobile layout for game options */
            .game-options {
                padding: 0 5px;
            }
            
            .difficulty-options {
                padding-top: 15px;
                margin-top: 15px;
                border-top: 1px solid #eaeaea;
            }
            
            .dark-mode .difficulty-options {
                border-top: 1px solid #333;
            }
            
            .option-label {
                margin: 3px 0;
                font-size: 0.9rem;
                display: inline-flex;
                justify-content: center;
                width: auto;
            }
            
            .game-options h3, .game-options h4 {
                margin-bottom: 8px;
            }
        }
        
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        /* Finnish vocabulary section */
        .game-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 8px;
        }
        
        .dark-mode .game-info {
            background-color: #252525;
        }
        
        .game-info h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .game-info p {
            margin: 5px 0;
            font-size: 0.9rem;
        }
        
        .vocabulary-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f8f8;
            border-radius: 8px;
        }
        
        .dark-mode .vocabulary-section {
            background-color: #252525;
        }
        
        .vocabulary-title {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .vocabulary-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .vocabulary-item {
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .dark-mode .vocabulary-item {
            background-color: #333;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .finnish-word {
            font-weight: bold;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="game-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../index.html">Home</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content">
                        <a href="../video.html?channel=kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../video.html?channel=finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=yleareena">Yle Areena 1</a>
                        <a href="../video.html?channel=yleareena2">Yle Areena 2</a>
                        <a href="../video.html?channel=yleareena3">Yle Areena 3</a>
                        <a href="../video.html?channel=yleareena4">Yle Areena 4</a>
                        <a href="../video.html?channel=yleareena5">Yle Areena 5</a>
                        <a href="../video.html?channel=pipsapossu">Pipsa Possu</a>
                                                <a href="../video.html?channel=katchatsfinnish">KatChats Finnish</a>
                        <a href="../video.html?channel=kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../video.html?channel=kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../video.html?channel=kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../video.html?channel=kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../video.html?channel=ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../video.html?channel=ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../video.html?channel=ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../video.html?channel=ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../video.html?channel=ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../video.html?channel=ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../audio.html">Audio</a></li>
                <li><a href="../finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../index.html#daily-life">Daily Life</a>
                        <a href="../index.html#web-development">Web Development</a>
                        <a href="../index.html#cleaner">Cleaner</a>
                        <a href="../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="game-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <div class="game-container">
            <!-- Intro section removed and content relocated -->
            <!-- Left Sidebar -->
            <div class="game-sidebar left">
                <div class="game-options">
                    <h3>Game Mode</h3>
                    <label class="option-label">
                        <input type="radio" name="game-mode" value="computer" checked> Play vs Computer
                    </label>
                    <label class="option-label">
                        <input type="radio" name="game-mode" value="friend"> Play vs Friend
                    </label>
                    
                    <div id="difficulty-options" class="difficulty-options">
                        <h4>Difficulty Level</h4>
                        <label class="option-label">
                            <input type="radio" name="difficulty" value="easy" checked> Easy
                        </label>
                        <label class="option-label">
                            <input type="radio" name="difficulty" value="normal"> Normal
                        </label>
                        <label class="option-label">
                            <input type="radio" name="difficulty" value="hard"> Hard
                        </label>
                    </div>
                </div>
                
                <div class="score-board">
                    <div class="score-item player-x">
                        <div class="score-label">Player X</div>
                        <div class="score-value" id="score-x">0</div>
                    </div>
                    <div class="score-item player-o">
                        <div class="score-label">Player O</div>
                        <div class="score-value" id="score-o">0</div>
                    </div>
                </div>
                
                <a href="../games.html" class="back-link">
                    <i class="fas fa-arrow-left"></i> Takaisin peleihin (Back to Games)
                </a>
            </div>
            
            <!-- Main Game Area -->
            <div class="game-main">
                <div class="game-header">
                    <h2 class="game-title">Ristinolla (Gomoku / Caro)</h2>
                    <button class="fullscreen-button" id="fullscreen-button">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
                
                <div class="game-board-container">
                    <div class="game-board" id="game-board">
                        <!-- Cells will be generated by JavaScript -->
                    </div>
                </div>
                
                <div class="fullscreen-container" id="fullscreen-container">
                    <div class="fullscreen-header">
                        <div class="fullscreen-controls">
                            <button class="game-button" id="fullscreen-reset-button">
                                <i class="fas fa-redo"></i> Aloita uudelleen
                            </button>
                            <button class="game-button" id="fullscreen-reset-scores-button">
                                <i class="fas fa-eraser"></i> Nollaa pisteet
                            </button>
                            <div class="fullscreen-score">
                                <span class="score-x">X:<span id="fullscreen-score-x">0</span></span>
                                <span class="score-o">O:<span id="fullscreen-score-o">0</span></span>
                            </div>
                            <button class="game-button exit-button" id="exit-fullscreen-button">
                                <i class="fas fa-compress"></i> Sulje
                            </button>
                        </div>
                    </div>
                    
                    <div class="game-board" id="fullscreen-game-board">
                        <!-- Cells will be generated by JavaScript -->
                    </div>
                </div>
                
                <div id="notification" class="notification"></div>
            </div>
            
            <!-- Right Sidebar -->
            <div class="game-sidebar right">
                <h3>Game Controls</h3>
                <div class="game-controls">
                    <button class="game-button" id="reset-button">Aloita uudelleen (Restart)</button>
                    <button class="game-button" id="reset-scores-button">Nollaa pisteet (Reset Scores)</button>
                </div>
                
                <div class="game-info">
                    <h3>How to Play</h3>
                    <p>A classic game of Gomoku (Five in a Row), known as "Ristinolla" in Finnish. Get 5 in a row horizontally, vertically, or diagonally to win!</p>
                    <p>Play against the computer with different difficulty levels or challenge a friend. X goes first, then O.</p>
                </div>
            </div>
        </div>
        
        <div class="vocabulary-section">
            <h3 class="vocabulary-title">Finnish Vocabulary for Gomoku</h3>
            <div class="vocabulary-list">
                <div class="vocabulary-item">
                    <span class="finnish-word">Ristinolla</span> - Gomoku / Five in a Row
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Peli</span> - Game
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Pelata</span> - To play
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Voittaa</span> - To win
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Hävitä</span> - To lose
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Tasapeli</span> - Draw/Tie
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Vuoro</span> - Turn
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Aloita uudelleen</span> - Restart
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Opiskelen Suomea. All rights reserved.</p>
        </div>
    </footer>

    <script src="../script.js"></script>
    <script>
        // Initialize dark mode and mobile menu
        document.addEventListener("DOMContentLoaded", function() {
            // Mobile menu toggle functionality
            const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            const navLinks = document.getElementById("nav-links");

            console.log("Mobile menu toggle:", mobileMenuToggle);
            console.log("Nav links:", navLinks);

            if (mobileMenuToggle && navLinks) {
                console.log("Adding click event listener to mobile menu toggle");
                // Toggle mobile menu
                mobileMenuToggle.addEventListener("click", function() {
                    console.log("Mobile menu toggle clicked");
                    navLinks.classList.toggle("show");
                    this.classList.toggle("active");
                    console.log("Nav links show class:", navLinks.classList.contains("show"));
                });
            }

            // Check for saved dark mode preference
            const darkMode = localStorage.getItem("darkMode");
            
            // Apply dark mode if previously enabled
            if (darkMode === "enabled") {
                document.body.classList.add("dark-mode");
                
                // Update icon to sun
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Update mobile icon too
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            }
            
            // Set up dark mode toggle
            const darkModeToggle = document.getElementById("game-toggle-dark");
            if (darkModeToggle) {
                darkModeToggle.addEventListener("click", toggleDarkMode);
            }
            
            // Set up mobile dark mode toggle
            const mobileDarkModeToggle = document.getElementById("game-toggle-dark-mobile");
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.addEventListener("click", toggleDarkMode);
            }

            // Handle dropdown menus in mobile view
            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach((dropdown) => {
                const dropbtn = dropdown.querySelector(".dropbtn");
                if (dropbtn) {
                    dropbtn.addEventListener("click", function (e) {
                        // Only in mobile view
                        if (window.innerWidth <= 767) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log("Tic-tac-toe page - Dropdown button clicked:", dropbtn.textContent);

                            // Close other active dropdowns
                            dropdowns.forEach((otherDropdown) => {
                                if (
                                    otherDropdown !== dropdown &&
                                    otherDropdown.classList.contains("active")
                                ) {
                                    otherDropdown.classList.remove("active");
                                }
                            });

                            // Toggle current dropdown
                            dropdown.classList.toggle("active");
                            console.log("Tic-tac-toe page - Dropdown active:", dropdown.classList.contains("active"));
                        }
                    });
                }
            });
        });
        
        // Toggle dark mode function
        function toggleDarkMode() {
            document.body.classList.toggle("dark-mode");
            
            // Store preference in localStorage
            if (document.body.classList.contains("dark-mode")) {
                localStorage.setItem("darkMode", "enabled");
                
                // Change icon to sun
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Change mobile icon to sun
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            } else {
                localStorage.setItem("darkMode", "disabled");
                
                // Change icon back to moon
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-sun");
                        icon.classList.add("fa-moon");
                    }
                }
                
                // Change mobile icon back to moon
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-sun");
                        mobileIcon.classList.add("fa-moon");
                    }
                }
            }
        }

        // Gomoku (Five in a Row) Game Logic
        document.addEventListener('DOMContentLoaded', () => {
            const board = document.getElementById('game-board');
            // Game status element has been removed
            const status = { textContent: '' }; // Dummy object to avoid errors
            const resetButton = document.getElementById('reset-button');
            const gameModeRadios = document.querySelectorAll('input[name="game-mode"]');
            const scoreX = document.getElementById('score-x');
            const scoreO = document.getElementById('score-o');
            const notification = document.getElementById('notification');
            
            // Game configuration
            const BOARD_SIZE = 20; // 20x20 board
            const WIN_CONDITION = 5; // 5 in a row to win
            
            let currentPlayer = 'X';
            let gameActive = true;
            let gameState = Array(BOARD_SIZE * BOARD_SIZE).fill('');
            let gameMode = 'computer'; // Default game mode (matches the radio button value)
            let difficulty = 'easy'; // Default difficulty level
            let scores = { X: 0, O: 0 };
            
            // Finnish messages
            const messages = {
                playerXTurn: "X:n vuoro",
                playerOTurn: "O:n vuoro",
                playerXWin: "X voitti pelin!",
                playerOWin: "O voitti pelin!",
                draw: "Tasapeli!",
                gameStart: "Peli alkaa! (Game starts!)",
                playerXWinRound: "Pelaaja X voitti kierroksen! (Player X won the round!)",
                playerOWinRound: "Pelaaja O voitti kierroksen! (Player O won the round!)",
                drawRound: "Tasapeli! (It's a draw!)"
            };
            
            // Create the game board
            function createBoard() {
                board.innerHTML = '';
                for (let i = 0; i < BOARD_SIZE * BOARD_SIZE; i++) {
                    const cell = document.createElement('div');
                    cell.classList.add('cell');
                    cell.setAttribute('data-index', i);
                    
                    // Set inline styles to prevent any layout shifts
                    cell.style.transform = 'none';
                    cell.style.transition = 'none';
                    cell.style.willChange = 'transform';
                    cell.style.backfaceVisibility = 'hidden';
                    cell.style.webkitBackfaceVisibility = 'hidden';
                    cell.style.width = '100%';
                    cell.style.height = '100%';
                    cell.style.boxSizing = 'border-box';
                    cell.style.margin = '0';
                    cell.style.padding = '0';
                    cell.style.border = 'none';
                    
                    cell.addEventListener('click', cellClicked);
                    board.appendChild(cell);
                }
            }
            
            // Show notification
            function showNotification(message, duration = 2000) {
                // Clear any existing timeout
                if (window.notificationTimeout) {
                    clearTimeout(window.notificationTimeout);
                }
                
                // Remove existing show class if present
                notification.classList.remove('show');
                
                // Set the message
                notification.textContent = message;
                
                // Small delay before showing to ensure animation works properly
                setTimeout(() => {
                    notification.classList.add('show');
                    
                    // Set timeout to hide notification
                    window.notificationTimeout = setTimeout(() => {
                        notification.classList.remove('show');
                    }, duration);
                }, 50);
            }
            
            // Update scores
            function updateScores() {
                scoreX.textContent = scores.X;
                scoreO.textContent = scores.O;
                
                // Update fullscreen scores if they exist
                const fullscreenScoreX = document.getElementById('fullscreen-score-x');
                const fullscreenScoreO = document.getElementById('fullscreen-score-o');
                
                if (fullscreenScoreX) fullscreenScoreX.textContent = scores.X;
                if (fullscreenScoreO) fullscreenScoreO.textContent = scores.O;
            }
            
            // Initialize fullscreen mode
            function initFullscreenMode() {
                const fullscreenButton = document.getElementById('fullscreen-button');
                const exitFullscreenButton = document.getElementById('exit-fullscreen-button');
                const fullscreenContainer = document.getElementById('fullscreen-container');
                const fullscreenBoard = document.getElementById('fullscreen-game-board');
                
                // Enter fullscreen mode
                fullscreenButton.addEventListener('click', () => {
                    fullscreenContainer.classList.add('active');
                    
                    // Copy the current game state to the fullscreen board
                    updateFullscreenBoard();
                    
                    // Update the fullscreen scores
                    updateScores();
                    
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                    
                    // No need to hide intro section anymore as it's been relocated
                });
                
                // Exit fullscreen mode
                exitFullscreenButton.addEventListener('click', () => {
                    fullscreenContainer.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                    
                    // Update the main board to match the fullscreen board state
                    updateMainBoard();
                    
                    // No need to show intro section anymore as it's been relocated
                });
                
                // Create the fullscreen board
                function createFullscreenBoard() {
                    fullscreenBoard.innerHTML = '';
                    for (let i = 0; i < BOARD_SIZE * BOARD_SIZE; i++) {
                        const cell = document.createElement('div');
                        cell.classList.add('cell');
                        cell.setAttribute('data-index', i);
                        
                        // Set inline styles to prevent any layout shifts
                        cell.style.transform = 'none';
                        cell.style.transition = 'none';
                        cell.style.willChange = 'transform';
                        cell.style.backfaceVisibility = 'hidden';
                        cell.style.webkitBackfaceVisibility = 'hidden';
                        cell.style.width = '100%';
                        cell.style.height = '100%';
                        cell.style.boxSizing = 'border-box';
                        cell.style.margin = '0';
                        cell.style.padding = '0';
                        cell.style.border = 'none';
                        
                        cell.addEventListener('click', cellClicked);
                        fullscreenBoard.appendChild(cell);
                    }
                }
                
                // Update the fullscreen board with the current game state
                function updateFullscreenBoard() {
                    const fullscreenCells = fullscreenBoard.querySelectorAll('.cell');
                    fullscreenCells.forEach((cell, index) => {
                        cell.textContent = gameState[index];
                        cell.className = 'cell';
                        if (gameState[index] === 'X') {
                            cell.classList.add('x');
                        } else if (gameState[index] === 'O') {
                            cell.classList.add('o');
                        }
                    });
                }
                
                // Update the main board with the current game state
                function updateMainBoard() {
                    const mainCells = board.querySelectorAll('.cell');
                    mainCells.forEach((cell, index) => {
                        cell.textContent = gameState[index];
                        cell.className = 'cell';
                        if (gameState[index] === 'X') {
                            cell.classList.add('x');
                        } else if (gameState[index] === 'O') {
                            cell.classList.add('o');
                        }
                    });
                }
                
                // Create the fullscreen board initially
                createFullscreenBoard();
                
                // Return both update functions for use in other functions
                return { updateFullscreenBoard, updateMainBoard };
            }
            
            // Initialize the game
            function initGame() {
                createBoard();
                const { updateFullscreenBoard, updateMainBoard } = initFullscreenMode();
                const cells = document.querySelectorAll('.cell');
                
                cells.forEach(cell => {
                    cell.textContent = '';
                    cell.classList.remove('x', 'o');
                });
                
                resetButton.addEventListener('click', resetGame);
                
                const resetScoresButton = document.getElementById('reset-scores-button');
                resetScoresButton.addEventListener('click', resetScores);
                
                // Add event listeners for fullscreen buttons
                const fullscreenResetButton = document.getElementById('fullscreen-reset-button');
                fullscreenResetButton.addEventListener('click', resetGame);
                
                const fullscreenResetScoresButton = document.getElementById('fullscreen-reset-scores-button');
                fullscreenResetScoresButton.addEventListener('click', resetScores);
                
                // Game mode change handler
                gameModeRadios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        gameMode = e.target.value;
                        
                        // Show/hide difficulty options based on game mode
                        const difficultyOptions = document.getElementById('difficulty-options');
                        if (gameMode === 'computer') {
                            difficultyOptions.style.display = 'block';
                        } else {
                            difficultyOptions.style.display = 'none';
                        }
                        
                        resetGame();
                    });
                });
                
                // Difficulty change handler
                const difficultyRadios = document.querySelectorAll('input[name="difficulty"]');
                difficultyRadios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        difficulty = e.target.value;
                        resetGame();
                    });
                });
                
                // Initialize difficulty options visibility
                const difficultyOptions = document.getElementById('difficulty-options');
                if (gameMode === 'computer') {
                    difficultyOptions.style.display = 'block';
                } else {
                    difficultyOptions.style.display = 'none';
                }
                
                // Status element has been removed
                updateScores();
                showNotification(messages.gameStart, 1500);
                
                // Store the original updateCell function
                const originalUpdateCell = updateCell;
                
                // Override the updateCell function to update both boards
                updateCell = function(cell, index) {
                    originalUpdateCell(cell, index);
                    
                    // Check if the cell is from the main board or fullscreen board
                    const isMainBoard = cell.closest('#game-board') !== null;
                    const isFullscreenBoard = cell.closest('#fullscreen-game-board') !== null;
                    
                    // Update the other board accordingly
                    if (isMainBoard) {
                        updateFullscreenBoard();
                    } else if (isFullscreenBoard) {
                        updateMainBoard();
                    }
                };
                
                // The changePlayer function now handles both status displays
            }
            
            // Handle cell click
            function cellClicked(e) {
                const cell = e.target;
                const index = parseInt(cell.getAttribute('data-index'));
                
                if (gameState[index] !== '' || !gameActive) {
                    return;
                }
                
                // If it's computer's turn, don't allow clicks
                if (gameMode === 'computer' && currentPlayer === 'O') {
                    return;
                }
                
                updateCell(cell, index);
                checkGameResult(index);
                
                // If game is still active and in computer mode, make computer move
                if (gameActive && currentPlayer === 'O' && gameMode === 'computer') {
                    // Add a slight delay before computer moves
                    // Use a longer delay for easy mode to make it feel more natural
                    const delay = difficulty === 'easy' ? 800 : 
                                 difficulty === 'normal' ? 600 : 400;
                    setTimeout(makeComputerMove, delay);
                }
            }
            
            // Change player
            function changePlayer() {
                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                // Don't show turn notifications - they're too frequent
                // We'll only show important notifications like game start, win, or draw
            }
            
            // Update cell state
            function updateCell(cell, index) {
                // Prevent any layout shifts by setting a fixed style
                cell.style.transform = 'none';
                cell.style.transition = 'none';
                cell.style.width = '100%';
                cell.style.height = '100%';
                cell.style.boxSizing = 'border-box';
                cell.style.margin = '0';
                cell.style.padding = '0';
                cell.style.border = 'none';
                
                // Update the cell
                gameState[index] = currentPlayer;
                cell.textContent = currentPlayer;
                cell.classList.add(currentPlayer.toLowerCase());
                
                // Force a repaint to prevent any visual glitches
                void cell.offsetWidth;
                
                // Switch player
                changePlayer();
            }
            
            // Check if the game has a result
            function checkGameResult(lastMoveIndex) {
                // Get row and column of the last move
                const row = Math.floor(lastMoveIndex / BOARD_SIZE);
                const col = lastMoveIndex % BOARD_SIZE;
                const player = gameState[lastMoveIndex];
                
                // Check if the last move resulted in a win
                if (
                    checkDirection(row, col, 1, 0, player) || // Horizontal
                    checkDirection(row, col, 0, 1, player) || // Vertical
                    checkDirection(row, col, 1, 1, player) || // Diagonal (top-left to bottom-right)
                    checkDirection(row, col, 1, -1, player)   // Diagonal (top-right to bottom-left)
                ) {
                    // Update status
                    const winMessage = player === 'X' ? messages.playerXWin : messages.playerOWin;
                    status.textContent = winMessage;
                    
                    gameActive = false;
                    
                    // Update score
                    if (player === 'X') {
                        scores.X++;
                        updateScores();
                        showNotification(messages.playerXWinRound, 2000);
                    } else {
                        scores.O++;
                        updateScores();
                        showNotification(messages.playerOWinRound, 2000);
                    }
                    
                    return;
                }
                
                // Check for draw
                if (!gameState.includes('')) {
                    // Status element has been removed
                    
                    gameActive = false;
                    showNotification(messages.drawRound, 2000);
                    return;
                }
            }
            
            // Check if there are WIN_CONDITION consecutive pieces in a direction
            function checkDirection(row, col, rowDir, colDir, player) {
                // Count consecutive pieces in both directions
                let count = 1; // Start with 1 for the current piece
                
                // Check in positive direction
                for (let i = 1; i < WIN_CONDITION; i++) {
                    const newRow = row + i * rowDir;
                    const newCol = col + i * colDir;
                    
                    // Check if out of bounds
                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                        break;
                    }
                    
                    const index = newRow * BOARD_SIZE + newCol;
                    if (gameState[index] === player) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                // Check in negative direction
                for (let i = 1; i < WIN_CONDITION; i++) {
                    const newRow = row - i * rowDir;
                    const newCol = col - i * colDir;
                    
                    // Check if out of bounds
                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                        break;
                    }
                    
                    const index = newRow * BOARD_SIZE + newCol;
                    if (gameState[index] === player) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                return count >= WIN_CONDITION;
            }
            
            // Computer move with different difficulty levels
            function makeComputerMove() {
                if (!gameActive) return;
                
                // Random move function (for easy mode and fallback)
                function makeRandomMove() {
                    const emptyCells = gameState.map((cell, index) => cell === '' ? index : null).filter(index => index !== null);
                    if (emptyCells.length > 0) {
                        const randomIndex = Math.floor(Math.random() * emptyCells.length);
                        const moveIndex = emptyCells[randomIndex];
                        
                        const cell = document.querySelector(`.cell[data-index="${moveIndex}"]`);
                        updateCell(cell, moveIndex);
                        checkGameResult(moveIndex);
                        return true;
                    }
                    return false;
                }
                
                // Easy mode: basic strategy (former Normal mode)
                if (difficulty === 'easy') {
                    // Check if computer can win in the next move
                    for (let i = 0; i < gameState.length; i++) {
                        if (gameState[i] === '') {
                            // Try this move
                            gameState[i] = 'O';
                            
                            // Check if this move would win
                            const row = Math.floor(i / BOARD_SIZE);
                            const col = i % BOARD_SIZE;
                            
                            if (
                                checkDirection(row, col, 1, 0, 'O') || // Horizontal
                                checkDirection(row, col, 0, 1, 'O') || // Vertical
                                checkDirection(row, col, 1, 1, 'O') || // Diagonal (top-left to bottom-right)
                                checkDirection(row, col, 1, -1, 'O')   // Diagonal (top-right to bottom-left)
                            ) {
                                // This is a winning move, make it
                                const cell = document.querySelector(`.cell[data-index="${i}"]`);
                                gameState[i] = ''; // Reset for updateCell
                                updateCell(cell, i);
                                checkGameResult(i);
                                return;
                            }
                            
                            // Undo the move
                            gameState[i] = '';
                        }
                    }
                    
                    // Check if player can win in the next move and block
                    for (let i = 0; i < gameState.length; i++) {
                        if (gameState[i] === '') {
                            // Try this move for the player
                            gameState[i] = 'X';
                            
                            // Check if this move would win for the player
                            const row = Math.floor(i / BOARD_SIZE);
                            const col = i % BOARD_SIZE;
                            
                            if (
                                checkDirection(row, col, 1, 0, 'X') || // Horizontal
                                checkDirection(row, col, 0, 1, 'X') || // Vertical
                                checkDirection(row, col, 1, 1, 'X') || // Diagonal (top-left to bottom-right)
                                checkDirection(row, col, 1, -1, 'X')   // Diagonal (top-right to bottom-left)
                            ) {
                                // This is a blocking move, make it
                                const cell = document.querySelector(`.cell[data-index="${i}"]`);
                                gameState[i] = ''; // Reset for updateCell
                                updateCell(cell, i);
                                checkGameResult(i);
                                return;
                            }
                            
                            // Undo the move
                            gameState[i] = '';
                        }
                    }
                    
                    // If no winning or blocking move, make a semi-strategic move
                    const potentialMoves = [];
                    
                    for (let i = 0; i < gameState.length; i++) {
                        if (gameState[i] === '') {
                            const row = Math.floor(i / BOARD_SIZE);
                            const col = i % BOARD_SIZE;
                            let score = 0;
                            
                            // Check adjacent cells
                            for (let r = -1; r <= 1; r++) {
                                for (let c = -1; c <= 1; c++) {
                                    if (r === 0 && c === 0) continue;
                                    
                                    const newRow = row + r;
                                    const newCol = col + c;
                                    
                                    if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE) {
                                        const index = newRow * BOARD_SIZE + newCol;
                                        
                                        if (gameState[index] === 'O') {
                                            score += 2; // Prefer moves near computer's pieces
                                        } else if (gameState[index] === 'X') {
                                            score += 1; // Also consider moves near player's pieces
                                        }
                                    }
                                }
                            }
                            
                            // Center and near-center positions are generally good
                            const centerRow = Math.floor(BOARD_SIZE / 2);
                            const centerCol = Math.floor(BOARD_SIZE / 2);
                            const distanceToCenter = Math.abs(row - centerRow) + Math.abs(col - centerCol);
                            
                            score += Math.max(0, 5 - distanceToCenter);
                            
                            potentialMoves.push({ index: i, score: score });
                        }
                    }
                    
                    // If there are potential moves, choose one with some randomness
                    if (potentialMoves.length > 0) {
                        // Sort by score (highest first)
                        potentialMoves.sort((a, b) => b.score - a.score);
                        
                        // Choose one of the top moves (with more randomness)
                        const topMoves = potentialMoves.slice(0, Math.min(5, potentialMoves.length));
                        const randomIndex = Math.floor(Math.random() * topMoves.length);
                        const moveIndex = topMoves[randomIndex].index;
                        
                        const cell = document.querySelector(`.cell[data-index="${moveIndex}"]`);
                        updateCell(cell, moveIndex);
                        checkGameResult(moveIndex);
                        return;
                    }
                    
                    // Fallback to random move if no potential moves found
                    makeRandomMove();
                    return;
                }
                
                // Normal mode (former Hard mode) and Hard mode (improved)
                // Check if computer can win in the next move
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        // Try this move
                        gameState[i] = 'O';
                        
                        // Check if this move would win
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        if (
                            checkDirection(row, col, 1, 0, 'O') || // Horizontal
                            checkDirection(row, col, 0, 1, 'O') || // Vertical
                            checkDirection(row, col, 1, 1, 'O') || // Diagonal (top-left to bottom-right)
                            checkDirection(row, col, 1, -1, 'O')   // Diagonal (top-right to bottom-left)
                        ) {
                            // This is a winning move, make it
                            const cell = document.querySelector(`.cell[data-index="${i}"]`);
                            gameState[i] = ''; // Reset for updateCell
                            updateCell(cell, i);
                            checkGameResult(i);
                            return;
                        }
                        
                        // Undo the move
                        gameState[i] = '';
                    }
                }
                
                // Check if player can win in the next move and block (for normal and hard modes)
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        // Try this move for the player
                        gameState[i] = 'X';
                        
                        // Check if this move would win for the player
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        if (
                            checkDirection(row, col, 1, 0, 'X') || // Horizontal
                            checkDirection(row, col, 0, 1, 'X') || // Vertical
                            checkDirection(row, col, 1, 1, 'X') || // Diagonal (top-left to bottom-right)
                            checkDirection(row, col, 1, -1, 'X')   // Diagonal (top-right to bottom-left)
                        ) {
                            // This is a blocking move, make it
                            const cell = document.querySelector(`.cell[data-index="${i}"]`);
                            gameState[i] = ''; // Reset for updateCell
                            updateCell(cell, i);
                            checkGameResult(i);
                            return;
                        }
                        
                        // Undo the move
                        gameState[i] = '';
                    }
                }
                
                // For normal and hard modes, check for 3 in a row (even if not winning yet)
                // This is a critical threat that must be blocked
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        // Check all directions for 3 in a row
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            // Count player's pieces in this direction
                            let count = 0;
                            
                            // Check in positive direction
                            for (let j = 1; j < 4; j++) {
                                const newRow = row + j * rowDir;
                                const newCol = col + j * colDir;
                                
                                if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                    break;
                                }
                                
                                const index = newRow * BOARD_SIZE + newCol;
                                if (gameState[index] === 'X') {
                                    count++;
                                } else {
                                    break;
                                }
                            }
                            
                            // Check in negative direction
                            for (let j = 1; j < 4; j++) {
                                const newRow = row - j * rowDir;
                                const newCol = col - j * colDir;
                                
                                if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                    break;
                                }
                                
                                const index = newRow * BOARD_SIZE + newCol;
                                if (gameState[index] === 'X') {
                                    count++;
                                } else {
                                    break;
                                }
                            }
                            
                            // If we found 3 or more in a row, block it
                            if (count >= 3) {
                                const cell = document.querySelector(`.cell[data-index="${i}"]`);
                                updateCell(cell, i);
                                checkGameResult(i);
                                return;
                            }
                        }
                    }
                }
                
                // Advanced threat detection for hard mode only
                if (difficulty === 'hard') {
                    // First priority: Check for critical threats (3 in a row with both ends open)
                    const criticalThreats = findCriticalThreats();
                    if (criticalThreats.length > 0) {
                        const moveIndex = criticalThreats[0].index;
                        const cell = document.querySelector(`.cell[data-index="${moveIndex}"]`);
                        updateCell(cell, moveIndex);
                        checkGameResult(moveIndex);
                        return;
                    }
                    
                    // Second priority: Look for creating a fork (two winning threats)
                    const forkMove = findForkMove('O');
                    if (forkMove !== -1) {
                        const cell = document.querySelector(`.cell[data-index="${forkMove}"]`);
                        updateCell(cell, forkMove);
                        checkGameResult(forkMove);
                        return;
                    }
                    
                    // Third priority: Block opponent's fork
                    const blockForkMove = findForkMove('X');
                    if (blockForkMove !== -1) {
                        const cell = document.querySelector(`.cell[data-index="${blockForkMove}"]`);
                        updateCell(cell, blockForkMove);
                        checkGameResult(blockForkMove);
                        return;
                    }
                    
                    // Fourth priority: Use minimax algorithm with alpha-beta pruning for deeper search
                    // This is only used when there are fewer than 100 empty cells to keep performance reasonable
                    const emptyCount = gameState.filter(cell => cell === '').length;
                    if (emptyCount < 100) {
                        const bestMove = findBestMoveWithMinimax(Math.min(4, emptyCount));
                        if (bestMove !== -1) {
                            const cell = document.querySelector(`.cell[data-index="${bestMove}"]`);
                            updateCell(cell, bestMove);
                            checkGameResult(bestMove);
                            return;
                        }
                    }
                    
                    // Fifth priority: Look for special patterns (3x3, 4x3, etc.)
                    const specialPatternMove = findSpecialPatternMove();
                    if (specialPatternMove !== -1) {
                        const cell = document.querySelector(`.cell[data-index="${specialPatternMove}"]`);
                        updateCell(cell, specialPatternMove);
                        checkGameResult(specialPatternMove);
                        return;
                    }
                    
                    // Sixth priority: Look for other threats
                    const threatMoves = findPlayerThreats();
                    if (threatMoves.length > 0) {
                        // If there are multiple threats, we need to block the most critical one
                        const moveIndex = threatMoves[0].index;
                        const cell = document.querySelector(`.cell[data-index="${moveIndex}"]`);
                        updateCell(cell, moveIndex);
                        checkGameResult(moveIndex);
                        return;
                    }
                }
                
                // For normal and hard modes, look for strategic moves
                const potentialMoves = [];
                
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        let score = 0;
                        
                        // Check adjacent cells
                        for (let r = -1; r <= 1; r++) {
                            for (let c = -1; c <= 1; c++) {
                                if (r === 0 && c === 0) continue;
                                
                                const newRow = row + r;
                                const newCol = col + c;
                                
                                if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE) {
                                    const index = newRow * BOARD_SIZE + newCol;
                                    
                                    // Hard mode gives more weight to offensive moves
                                    if (difficulty === 'hard') {
                                        if (gameState[index] === 'O') {
                                            score += 3; // Prefer moves near computer's pieces
                                        } else if (gameState[index] === 'X') {
                                            score += 1; // Also consider moves near player's pieces
                                        }
                                    } else { // Normal mode is more balanced
                                        if (gameState[index] === 'O') {
                                            score += 2; // Prefer moves near computer's pieces
                                        } else if (gameState[index] === 'X') {
                                            score += 1; // Also consider moves near player's pieces
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Hard mode uses advanced pattern recognition
                        if (difficulty === 'hard') {
                            // Try this move
                            gameState[i] = 'O';
                            
                            // Check for potential future wins (4 in a row)
                            for (let dir = 0; dir < 4; dir++) {
                                const directions = [
                                    [1, 0], // Horizontal
                                    [0, 1], // Vertical
                                    [1, 1], // Diagonal (top-left to bottom-right)
                                    [1, -1] // Diagonal (top-right to bottom-left)
                                ];
                                const [rowDir, colDir] = directions[dir];
                                
                                // Count consecutive pieces and open ends in both directions
                                const pattern = analyzePattern(row, col, rowDir, colDir, 'O');
                                
                                // Score based on pattern analysis
                                if (pattern.count >= 2) {
                                    // Base score for consecutive pieces
                                    score += pattern.count * 3;
                                    
                                    // Bonus for open ends (potential for expansion)
                                    if (pattern.openEnds > 0) {
                                        score += pattern.openEnds * pattern.count * 2;
                                    }
                                    
                                    // Heavily favor moves that create 3+ in a row with open ends
                                    if (pattern.count >= 3) {
                                        score += 15;
                                        if (pattern.openEnds === 2) {
                                            score += 30; // Both ends open is very strong
                                        }
                                    }
                                    
                                    // Favor moves that create 4 in a row (one away from winning)
                                    if (pattern.count === 4) {
                                        score += 80;
                                        if (pattern.openEnds > 0) {
                                            score += 50; // Open end makes it a winning threat
                                        }
                                    }
                                }
                            }
                            
                            // Check for creating special patterns
                            // Check for creating a 3x3 pattern (three in a row in two directions)
                            let threeInRowCount = 0;
                            let fourInRowCount = 0;
                            
                            for (let dir = 0; dir < 4; dir++) {
                                const directions = [
                                    [1, 0], // Horizontal
                                    [0, 1], // Vertical
                                    [1, 1], // Diagonal (top-left to bottom-right)
                                    [1, -1] // Diagonal (top-right to bottom-left)
                                ];
                                const [rowDir, colDir] = directions[dir];
                                
                                const pattern = analyzePattern(row, col, rowDir, colDir, 'O');
                                
                                // Count directions with 3 or more in a row
                                if (pattern.count >= 4 && pattern.openEnds > 0) {
                                    fourInRowCount++;
                                } else if (pattern.count >= 3 && pattern.openEnds > 0) {
                                    threeInRowCount++;
                                }
                            }
                            
                            // If we found a 3x3 pattern, this is a strong move
                            if (threeInRowCount >= 2) {
                                score += 100; // Very strong position
                            }
                            
                            // If we found a 4x3 pattern, this is an even stronger move
                            if (fourInRowCount >= 1 && threeInRowCount >= 1) {
                                score += 150; // Extremely strong position
                            }
                            
                            // Also analyze opponent's potential patterns from this position
                            // This helps block opponent's strong formations
                            gameState[i] = 'X';
                            for (let dir = 0; dir < 4; dir++) {
                                const directions = [
                                    [1, 0], // Horizontal
                                    [0, 1], // Vertical
                                    [1, 1], // Diagonal (top-left to bottom-right)
                                    [1, -1] // Diagonal (top-right to bottom-left)
                                ];
                                const [rowDir, colDir] = directions[dir];
                                
                                // Analyze what the opponent could do if they played here
                                const pattern = analyzePattern(row, col, rowDir, colDir, 'X');
                                
                                // Block opponent's strong patterns
                                if (pattern.count >= 2) {
                                    // Base defensive score
                                    score += pattern.count * 2;
                                    
                                    // Higher priority for blocking patterns with open ends
                                    if (pattern.openEnds > 0) {
                                        score += pattern.openEnds * pattern.count;
                                    }
                                    
                                    // Critical to block 3+ in a row with open ends
                                    if (pattern.count >= 3) {
                                        score += 15;
                                        if (pattern.openEnds === 2) {
                                            score += 25; // Both ends open is extremely dangerous
                                        }
                                    }
                                    
                                    // Absolutely critical to block 4 in a row with an open end
                                    if (pattern.count >= 4 && pattern.openEnds > 0) {
                                        score += 90; // Almost as important as winning
                                    }
                                }
                            }
                            
                            // Check for opponent's special patterns
                            let opponentThreeInRowCount = 0;
                            for (let dir = 0; dir < 4; dir++) {
                                const directions = [
                                    [1, 0], // Horizontal
                                    [0, 1], // Vertical
                                    [1, 1], // Diagonal (top-left to bottom-right)
                                    [1, -1] // Diagonal (top-right to bottom-left)
                                ];
                                const [rowDir, colDir] = directions[dir];
                                
                                const pattern = analyzePattern(row, col, rowDir, colDir, 'X');
                                
                                // Count directions with 3 or more in a row with open ends
                                if (pattern.count >= 3 && pattern.openEnds > 0) {
                                    opponentThreeInRowCount++;
                                }
                            }
                            
                            // If opponent could create a 3x3 pattern, block it
                            if (opponentThreeInRowCount >= 2) {
                                score += 80; // Very important to block
                            }
                            
                            // Undo the move
                            gameState[i] = '';
                            
                            // Look ahead one more move (simulate opponent's response)
                            if (gameState.filter(cell => cell === '').length > 1) { // If there are at least 2 empty cells
                                // Make this move
                                gameState[i] = 'O';
                                
                                // Find opponent's best response
                                let bestResponseScore = -Infinity;
                                let opponentCanWin = false;
                                
                                for (let j = 0; j < gameState.length; j++) {
                                    if (gameState[j] === '') {
                                        // Try opponent's move
                                        gameState[j] = 'X';
                                        
                                        // Check if this is a winning move for opponent
                                        const responseRow = Math.floor(j / BOARD_SIZE);
                                        const responseCol = j % BOARD_SIZE;
                                        
                                        if (
                                            checkDirection(responseRow, responseCol, 1, 0, 'X') || // Horizontal
                                            checkDirection(responseRow, responseCol, 0, 1, 'X') || // Vertical
                                            checkDirection(responseRow, responseCol, 1, 1, 'X') || // Diagonal (top-left to bottom-right)
                                            checkDirection(responseRow, responseCol, 1, -1, 'X')   // Diagonal (top-right to bottom-left)
                                        ) {
                                            // If opponent can win, this is a terrible move
                                            opponentCanWin = true;
                                            gameState[j] = '';
                                            break;
                                        }
                                        
                                        // Check if this creates a winning threat for opponent
                                        let threatScore = 0;
                                        
                                        // Check all directions for opponent's threats
                                        for (let dir = 0; dir < 4; dir++) {
                                            const directions = [
                                                [1, 0], // Horizontal
                                                [0, 1], // Vertical
                                                [1, 1], // Diagonal (top-left to bottom-right)
                                                [1, -1] // Diagonal (top-right to bottom-left)
                                            ];
                                            const [rowDir, colDir] = directions[dir];
                                            
                                            const pattern = analyzePattern(responseRow, responseCol, rowDir, colDir, 'X');
                                            
                                            // Score opponent's potential threats
                                            if (pattern.count >= 3) {
                                                threatScore += pattern.count * 5;
                                                if (pattern.openEnds > 0) {
                                                    threatScore += pattern.openEnds * 10;
                                                }
                                                if (pattern.count >= 4 && pattern.openEnds > 0) {
                                                    threatScore += 100; // Critical threat
                                                }
                                            }
                                        }
                                        
                                        // Update best response score
                                        bestResponseScore = Math.max(bestResponseScore, threatScore);
                                        
                                        // Undo opponent's move
                                        gameState[j] = '';
                                    }
                                }
                                
                                // Heavily penalize moves that allow opponent to win next turn
                                if (opponentCanWin) {
                                    score -= 1000;
                                } else {
                                    // Penalize moves that allow opponent to create strong threats
                                    score -= bestResponseScore;
                                }
                                
                                // Undo our move
                                gameState[i] = '';
                            }
                        } else {
                            // Normal mode - simpler pattern recognition
                            gameState[i] = 'O';
                            
                            // Check for potential future wins
                            for (let dir = 0; dir < 4; dir++) {
                                const directions = [
                                    [1, 0], // Horizontal
                                    [0, 1], // Vertical
                                    [1, 1], // Diagonal (top-left to bottom-right)
                                    [1, -1] // Diagonal (top-right to bottom-left)
                                ];
                                const [rowDir, colDir] = directions[dir];
                                
                                let count = 1;
                                let openEnds = 0;
                                
                                // Check in positive direction
                                for (let j = 1; j < WIN_CONDITION; j++) {
                                    const newRow = row + j * rowDir;
                                    const newCol = col + j * colDir;
                                    
                                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                        break;
                                    }
                                    
                                    const index = newRow * BOARD_SIZE + newCol;
                                    if (gameState[index] === 'O') {
                                        count++;
                                    } else if (gameState[index] === '') {
                                        openEnds++;
                                        break;
                                    } else {
                                        break;
                                    }
                                }
                                
                                // Check in negative direction
                                for (let j = 1; j < WIN_CONDITION; j++) {
                                    const newRow = row - j * rowDir;
                                    const newCol = col - j * colDir;
                                    
                                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                        break;
                                    }
                                    
                                    const index = newRow * BOARD_SIZE + newCol;
                                    if (gameState[index] === 'O') {
                                        count++;
                                    } else if (gameState[index] === '') {
                                        openEnds++;
                                        break;
                                    } else {
                                        break;
                                    }
                                }
                                
                                // Score based on consecutive pieces and open ends
                                if (count >= 2) {
                                    score += count * 2;
                                    if (openEnds > 0) {
                                        score += openEnds * count;
                                    }
                                    if (count >= 3) {
                                        score += 10; // Heavily favor moves that create 3+ in a row
                                    }
                                }
                            }
                            
                            // Undo the move
                            gameState[i] = '';
                        }
                        
                        // Center and near-center positions are generally good
                        const centerRow = Math.floor(BOARD_SIZE / 2);
                        const centerCol = Math.floor(BOARD_SIZE / 2);
                        const distanceToCenter = Math.abs(row - centerRow) + Math.abs(col - centerCol);
                        
                        // Hard mode values center position more
                        if (difficulty === 'hard') {
                            score += Math.max(0, 8 - distanceToCenter);
                        } else {
                            score += Math.max(0, 5 - distanceToCenter);
                        }
                        
                        potentialMoves.push({ index: i, score: score });
                    }
                }
                
                // If there are potential moves, choose one based on difficulty
                if (potentialMoves.length > 0) {
                    // Sort by score (highest first)
                    potentialMoves.sort((a, b) => b.score - a.score);
                    
                    let moveIndex;
                    
                    if (difficulty === 'hard') {
                        // Super Hard mode: Always choose the absolute best move
                        // Get the highest score
                        const highestScore = potentialMoves[0].score;
                        
                        // Always take the best move with no randomness
                        moveIndex = potentialMoves[0].index;
                    } else if (difficulty === 'normal') {
                        // Normal mode (former Hard): Choose the best move with minimal randomness
                        const highestScore = potentialMoves[0].score;
                        
                        // If the highest score is very high, just take the best move
                        if (highestScore > 100) {
                            moveIndex = potentialMoves[0].index;
                        } else {
                            // Get all moves with scores very close to the highest score
                            const bestMoves = potentialMoves.filter(move => move.score >= highestScore * 0.98);
                            // Choose one of the best moves (with very little randomness)
                            const randomIndex = Math.floor(Math.random() * bestMoves.length);
                            moveIndex = bestMoves[randomIndex].index;
                        }
                    } else { // Easy mode
                        // Normal mode: Choose one of the top moves (with more randomness)
                        const topMoves = potentialMoves.slice(0, Math.min(5, potentialMoves.length));
                        const randomIndex = Math.floor(Math.random() * topMoves.length);
                        moveIndex = topMoves[randomIndex].index;
                    }
                    
                    const cell = document.querySelector(`.cell[data-index="${moveIndex}"]`);
                    updateCell(cell, moveIndex);
                    checkGameResult(moveIndex);
                    return;
                }
                
                // Fallback to random move if no potential moves found
                makeRandomMove();
            }
            
            // Helper function to analyze patterns in a specific direction
            function analyzePattern(row, col, rowDir, colDir, player) {
                let count = 1; // Start with 1 for the current piece
                let openEnds = 0;
                let blocks = 0;
                
                // Check in positive direction
                for (let i = 1; i < WIN_CONDITION; i++) {
                    const newRow = row + i * rowDir;
                    const newCol = col + i * colDir;
                    
                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                        blocks++; // Edge of board blocks this direction
                        break;
                    }
                    
                    const index = newRow * BOARD_SIZE + newCol;
                    if (gameState[index] === player) {
                        count++;
                    } else if (gameState[index] === '') {
                        openEnds++;
                        break;
                    } else {
                        blocks++; // Opponent's piece blocks this direction
                        break;
                    }
                }
                
                // Check in negative direction
                for (let i = 1; i < WIN_CONDITION; i++) {
                    const newRow = row - i * rowDir;
                    const newCol = col - i * colDir;
                    
                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                        blocks++; // Edge of board blocks this direction
                        break;
                    }
                    
                    const index = newRow * BOARD_SIZE + newCol;
                    if (gameState[index] === player) {
                        count++;
                    } else if (gameState[index] === '') {
                        openEnds++;
                        break;
                    } else {
                        blocks++; // Opponent's piece blocks this direction
                        break;
                    }
                }
                
                return { count, openEnds, blocks };
            }
            
            // Find critical threats (3 in a row with both ends open - must be blocked immediately)
            function findCriticalThreats() {
                const criticalThreats = [];
                
                // Check all empty cells
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        // First check if this is a critical position for the player
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            // Check for "three in a row with both ends open" pattern
                            // This is a critical threat that must be blocked
                            
                            // Check one side
                            let count = 0;
                            let hasOpenEnd1 = false;
                            let hasOpenEnd2 = false;
                            
                            // Check in positive direction
                            for (let j = 1; j <= 3; j++) {
                                const newRow = row + j * rowDir;
                                const newCol = col + j * colDir;
                                
                                if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                    break;
                                }
                                
                                const index = newRow * BOARD_SIZE + newCol;
                                if (gameState[index] === 'X') {
                                    count++;
                                } else if (gameState[index] === '') {
                                    if (j === 1) {
                                        // Check one more position for open-three pattern
                                        const nextRow = row + 2 * rowDir;
                                        const nextCol = col + 2 * colDir;
                                        
                                        if (nextRow >= 0 && nextRow < BOARD_SIZE && nextCol >= 0 && nextCol < BOARD_SIZE) {
                                            const nextIndex = nextRow * BOARD_SIZE + nextCol;
                                            if (gameState[nextIndex] === 'X') {
                                                const thirdRow = row + 3 * rowDir;
                                                const thirdCol = col + 3 * colDir;
                                                
                                                if (thirdRow >= 0 && thirdRow < BOARD_SIZE && thirdCol >= 0 && thirdCol < BOARD_SIZE) {
                                                    const thirdIndex = thirdRow * BOARD_SIZE + thirdCol;
                                                    if (gameState[thirdIndex] === 'X') {
                                                        // Found X_XX pattern
                                                        count = 3;
                                                        hasOpenEnd1 = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    hasOpenEnd1 = true;
                                    break;
                                } else {
                                    break;
                                }
                            }
                            
                            // Check in negative direction
                            for (let j = 1; j <= 3; j++) {
                                const newRow = row - j * rowDir;
                                const newCol = col - j * colDir;
                                
                                if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                    break;
                                }
                                
                                const index = newRow * BOARD_SIZE + newCol;
                                if (gameState[index] === 'X') {
                                    count++;
                                } else if (gameState[index] === '') {
                                    if (j === 1) {
                                        // Check one more position for open-three pattern
                                        const nextRow = row - 2 * rowDir;
                                        const nextCol = col - 2 * colDir;
                                        
                                        if (nextRow >= 0 && nextRow < BOARD_SIZE && nextCol >= 0 && nextCol < BOARD_SIZE) {
                                            const nextIndex = nextRow * BOARD_SIZE + nextCol;
                                            if (gameState[nextIndex] === 'X') {
                                                const thirdRow = row - 3 * rowDir;
                                                const thirdCol = col - 3 * colDir;
                                                
                                                if (thirdRow >= 0 && thirdRow < BOARD_SIZE && thirdCol >= 0 && thirdCol < BOARD_SIZE) {
                                                    const thirdIndex = thirdRow * BOARD_SIZE + thirdCol;
                                                    if (gameState[thirdIndex] === 'X') {
                                                        // Found XX_X pattern
                                                        count = 3;
                                                        hasOpenEnd2 = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    hasOpenEnd2 = true;
                                    break;
                                } else {
                                    break;
                                }
                            }
                            
                            // If we found 3 in a row with both ends open, this is a critical threat
                            if (count >= 3 && hasOpenEnd1 && hasOpenEnd2) {
                                criticalThreats.push({ 
                                    index: i, 
                                    score: 1000 // Very high score for critical threats
                                });
                                break; // No need to check other directions
                            }
                            
                            // Also check for 4 in a row with one end open (also critical)
                            if (count >= 4 && (hasOpenEnd1 || hasOpenEnd2)) {
                                criticalThreats.push({ 
                                    index: i, 
                                    score: 900 // Very high score for critical threats
                                });
                                break; // No need to check other directions
                            }
                            
                            // Also check for 2 in a row with both ends open (important in hard mode)
                            if (difficulty === 'hard' && count >= 2 && hasOpenEnd1 && hasOpenEnd2) {
                                criticalThreats.push({ 
                                    index: i, 
                                    score: 800 // High score for potential future threats
                                });
                                // Don't break, continue checking other directions
                            }
                            
                            // Check for discontinuous patterns like X_X (with both ends open)
                            if (difficulty === 'hard') {
                                // Reset for new check
                                count = 0;
                                hasOpenEnd1 = false;
                                hasOpenEnd2 = false;
                                
                                // Check for X_X pattern in positive direction
                                let foundGap = false;
                                for (let j = 1; j <= 4; j++) {
                                    const newRow = row + j * rowDir;
                                    const newCol = col + j * colDir;
                                    
                                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                        break;
                                    }
                                    
                                    const index = newRow * BOARD_SIZE + newCol;
                                    if (gameState[index] === 'X') {
                                        if (foundGap) {
                                            // Found X_X pattern
                                            count += 2;
                                            break;
                                        } else {
                                            count++;
                                        }
                                    } else if (gameState[index] === '' && !foundGap && count === 1) {
                                        foundGap = true;
                                    } else {
                                        if (gameState[index] === '') {
                                            hasOpenEnd1 = true;
                                        }
                                        break;
                                    }
                                }
                                
                                // Check for X_X pattern in negative direction
                                foundGap = false;
                                let negCount = 0;
                                for (let j = 1; j <= 4; j++) {
                                    const newRow = row - j * rowDir;
                                    const newCol = col - j * colDir;
                                    
                                    if (newRow < 0 || newRow >= BOARD_SIZE || newCol < 0 || newCol >= BOARD_SIZE) {
                                        break;
                                    }
                                    
                                    const index = newRow * BOARD_SIZE + newCol;
                                    if (gameState[index] === 'X') {
                                        if (foundGap) {
                                            // Found X_X pattern
                                            negCount += 2;
                                            break;
                                        } else {
                                            negCount++;
                                        }
                                    } else if (gameState[index] === '' && !foundGap && negCount === 1) {
                                        foundGap = true;
                                    } else {
                                        if (gameState[index] === '') {
                                            hasOpenEnd2 = true;
                                        }
                                        break;
                                    }
                                }
                                
                                count += negCount;
                                
                                // If we found a discontinuous pattern with open ends, this is a potential threat
                                if (count >= 2 && (hasOpenEnd1 || hasOpenEnd2)) {
                                    criticalThreats.push({ 
                                        index: i, 
                                        score: 700 // High score for discontinuous patterns
                                    });
                                }
                            }
                        }
                        
                        // For hard mode, also check if this move creates a winning opportunity for us
                        if (difficulty === 'hard') {
                            // Try this move
                            gameState[i] = 'O';
                            
                            // Check if this creates a strong position for us
                            let offensiveScore = 0;
                            
                            for (let dir = 0; dir < 4; dir++) {
                                const directions = [
                                    [1, 0], // Horizontal
                                    [0, 1], // Vertical
                                    [1, 1], // Diagonal (top-left to bottom-right)
                                    [1, -1] // Diagonal (top-right to bottom-left)
                                ];
                                const [rowDir, colDir] = directions[dir];
                                
                                const pattern = analyzePattern(row, col, rowDir, colDir, 'O');
                                
                                // Score offensive opportunities
                                if (pattern.count >= 3 && pattern.openEnds > 0) {
                                    offensiveScore += 500 + pattern.count * 100 + pattern.openEnds * 50;
                                } else if (pattern.count >= 2 && pattern.openEnds === 2) {
                                    offensiveScore += 300; // Open-ended two is strong
                                }
                            }
                            
                            // Undo the move
                            gameState[i] = '';
                            
                            // If this is a strong offensive move, add it to the list
                            if (offensiveScore > 0) {
                                criticalThreats.push({ 
                                    index: i, 
                                    score: offensiveScore
                                });
                            }
                        }
                    }
                }
                
                // Sort threats by score (highest first)
                criticalThreats.sort((a, b) => b.score - a.score);
                
                return criticalThreats;
            }
            
            // Find special patterns like 3x3, 4x3, etc.
            function findSpecialPatternMove() {
                // Check for creating special patterns that lead to a win
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        // Try computer's move here
                        gameState[i] = 'O';
                        
                        // Check for creating a 3x3 pattern (three in a row in two directions)
                        let threeInRowCount = 0;
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            const pattern = analyzePattern(row, col, rowDir, colDir, 'O');
                            
                            // Count directions with 3 or more in a row
                            if (pattern.count >= 3 && pattern.openEnds > 0) {
                                threeInRowCount++;
                            }
                        }
                        
                        // If we found a 3x3 pattern, this is a strong move
                        if (threeInRowCount >= 2) {
                            gameState[i] = '';
                            return i;
                        }
                        
                        // Check for creating a 4x3 pattern (four in one direction, three in another)
                        let hasThree = false;
                        let hasFour = false;
                        
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            const pattern = analyzePattern(row, col, rowDir, colDir, 'O');
                            
                            if (pattern.count >= 4) {
                                hasFour = true;
                            } else if (pattern.count >= 3 && pattern.openEnds > 0) {
                                hasThree = true;
                            }
                        }
                        
                        // If we found a 4x3 pattern, this is a strong move
                        if (hasFour && hasThree) {
                            gameState[i] = '';
                            return i;
                        }
                        
                        // Undo the move
                        gameState[i] = '';
                    }
                }
                
                return -1; // No special pattern found
            }
            
            // Find player's threats (positions where they have 3 or 4 in a row with open ends)
            function findPlayerThreats() {
                const threats = [];
                
                // Check all empty cells
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        let threatScore = 0;
                        
                        // Try player's move here
                        gameState[i] = 'X';
                        
                        // Check all directions for threats
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            const pattern = analyzePattern(row, col, rowDir, colDir, 'X');
                            
                            // Score the threat
                            if (pattern.count >= 3 && pattern.openEnds > 0) {
                                threatScore += pattern.count * 10 + pattern.openEnds * 5;
                            } else if (pattern.count >= 2 && pattern.openEnds === 2) {
                                // Also consider potential open-two threats
                                threatScore += pattern.count * 5;
                            }
                        }
                        
                        // Undo the move
                        gameState[i] = '';
                        
                        // If this is a significant threat, add it to the list
                        if (threatScore > 0) {
                            threats.push({ index: i, score: threatScore });
                        }
                    }
                }
                
                // Sort threats by score (highest first)
                threats.sort((a, b) => b.score - a.score);
                
                return threats;
            }
            
            // Minimax algorithm with alpha-beta pruning for finding the best move
            function findBestMoveWithMinimax(depth = 3) {
                // If there are no empty cells, return -1
                if (gameState.every(cell => cell !== '')) {
                    return -1;
                }
                
                let bestScore = -Infinity;
                let bestMove = -1;
                
                // Try each empty cell
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        // Make the move
                        gameState[i] = 'O';
                        
                        // Get score from minimax
                        const score = minimax(depth, false, -Infinity, Infinity);
                        
                        // Undo the move
                        gameState[i] = '';
                        
                        // Update best score and move
                        if (score > bestScore) {
                            bestScore = score;
                            bestMove = i;
                        }
                    }
                }
                
                return bestMove;
            }
            
            // Minimax algorithm with alpha-beta pruning
            function minimax(depth, isMaximizing, alpha, beta) {
                // Check for terminal states
                const winner = checkWinner();
                if (winner === 'O') return 1000 + depth; // Computer wins
                if (winner === 'X') return -1000 - depth; // Player wins
                if (gameState.every(cell => cell !== '')) return 0; // Draw
                if (depth === 0) return evaluateBoard(); // Depth limit reached
                
                if (isMaximizing) {
                    // Computer's turn (maximizing)
                    let bestScore = -Infinity;
                    
                    // Try each empty cell
                    for (let i = 0; i < gameState.length; i++) {
                        if (gameState[i] === '') {
                            // Make the move
                            gameState[i] = 'O';
                            
                            // Get score from minimax
                            const score = minimax(depth - 1, false, alpha, beta);
                            
                            // Undo the move
                            gameState[i] = '';
                            
                            // Update best score
                            bestScore = Math.max(bestScore, score);
                            
                            // Alpha-beta pruning
                            alpha = Math.max(alpha, bestScore);
                            if (beta <= alpha) break;
                        }
                    }
                    
                    return bestScore;
                } else {
                    // Player's turn (minimizing)
                    let bestScore = Infinity;
                    
                    // Try each empty cell
                    for (let i = 0; i < gameState.length; i++) {
                        if (gameState[i] === '') {
                            // Make the move
                            gameState[i] = 'X';
                            
                            // Get score from minimax
                            const score = minimax(depth - 1, true, alpha, beta);
                            
                            // Undo the move
                            gameState[i] = '';
                            
                            // Update best score
                            bestScore = Math.min(bestScore, score);
                            
                            // Alpha-beta pruning
                            beta = Math.min(beta, bestScore);
                            if (beta <= alpha) break;
                        }
                    }
                    
                    return bestScore;
                }
            }
            
            // Check if there's a winner
            function checkWinner() {
                // Check all possible winning combinations
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] !== '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        if (
                            checkDirection(row, col, 1, 0, gameState[i]) || // Horizontal
                            checkDirection(row, col, 0, 1, gameState[i]) || // Vertical
                            checkDirection(row, col, 1, 1, gameState[i]) || // Diagonal (top-left to bottom-right)
                            checkDirection(row, col, 1, -1, gameState[i])   // Diagonal (top-right to bottom-left)
                        ) {
                            return gameState[i];
                        }
                    }
                }
                
                return null;
            }
            
            // Evaluate the current board state for minimax
            function evaluateBoard() {
                let score = 0;
                
                // Check all cells
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] !== '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        
                        // Check all directions
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            const pattern = analyzePattern(row, col, rowDir, colDir, gameState[i]);
                            
                            // Score the pattern
                            if (gameState[i] === 'O') {
                                // Computer's patterns (positive score)
                                if (pattern.count >= 2) {
                                    score += pattern.count * 10;
                                    if (pattern.openEnds > 0) {
                                        score += pattern.openEnds * pattern.count * 5;
                                    }
                                }
                            } else {
                                // Player's patterns (negative score)
                                if (pattern.count >= 2) {
                                    score -= pattern.count * 10;
                                    if (pattern.openEnds > 0) {
                                        score -= pattern.openEnds * pattern.count * 5;
                                    }
                                }
                            }
                        }
                    }
                }
                
                return score;
            }
            
            // Find a move that creates a fork (two winning threats)
            function findForkMove(player) {
                for (let i = 0; i < gameState.length; i++) {
                    if (gameState[i] === '') {
                        const row = Math.floor(i / BOARD_SIZE);
                        const col = i % BOARD_SIZE;
                        let winningDirections = 0;
                        
                        // Try this move
                        gameState[i] = player;
                        
                        // Check all directions for potential winning lines
                        for (let dir = 0; dir < 4; dir++) {
                            const directions = [
                                [1, 0], // Horizontal
                                [0, 1], // Vertical
                                [1, 1], // Diagonal (top-left to bottom-right)
                                [1, -1] // Diagonal (top-right to bottom-left)
                            ];
                            const [rowDir, colDir] = directions[dir];
                            
                            const pattern = analyzePattern(row, col, rowDir, colDir, player);
                            
                            // Count directions where we have 3+ in a row with an open end
                            if (pattern.count >= 3 && pattern.openEnds > 0) {
                                winningDirections++;
                            }
                        }
                        
                        // Undo the move
                        gameState[i] = '';
                        
                        // If this creates multiple winning threats, it's a fork
                        if (winningDirections >= 2) {
                            return i;
                        }
                    }
                }
                
                return -1; // No fork found
            }
            
            // Reset the game
            function resetGame() {
                currentPlayer = 'X';
                gameActive = true;
                gameState = Array(BOARD_SIZE * BOARD_SIZE).fill('');
                // Status element has been removed
                
                // No need to update fullscreen status anymore
                
                // Update both the regular and fullscreen boards
                const cells = document.querySelectorAll('#game-board .cell');
                cells.forEach(cell => {
                    cell.textContent = '';
                    cell.classList.remove('x', 'o');
                });
                
                const fullscreenCells = document.querySelectorAll('#fullscreen-game-board .cell');
                fullscreenCells.forEach(cell => {
                    cell.textContent = '';
                    cell.classList.remove('x', 'o');
                });
                
                showNotification(messages.gameStart, 1500);
                
                // If in fullscreen mode, make sure the fullscreen board is updated
                const fullscreenContainer = document.getElementById('fullscreen-container');
                if (fullscreenContainer && fullscreenContainer.classList.contains('active')) {
                    // Force a redraw of the fullscreen board
                    setTimeout(() => {
                        const fullscreenBoard = document.getElementById('fullscreen-game-board');
                        if (fullscreenBoard) {
                            fullscreenBoard.style.opacity = '0.99';
                            setTimeout(() => {
                                fullscreenBoard.style.opacity = '1';
                            }, 50);
                        }
                    }, 100);
                }
            }
            
            // Reset scores
            function resetScores() {
                scores.X = 0;
                scores.O = 0;
                updateScores();
            }
            
            // Initialize the game
            initGame();
        });
    </script>
</body>
</html>






