﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infinitives - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="infinitives-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="infinitives-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="infinitives-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Infinitives</span>
        </div>
        
        <section class="grammar-section">
            <h2>Infinitives in Finnish</h2>
            <p>Finnish has five different infinitive forms, each with its own uses and cases. These infinitive forms are essential for constructing complex sentences and expressing various verbal relationships.</p>
            
            <div class="note-box">
                <p><strong>Important:</strong> Unlike many other languages that have just one or two infinitive forms, Finnish has five distinct infinitives, traditionally numbered from 1 to 5. However, in modern grammar descriptions, they are often referred to by their case forms rather than numbers.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>First Infinitive (Basic Form)</h3>
            <p>The first infinitive is the basic dictionary form of the verb. It has two forms: the short form (basic) and the translative form (longer form with -kse-).</p>
            
            <h4>Short Form (Basic)</h4>
            <p>This is the form you find in dictionaries, ending in -a/-ä or -da/-dä.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Example</th>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>puhua (to speak)</td>
                </tr>
                <tr>
                    <td>Type 2</td>
                    <td>syödä (to eat)</td>
                </tr>
                <tr>
                    <td>Type 3</td>
                    <td>tulla (to come)</td>
                </tr>
                <tr>
                    <td>Type 4</td>
                    <td>haluta (to want)</td>
                </tr>
                <tr>
                    <td>Type 5</td>
                    <td>tarvita (to need)</td>
                </tr>
                <tr>
                    <td>Type 6</td>
                    <td>vanheta (to grow old)</td>
                </tr>
            </table>
            
            <h4>Uses of the First Infinitive Short Form</h4>
            <div class="example-box">
                <p>Haluan <strong>puhua</strong> suomea. (I want to speak Finnish.)</p>
                <p>On kiva <strong>oppia</strong> uusia asioita. (It's nice to learn new things.)</p>
                <p>Aion <strong>matkustaa</strong> Suomeen. (I intend to travel to Finland.)</p>
            </div>
            
            <h4>Translative Form</h4>
            <p>The translative form of the first infinitive adds -kse- + possessive suffix to express purpose ("in order to").</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-kseni</td>
                    <td>puhuakseni</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-ksesi</td>
                    <td>puhuaksesi</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>-kseen</td>
                    <td>puhuakseen</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-ksemme</td>
                    <td>puhuaksemme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-ksenne</td>
                    <td>puhuaksenne</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-kseen</td>
                    <td>puhuakseen</td>
                </tr>
            </table>
            
            <h4>Uses of the First Infinitive Translative Form</h4>
            <div class="example-box">
                <p>Menen Suomeen <strong>oppiakseni</strong> suomea. (I'm going to Finland in order to learn Finnish.)</p>
                <p>Hän tuli tänne <strong>tavatakseen</strong> sinut. (He/she came here in order to meet you.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Second Infinitive</h3>
            <p>The second infinitive is used in the inessive (-ssa/-ssä) and instructive (-n) cases to express actions happening simultaneously.</p>
            
            <h4>Formation</h4>
            <p>The second infinitive is formed from the verb stem by adding -e- and then the case ending.</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>2nd Infinitive Inessive</th>
                    <th>2nd Infinitive Instructive</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhuessa (when speaking)</td>
                    <td>puhuen (by speaking)</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syödessä (when eating)</td>
                    <td>syöden (by eating)</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tul-</td>
                    <td>tullessa (when coming)</td>
                    <td>tullen (by coming)</td>
                </tr>
                <tr>
                    <td>haluta (to want)</td>
                    <td>halut-</td>
                    <td>halutessa (when wanting)</td>
                    <td>haluten (by wanting)</td>
                </tr>
            </table>
            
            <h4>Inessive Case (-ssa/-ssä)</h4>
            <p>The inessive form expresses "when" or "while" doing something. It can take possessive suffixes to indicate who is performing the action.</p>
            
            <div class="example-box">
                <p><strong>Puhuessani</strong> suomea teen paljon virheitä. (When I speak Finnish, I make many mistakes.)</p>
                <p><strong>Syödessämme</strong> ravintolassa näimme kuuluisan näyttelijän. (While we were eating in a restaurant, we saw a famous actor.)</p>
                <p>Hän hymyili <strong>tullessaan</strong> sisään. (He/she smiled when coming in.)</p>
            </div>
            
            <h4>Instructive Case (-n)</h4>
            <p>The instructive form expresses "by" or "by means of" doing something.</p>
            
            <div class="example-box">
                <p>Hän vastasi <strong>hymyillen</strong>. (He/she answered smiling / by smiling.)</p>
                <p>Opin suomea <strong>lukien</strong> kirjoja. (I learn Finnish by reading books.)</p>
                <p>He tulivat <strong>juosten</strong>. (They came running.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Third Infinitive</h3>
            <p>The third infinitive is one of the most commonly used infinitive forms in Finnish. It's formed by adding -ma/-mä to the verb stem, followed by a case ending.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>3rd Infinitive Base</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhuma-</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syömä-</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tule-</td>
                    <td>tulema-</td>
                </tr>
                <tr>
                    <td>haluta (to want)</td>
                    <td>halua-</td>
                    <td>haluama-</td>
                </tr>
            </table>
            
            <h4>Common Case Forms of the Third Infinitive</h4>
            <table class="example-table">
                <tr>
                    <th>Case</th>
                    <th>Ending</th>
                    <th>Example</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>Illative</td>
                    <td>-an/-än</td>
                    <td>puhumaan</td>
                    <td>to speak (movement into action)</td>
                </tr>
                <tr>
                    <td>Inessive</td>
                    <td>-ssa/-ssä</td>
                    <td>puhumassa</td>
                    <td>speaking (being in the middle of action)</td>
                </tr>
                <tr>
                    <td>Elative</td>
                    <td>-sta/-stä</td>
                    <td>puhumasta</td>
                    <td>from speaking (movement out of action)</td>
                </tr>
                <tr>
                    <td>Adessive</td>
                    <td>-lla/-llä</td>
                    <td>puhumalla</td>
                    <td>by speaking (means of action)</td>
                </tr>
                <tr>
                    <td>Abessive</td>
                    <td>-tta/-ttä</td>
                    <td>puhumatta</td>
                    <td>without speaking</td>
                </tr>
                <tr>
                    <td>Instructive</td>
                    <td>-n</td>
                    <td>puhuman</td>
                    <td>by means of speaking (rare)</td>
                </tr>
            </table>
            
            <h4>Uses of the Third Infinitive</h4>
            
            <h5>Illative (-maan/-mään)</h5>
            <div class="example-box">
                <p>Menen <strong>uimaan</strong>. (I'm going to swim.)</p>
                <p>Hän oppi <strong>puhumaan</strong> suomea. (He/she learned to speak Finnish.)</p>
                <p>Aloitan <strong>opiskelemaan</strong> suomea. (I'll start to study Finnish.)</p>
            </div>
            
            <h5>Inessive (-massa/-mässä)</h5>
            <div class="example-box">
                <p>Olen <strong>lukemassa</strong> kirjaa. (I am reading a book. / I am in the process of reading a book.)</p>
                <p>Hän on <strong>syömässä</strong> ravintolassa. (He/she is eating in a restaurant.)</p>
                <p>He ovat <strong>opiskelemassa</strong> suomea. (They are studying Finnish.)</p>
            </div>
            
            <h5>Elative (-masta/-mästä)</h5>
            <div class="example-box">
                <p>Tulen <strong>uimasta</strong>. (I'm coming from swimming.)</p>
                <p>Hän lakkasi <strong>puhumasta</strong>. (He/she stopped speaking.)</p>
                <p>Estän häntä <strong>lähtemästä</strong>. (I prevent him/her from leaving.)</p>
            </div>
            
            <h5>Adessive (-malla/-mällä)</h5>
            <div class="example-box">
                <p><strong>Lukemalla</strong> opin suomea. (By reading I learn Finnish.)</p>
                <p>Hän ansaitsee rahaa <strong>opettamalla</strong> suomea. (He/she earns money by teaching Finnish.)</p>
            </div>
            
            <h5>Abessive (-matta/-mättä)</h5>
            <div class="example-box">
                <p>Hän lähti <strong>sanomatta</strong> mitään. (He/she left without saying anything.)</p>
                <p>En voi elää <strong>näkemättä</strong> sinua. (I can't live without seeing you.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Fourth Infinitive</h3>
            <p>The fourth infinitive is formed by adding -minen to the verb stem. It functions more like a noun and can be declined in all cases.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>4th Infinitive (Nominative)</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhuminen (speaking)</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syöminen (eating)</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tule-</td>
                    <td>tuleminen (coming)</td>
                </tr>
                <tr>
                    <td>haluta (to want)</td>
                    <td>halua-</td>
                    <td>haluaminen (wanting)</td>
                </tr>
            </table>
            
            <h4>Case Forms of the Fourth Infinitive</h4>
            <p>The fourth infinitive can be declined in all cases like a regular noun.</p>
            
            <table class="example-table">
                <tr>
                    <th>Case</th>
                    <th>puhuminen (speaking)</th>
                </tr>
                <tr>
                    <td>Nominative</td>
                    <td>puhuminen</td>
                </tr>
                <tr>
                    <td>Genitive</td>
                    <td>puhumisen</td>
                </tr>
                <tr>
                    <td>Partitive</td>
                    <td>puhumista</td>
                </tr>
                <tr>
                    <td>Inessive</td>
                    <td>puhumisessa</td>
                </tr>
                <tr>
                    <td>Elative</td>
                    <td>puhumisesta</td>
                </tr>
                <tr>
                    <td>Illative</td>
                    <td>puhumiseen</td>
                </tr>
                <tr>
                    <td>Adessive</td>
                    <td>puhumisella</td>
                </tr>
                <tr>
                    <td>Ablative</td>
                    <td>puhumiselta</td>
                </tr>
                <tr>
                    <td>Allative</td>
                    <td>puhumiselle</td>
                </tr>
            </table>
            
            <h4>Uses of the Fourth Infinitive</h4>
            <div class="example-box">
                <p><strong>Puhuminen</strong> on tärkeää. (Speaking is important.)</p>
                <p>Pidän <strong>lukemisesta</strong>. (I like reading.)</p>
                <p><strong>Uiminen</strong> on hauskaa. (Swimming is fun.)</p>
                <p>Keskityn <strong>opiskelemiseen</strong>. (I concentrate on studying.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Fifth Infinitive</h3>
            <p>The fifth infinitive is the rarest of all infinitives. It's formed by adding -maisilla/-mäisillä to the verb stem and is used to express that someone is about to do something.</p>
            
            <h4>Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Verb</th>
                    <th>Stem</th>
                    <th>5th Infinitive</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhu-</td>
                    <td>puhumaisillaan (about to speak)</td>
                </tr>
                <tr>
                    <td>syödä (to eat)</td>
                    <td>syö-</td>
                    <td>syömäisillään (about to eat)</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tule-</td>
                    <td>tulemaisillaan (about to come)</td>
                </tr>
                <tr>
                    <td>lähteä (to leave)</td>
                    <td>lähte-</td>
                    <td>lähtemäisillään (about to leave)</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> The fifth infinitive is almost always used with the possessive suffix and in the adessive case (-lla/-llä).</p>
            </div>
            
            <h4>Uses of the Fifth Infinitive</h4>
            <div class="example-box">
                <p>Olin <strong>nukahtamaisillani</strong>, kun puhelin soi. (I was about to fall asleep when the phone rang.)</p>
                <p>Hän oli <strong>lähtemäisillään</strong>, kun saavuin. (He/she was about to leave when I arrived.)</p>
                <p>Aurinko on <strong>laskemaisillaan</strong>. (The sun is about to set.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Summary of Finnish Infinitives</h3>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>Formation</th>
                    <th>Main Uses</th>
                    <th>Example</th>
                </tr>
                <tr>
                    <td>1st Infinitive (Short)</td>
                    <td>Dictionary form</td>
                    <td>Basic infinitive, after modal verbs</td>
                    <td>Haluan puhua suomea.</td>
                </tr>
                <tr>
                    <td>1st Infinitive (Translative)</td>
                    <td>-kse- + possessive suffix</td>
                    <td>Purpose ("in order to")</td>
                    <td>Menen Suomeen oppiakseni suomea.</td>
                </tr>
                <tr>
                    <td>2nd Infinitive (Inessive)</td>
                    <td>stem + -e- + -ssa/-ssä</td>
                    <td>When/while doing something</td>
                    <td>Puhuessani suomea teen virheitä.</td>
                </tr>
                <tr>
                    <td>2nd Infinitive (Instructive)</td>
                    <td>stem + -e- + -n</td>
                    <td>By doing something</td>
                    <td>Hän vastasi hymyillen.</td>
                </tr>
                <tr>
                    <td>3rd Infinitive (Illative)</td>
                    <td>stem + -ma/-mä + -an/-än</td>
                    <td>Movement into action</td>
                    <td>Menen uimaan.</td>
                </tr>
                <tr>
                    <td>3rd Infinitive (Inessive)</td>
                    <td>stem + -ma/-mä + -ssa/-ssä</td>
                    <td>Being in the middle of action</td>
                    <td>Olen lukemassa kirjaa.</td>
                </tr>
                <tr>
                    <td>3rd Infinitive (Elative)</td>
                    <td>stem + -ma/-mä + -sta/-stä</td>
                    <td>Movement out of action</td>
                    <td>Tulen uimasta.</td>
                </tr>
                <tr>
                    <td>3rd Infinitive (Adessive)</td>
                    <td>stem + -ma/-mä + -lla/-llä</td>
                    <td>By means of action</td>
                    <td>Lukemalla opin suomea.</td>
                </tr>
                <tr>
                    <td>3rd Infinitive (Abessive)</td>
                    <td>stem + -ma/-mä + -tta/-ttä</td>
                    <td>Without doing something</td>
                    <td>Hän lähti sanomatta mitään.</td>
                </tr>
                <tr>
                    <td>4th Infinitive</td>
                    <td>stem + -minen</td>
                    <td>Verbal noun</td>
                    <td>Puhuminen on tärkeää.</td>
                </tr>
                <tr>
                    <td>5th Infinitive</td>
                    <td>stem + -maisilla/-mäisillä</td>
                    <td>Being about to do something</td>
                    <td>Olin nukahtamaisillani.</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Infinitive Form</th>
                </tr>
                <tr>
                    <td>Haluan oppia puhumaan suomea.</td>
                    <td>I want to learn to speak Finnish.</td>
                    <td>1st Infinitive + 3rd Infinitive Illative</td>
                </tr>
                <tr>
                    <td>Menen Suomeen oppiakseni suomea.</td>
                    <td>I'm going to Finland in order to learn Finnish.</td>
                    <td>1st Infinitive Translative</td>
                </tr>
                <tr>
                    <td>Puhuessani suomea teen paljon virheitä.</td>
                    <td>When I speak Finnish, I make many mistakes.</td>
                    <td>2nd Infinitive Inessive</td>
                </tr>
                <tr>
                    <td>Hän vastasi hymyillen.</td>
                    <td>He/she answered smiling.</td>
                    <td>2nd Infinitive Instructive</td>
                </tr>
                <tr>
                    <td>Menen uimaan.</td>
                    <td>I'm going to swim.</td>
                    <td>3rd Infinitive Illative</td>
                </tr>
                <tr>
                    <td>Olen lukemassa kirjaa.</td>
                    <td>I am reading a book.</td>
                    <td>3rd Infinitive Inessive</td>
                </tr>
                <tr>
                    <td>Tulen uimasta.</td>
                    <td>I'm coming from swimming.</td>
                    <td>3rd Infinitive Elative</td>
                </tr>
                <tr>
                    <td>Lukemalla opin suomea.</td>
                    <td>By reading I learn Finnish.</td>
                    <td>3rd Infinitive Adessive</td>
                </tr>
                <tr>
                    <td>Hän lähti sanomatta mitään.</td>
                    <td>He/she left without saying anything.</td>
                    <td>3rd Infinitive Abessive</td>
                </tr>
                <tr>
                    <td>Puhuminen on tärkeää.</td>
                    <td>Speaking is important.</td>
                    <td>4th Infinitive</td>
                </tr>
                <tr>
                    <td>Olin nukahtamaisillani, kun puhelin soi.</td>
                    <td>I was about to fall asleep when the phone rang.</td>
                    <td>5th Infinitive</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












