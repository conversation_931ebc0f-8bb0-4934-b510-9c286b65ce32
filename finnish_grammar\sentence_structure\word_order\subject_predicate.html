﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subject and Predicate in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Subject and Predicate</span>
        </div>
        
        <section class="grammar-section">
            <h2>Subject and Predicate in Finnish</h2>
            <p>Understanding how subjects and predicates work in Finnish sentences is essential for mastering Finnish grammar. This page explains the characteristics of subjects and predicates in Finnish and how they interact in sentences.</p>
        </section>

        <section class="grammar-category">
            <h3>THE SUBJECT</h3>
            
            <div class="grammar-content">
                <p>The subject (subjekti) in a Finnish sentence is the person or thing that performs the action or is in the state described by the verb. The subject typically:</p>
                
                <ul>
                    <li>Is in the nominative case (basic form without endings)</li>
                    <li>Agrees with the verb in person and number</li>
                    <li>Usually comes at the beginning of the sentence in neutral word order</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä puhun suomea.</span> <span class="english">I speak Finnish.</span></p>
                    <p><span class="finnish">Koira juoksee puistossa.</span> <span class="english">The dog runs in the park.</span></p>
                    <p><span class="finnish">Lapset leikkivät pihalla.</span> <span class="english">The children play in the yard.</span></p>
                </div>
                
                <p>In these examples, "minä," "koira," and "lapset" are the subjects.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SUBJECT TYPES</h3>
            
            <div class="grammar-content">
                <h4>1. Nominal subjects</h4>
                <p>These are the most common type of subjects, consisting of nouns or pronouns:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Matti lukee kirjaa.</span> <span class="english">Matti is reading a book.</span></p>
                    <p><span class="finnish">Tämä talo on vanha.</span> <span class="english">This house is old.</span></p>
                </div>
                
                <h4>2. Compound subjects</h4>
                <p>When two or more nouns or pronouns function together as the subject, they form a compound subject:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Matti ja Liisa menevät kauppaan.</span> <span class="english">Matti and Liisa go to the store.</span></p>
                    <p><span class="finnish">Kissa ja koira nukkuvat.</span> <span class="english">The cat and dog are sleeping.</span></p>
                </div>
                
                <h4>3. Implied subjects</h4>
                <p>In Finnish, the subject can often be omitted when it's a personal pronoun, as the verb form already indicates the person:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Puhun suomea.</span> <span class="english">I speak Finnish. (minä is implied)</span></p>
                    <p><span class="finnish">Menemme kotiin.</span> <span class="english">We are going home. (me is implied)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>THE PREDICATE</h3>
            
            <div class="grammar-content">
                <p>The predicate (predikaatti) is the part of the sentence that contains the verb and tells something about the subject. In Finnish, the predicate:</p>
                
                <ul>
                    <li>Always contains a finite verb (a verb that shows tense and agrees with the subject)</li>
                    <li>May include other elements like objects, complements, or adverbials</li>
                    <li>Agrees with the subject in person and number</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä <strong>puhun</strong> suomea.</span> <span class="english">I <strong>speak</strong> Finnish.</span></p>
                    <p><span class="finnish">Hän <strong>on opettaja</strong>.</span> <span class="english">He/she <strong>is a teacher</strong>.</span></p>
                    <p><span class="finnish">Lapset <strong>leikkivät pihalla</strong>.</span> <span class="english">The children <strong>play in the yard</strong>.</span></p>
                </div>
                
                <p>In these examples, the bolded parts are the predicates.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SUBJECT-VERB AGREEMENT</h3>
            
            <div class="grammar-content">
                <p>In Finnish, the verb must agree with the subject in person and number:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>1st (I/we)</td>
                        <td>Minä puhun</td>
                        <td>Me puhumme</td>
                    </tr>
                    <tr>
                        <td>2nd (you)</td>
                        <td>Sinä puhut</td>
                        <td>Te puhutte</td>
                    </tr>
                    <tr>
                        <td>3rd (he/she/it/they)</td>
                        <td>Hän puhuu</td>
                        <td>He puhuvat</td>
                    </tr>
                </table>
                
                <p>With compound subjects, the verb is typically in the plural form:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Matti ja Liisa puhuvat suomea.</span> <span class="english">Matti and Liisa speak Finnish.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL SUBJECT CASES</h3>
            
            <div class="grammar-content">
                <h4>1. Existential sentences</h4>
                <p>In existential sentences (sentences that express existence), the subject can be in the partitive case and doesn't necessarily agree with the verb:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Pöydällä on kirja.</span> <span class="english">There is a book on the table.</span></p>
                    <p><span class="finnish">Pöydällä on kirjoja.</span> <span class="english">There are books on the table.</span></p>
                </div>
                
                <p>In both examples, the verb "on" (is) remains in the singular form regardless of whether the subject is singular or plural.</p>
                
                <h4>2. Impersonal constructions</h4>
                <p>Finnish has several impersonal constructions where there is no explicit subject:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Sataa.</span> <span class="english">It's raining.</span></p>
                    <p><span class="finnish">Täytyy mennä.</span> <span class="english">One must go.</span></p>
                    <p><span class="finnish">On kylmä.</span> <span class="english">It's cold.</span></p>
                </div>
                
                <h4>3. Passive voice</h4>
                <p>The Finnish passive doesn't have an explicit subject and is used to express actions where the doer is not specified:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Suomessa puhutaan suomea.</span> <span class="english">In Finland, Finnish is spoken.</span></p>
                    <p><span class="finnish">Kaupassa myydään ruokaa.</span> <span class="english">Food is sold in the store.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PREDICATE TYPES</h3>
            
            <div class="grammar-content">
                <h4>1. Verbal predicates</h4>
                <p>These consist of a main verb that expresses an action:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen.</span> <span class="english">I read.</span></p>
                    <p><span class="finnish">Hän juoksee.</span> <span class="english">He/she runs.</span></p>
                </div>
                
                <h4>2. Nominal predicates</h4>
                <p>These consist of the verb "olla" (to be) plus a complement that describes the subject:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän on opettaja.</span> <span class="english">He/she is a teacher.</span></p>
                    <p><span class="finnish">Talo on punainen.</span> <span class="english">The house is red.</span></p>
                </div>
                
                <h4>3. Compound predicates</h4>
                <p>These include auxiliary verbs plus a main verb:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä olen lukenut kirjan.</span> <span class="english">I have read the book.</span></p>
                    <p><span class="finnish">Hän voi tulla huomenna.</span> <span class="english">He/she can come tomorrow.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















