<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio - Opiskelen Suomea</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="audio-styles.css">
    <link rel="stylesheet" href="css/pagination.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    
    <style>
        /* Make sure the audio content is displayed by default */
        .audio-container {
            display: block !important;
            margin-top: 30px;
            padding: 20px;
        }
        
        .audio-tab-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        /* Theme toggle styles */
        .theme-toggle-container {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1000;
        }
        
        .theme-toggle-button {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 44px !important;
            height: 44px !important;
            border-radius: 4px !important;
            background-color: rgba(0, 0, 0, 0.05) !important;
            color: inherit !important;
            font-size: 1.2rem !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .theme-toggle-button:hover {
            background-color: rgba(0, 0, 0, 0.1) !important;
            transform: scale(1.1) !important;
        }
        
        .dark-mode .theme-toggle-button {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: #fff !important;
        }
        
        /* Desktop theme toggle styles */
        .desktop-only-theme-toggle {
            display: none !important; /* Hidden by default */
        }
        
        /* Mobile theme toggle styles */
        .mobile-only-theme-toggle {
            display: flex !important; /* Shown by default on mobile */
        }
        
        /* Position the toggle buttons in the navbar */
        .mobile-only-theme-toggle {
            position: absolute !important;
            right: 60px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }
        
        /* Media queries for responsive behavior */
        @media (min-width: 768px) {
            /* On desktop */
            .desktop-only-theme-toggle {
                display: inline-flex !important; /* Show desktop toggle */
                position: absolute !important;
                right: 20px !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
            }
            
            .mobile-only-theme-toggle {
                display: none !important; /* Hide mobile toggle */
            }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .audio-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="audio-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="audio.html" class="active">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="index.html#daily-life">Daily Life</a>
                        <a href="index.html#web-development">Web Development</a>
                        <a href="index.html#cleaner">Cleaner</a>
                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="audio-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="audio-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container audio-container">
        <h1>Finnish Audio Learning</h1>
        <p>Listen to Finnish audio lessons and practice your listening skills. Use the controls below to navigate through the audio tracks.</p>
        
        <div class="audio-tab-container">
            <!-- Audio Controls -->
            <div class="audio-controls">
                <button id="previous-button" class="audio-control-button">
                    <i class="fas fa-step-backward"></i> <span class="button-text">Previous</span>
                </button>
                <button id="play-mode-button" class="audio-control-button">
                    <i class="fas fa-sync-alt"></i> <span class="button-text">Repeat One</span>
                </button>
                <button id="next-button" class="audio-control-button">
                    <i class="fas fa-step-forward"></i> <span class="button-text">Next</span>
                </button>
                <button id="stop-button" class="audio-control-button">
                    <i class="fas fa-stop-circle"></i> <span class="button-text">Stop</span>
                </button>
                <button id="subtitle-button" class="audio-control-button">
                    <i class="fas fa-closed-captioning"></i> <span class="button-text">Subtitles</span>
                </button>
            </div>
            
            <!-- Audio List -->
            <div id="audio-list-container" class="audio-list-container">
                <!-- Audio items will be dynamically added here by JavaScript -->
            </div>
            
            <!-- Subtitle Container -->
            <div id="subtitle-container" class="subtitle-container">
                <!-- Subtitles will be displayed here -->
            </div>
        </div>
    </div>

    <!-- Highlight mode notification -->
    <div id="highlight-notification" class="highlight-notification">
        <i class="fas fa-highlighter"></i> Highlight mode enabled. Select text to highlight it.
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Opiskelen Suomea provides Finnish language learning resources specifically designed for professional environments and workplace integration in Finland.</p>
                <div class="footer-social">
                    <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Categories</h3>
                <ul class="footer-links">
                    <li><a href="index.html#daily-life"><i class="fas fa-angle-right"></i> Daily Life</a></li>
                    <li><a href="index.html#web-development"><i class="fas fa-angle-right"></i> Web Development</a></li>
                    <li><a href="index.html#cleaner"><i class="fas fa-angle-right"></i> Cleaner</a></li>
                    <li><a href="index.html#kitchen-assistant"><i class="fas fa-angle-right"></i> Kitchen Assistant</a></li>
                    <li><a href="index.html#warehouse"><i class="fas fa-angle-right"></i> Warehouse</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html"><i class="fas fa-angle-right"></i> Home</a></li>
                    <li><a href="audio.html"><i class="fas fa-angle-right"></i> Audio</a></li>
                    <li><a href="video.html"><i class="fas fa-angle-right"></i> Videos</a></li>
                    <li><a href="finnish_grammar/index.html"><i class="fas fa-angle-right"></i> Grammar</a></li>
                    <li><a href="index.html#about"><i class="fas fa-angle-right"></i> About</a></li>
                </ul>
            </div>

            <div class="footer-section footer-contact">
                <h3>Contact Us</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> +358 40 123 4567</p>
                <p><i class="fas fa-map-marker-alt"></i> Helsinki, Finland</p>
            </div>

            <div class="footer-bottom">
                <p class="copyright">&copy; 2024 Opiskelen Suomea. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="audiolist.js"></script>
    <script src="highlight.js"></script>
    
    <!-- Initialize audio functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Audio page loaded, initializing audio functionality");
            
            // Apply highlight mode from localStorage
            if (localStorage.getItem('highlightMode') === 'enabled') {
                document.body.classList.add('highlight-mode');
                // Set cursor to indicate highlight mode
                document.body.style.cursor = 'text';
                // Add active-tool class to the highlight button
                const highlightBtn = document.getElementById('audio-toggle-highlight');
                if (highlightBtn) {
                    highlightBtn.classList.add('active-tool');
                }
                
                // Add event listener for text selection
                document.addEventListener('mouseup', handleTextSelection);
            }
            
            // Initialize theme toggle
            const themeToggleButtons = [
                document.getElementById('audio-toggle-dark'),
                document.getElementById('audio-toggle-dark-mobile')
            ];
            
            themeToggleButtons.forEach(button => {
                if (button) {
                    button.addEventListener('click', function() {
                        document.body.classList.toggle('dark-theme');
                        updateThemeIcon();
                    });
                }
            });
            
            function updateThemeIcon() {
                const isDarkTheme = document.body.classList.contains('dark-theme');
                themeToggleButtons.forEach(button => {
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            icon.className = isDarkTheme ? 'fas fa-sun' : 'fas fa-moon';
                        }
                    }
                });
            }
            
            // Initialize highlight mode
            const highlightButton = document.getElementById('audio-toggle-highlight');
            const highlightNotification = document.getElementById('highlight-notification');
            
            if (highlightButton) {
                highlightButton.addEventListener('click', function() {
                    document.body.classList.toggle('highlight-mode');
                    // Toggle active-tool class on the button
                    this.classList.toggle('active-tool');
                    
                    // Change cursor to indicate highlight mode
                    if (document.body.classList.contains('highlight-mode')) {
                        document.body.style.cursor = 'text';
                        
                        // Show notification
                        if (highlightNotification) {
                            highlightNotification.style.display = 'block';
                            setTimeout(() => {
                                highlightNotification.style.opacity = '1';
                            }, 10);
                            setTimeout(() => {
                                highlightNotification.style.opacity = '0';
                                setTimeout(() => {
                                    highlightNotification.style.display = 'none';
                                }, 500);
                            }, 3000);
                        }
                        
                        // Add event listener for text selection
                        document.addEventListener('mouseup', handleTextSelection);
                    } else {
                        document.body.style.cursor = '';
                        
                        // Remove event listener for text selection
                        document.removeEventListener('mouseup', handleTextSelection);
                    }
                    
                    // Save highlight state to localStorage
                    localStorage.setItem('highlightMode', document.body.classList.contains('highlight-mode') ? 'enabled' : 'disabled');
                });
            }
            
            // Function to handle text selection
            function handleTextSelection() {
                if (!document.body.classList.contains('highlight-mode')) return;
                
                const selection = window.getSelection();
                if (selection.toString().length > 0) {
                    // Create a span element to wrap the selected text
                    const range = selection.getRangeAt(0);
                    const selectedText = range.extractContents();
                    const span = document.createElement('span');
                    span.className = 'user-highlight';
                    span.appendChild(selectedText);
                    range.insertNode(span);
                    
                    // Clear the selection
                    selection.removeAllRanges();
                }
            }
            
            // Check if audiolist.js is loaded and initialize audio functionality
            // Add a small delay to ensure all DOM elements are ready
            setTimeout(function() {
                if (typeof window.initializeAudioList === 'function') {
                    console.log("Calling initializeAudioList function...");
                    window.initializeAudioList();
                } else {
                    console.error("audiolist.js not loaded properly - initializeAudioList function not found");
                }
            }, 500);
        });
            // Function to toggle text highlighting
        function toggleHighlight() {
            const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
            
            if (document.body.classList.contains('highlight-mode') || document.body.classList.contains('highlight-enabled')) {
                // Disable highlighting
                document.body.classList.remove('highlight-mode');
                document.body.classList.remove('highlight-enabled');
                localStorage.setItem('highlightMode', 'disabled');
                
                if (highlightButton) {
                    highlightButton.classList.remove('active');
                    highlightButton.classList.remove('active-tool');
                }
                
                // Remove background color from Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = '';
                });
            } else {
                // Enable highlighting
                document.body.classList.add(document.querySelector('.highlight-mode') !== null ? 'highlight-mode' : 'highlight-enabled');
                localStorage.setItem('highlightMode', 'enabled');
                
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                
                // Add background color to Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                });
            }
        }
        
        // Check for highlight settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Migrate old settings if needed
            if (!localStorage.getItem('highlightMode')) {
                const oldHighlightState = localStorage.getItem('highlight');
                if (oldHighlightState === 'enabled') {
                    localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') {
                    localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
            
            // Apply highlight state on page load
            const isHighlighted = localStorage.getItem('highlightMode') === 'enabled';
            if (isHighlighted) {
                const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                
                document.body.classList.add(document.querySelector('.highlight-mode') !== null ? 'highlight-mode' : 'highlight-enabled');
                
                // Add background color to Finnish words if they exist
                const finnishWords = document.querySelectorAll('.finnish');
                if (finnishWords.length > 0) {
                    finnishWords.forEach(function(element) {
                        element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                    });
                }
            }
        });
        
        // Set up dark mode toggle
        const darkModeToggle = document.getElementById('audio-toggle-dark');
        const darkModeToggleMobile = document.getElementById('audio-toggle-dark-mobile');
        
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
            
            // Update icon
            const isDarkMode = document.body.classList.contains('dark-mode');
            const updateIcon = (button) => {
                if (button) {
                    const icon = button.querySelector('i');
                    if (icon) {
                        icon.classList.remove(isDarkMode ? 'fa-moon' : 'fa-sun');
                        icon.classList.add(isDarkMode ? 'fa-sun' : 'fa-moon');
                    }
                }
            };
            
            // Update both buttons if they exist
            if (darkModeToggle) updateIcon(darkModeToggle);
            if (darkModeToggleMobile) updateIcon(darkModeToggleMobile);
            
            // Save preference
            localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
            console.log('Dark mode toggled:', isDarkMode ? 'enabled' : 'disabled');
        }
        
        // Add event listeners to both buttons if they exist
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
            console.log('Dark mode toggle desktop button initialized');
        }
        
        if (darkModeToggleMobile) {
            darkModeToggleMobile.addEventListener('click', toggleDarkMode);
            console.log('Dark mode toggle mobile button initialized');
        }
        
        // Check for dark mode preference on page load
        document.addEventListener('DOMContentLoaded', function() {
            const darkModePreference = localStorage.getItem('darkMode');
            if (darkModePreference === 'enabled') {
                document.body.classList.add('dark-mode');
                
                // Update icons
                const updateIcon = (button) => {
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            icon.classList.remove('fa-moon');
                            icon.classList.add('fa-sun');
                        }
                    }
                };
                
                updateIcon(document.getElementById('audio-toggle-dark'));
                updateIcon(document.getElementById('audio-toggle-dark-mobile'));
                
                console.log('Dark mode applied from localStorage');
            }
        });
        </script>
</body>

<script>
// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        console.log("Setting up mobile menu toggle");
        mobileMenuToggle.addEventListener('click', function(e) {
            console.log("Mobile menu toggle clicked");
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Handle dropdown menus in mobile view
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const dropbtn = dropdown.querySelector('.dropbtn');
        if (dropbtn) {
            dropbtn.addEventListener('click', function(e) {
                // Only in mobile view
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('active');
                }
            });
        }
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (navLinks && navLinks.classList.contains('show')) {
            // Check if click is outside the nav menu
            if (!navLinks.contains(e.target) && e.target !== mobileMenuToggle) {
                navLinks.classList.remove('show');
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                    
                    // Change icon back to bars
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-bars';
                    }
                }
            }
        }
    });
});
</script>
</html>

