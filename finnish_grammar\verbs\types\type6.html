﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type 6 Verbs - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="type6-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="type6-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="type6-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Type 6 Verbs</span>
        </div>
        
        <section class="grammar-section">
            <h2>Type 6 Verbs: -eta/-etä</h2>
            <p>Type 6 verbs in Finnish end in -eta or -etä (depending on vowel harmony). These verbs have a special conjugation pattern where the -et- changes to -ene- in most forms. Type 6 verbs often describe a change of state or becoming something.</p>
            
            <h3>Characteristics</h3>
            <ul>
                <li>End in -eta or -etä</li>
                <li>The stem is formed by replacing -eta/-etä with -ene-</li>
                <li>Examples: vanheta (to grow old), kylmetä (to get cold), pidetä (to lengthen)</li>
                <li>Often describe a change of state or becoming something</li>
            </ul>
            
            <div class="example-box">
                <p><strong>vanheta</strong> (to grow old) → stem: <strong>vanhene-</strong></p>
                <p><strong>kylmetä</strong> (to get cold) → stem: <strong>kylmene-</strong></p>
                <p><strong>pidetä</strong> (to lengthen) → stem: <strong>pidene-</strong></p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The key characteristic of Type 6 verbs is the change from -et- to -ene- in the stem.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Present Tense Conjugation</h3>
            <p>To form the present tense, add the personal endings to the stem:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>vanheta (to grow old)</th>
                    <th>kylmetä (to get cold)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-n</td>
                    <td>vanhenen</td>
                    <td>kylmenen</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-t</td>
                    <td>vanhenet</td>
                    <td>kylmenet</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>-e</td>
                    <td>vanhenee</td>
                    <td>kylmenee</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-mme</td>
                    <td>vanhenemme</td>
                    <td>kylmenemme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-tte</td>
                    <td>vanhenette</td>
                    <td>kylmenette</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-vat/-vät</td>
                    <td>vanhenevat</td>
                    <td>kylmenevät</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> For the 3rd person singular (hän), the final -e of the stem is lengthened (e → ee).</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Past Tense Conjugation</h3>
            <p>To form the past tense (imperfect), add -i- to the stem (after removing the final -e), followed by the personal endings:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>vanheta (to grow old)</th>
                    <th>kylmetä (to get cold)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>vanhenin</td>
                    <td>kylmenin</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>vanhenit</td>
                    <td>kylmenit</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>vanheni</td>
                    <td>kylmeni</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>vanhenimme</td>
                    <td>kylmenimme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>vanhenitte</td>
                    <td>kylmenitte</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>vanhenivat</td>
                    <td>kylmenivät</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In the past tense, the final -e of the stem is dropped before adding the -i-.</p>
                <p>For example: vanhene- → vanhen- + i → vanheni-</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Forms</h3>
            <p>To form negative sentences, use the negative verb "ei" conjugated for person, followed by the stem:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative verb</th>
                    <th>vanheta (to grow old)</th>
                    <th>kylmetä (to get cold)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en</td>
                    <td>en vanhene</td>
                    <td>en kylmene</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et</td>
                    <td>et vanhene</td>
                    <td>et kylmene</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei</td>
                    <td>ei vanhene</td>
                    <td>ei kylmene</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme</td>
                    <td>emme vanhene</td>
                    <td>emme kylmene</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette</td>
                    <td>ette vanhene</td>
                    <td>ette kylmene</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät</td>
                    <td>eivät vanhene</td>
                    <td>eivät kylmene</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Meaning and Usage</h3>
            <p>Type 6 verbs typically describe a change of state or becoming something. They are often derived from adjectives:</p>
            
            <table class="example-table">
                <tr>
                    <th>Adjective</th>
                    <th>Type 6 Verb</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>vanha (old)</td>
                    <td>vanheta</td>
                    <td>to grow old</td>
                </tr>
                <tr>
                    <td>kylmä (cold)</td>
                    <td>kylmetä</td>
                    <td>to get cold</td>
                </tr>
                <tr>
                    <td>pitkä (long)</td>
                    <td>pidetä</td>
                    <td>to lengthen</td>
                </tr>
                <tr>
                    <td>lyhyt (short)</td>
                    <td>lyhetä</td>
                    <td>to shorten</td>
                </tr>
                <tr>
                    <td>pehmeä (soft)</td>
                    <td>pehmetä</td>
                    <td>to soften</td>
                </tr>
                <tr>
                    <td>kova (hard)</td>
                    <td>koveta</td>
                    <td>to harden</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Common Type 6 Verbs</h3>
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>vanheta</td>
                    <td>to grow old</td>
                    <td>kylmetä</td>
                    <td>to get cold</td>
                </tr>
                <tr>
                    <td>pidetä</td>
                    <td>to lengthen</td>
                    <td>lyhetä</td>
                    <td>to shorten</td>
                </tr>
                <tr>
                    <td>pehmetä</td>
                    <td>to soften</td>
                    <td>koveta</td>
                    <td>to harden</td>
                </tr>
                <tr>
                    <td>lämmetä</td>
                    <td>to warm up</td>
                    <td>suureta</td>
                    <td>to grow bigger</td>
                </tr>
                <tr>
                    <td>pienetä</td>
                    <td>to get smaller</td>
                    <td>parantua</td>
                    <td>to get better</td>
                </tr>
                <tr>
                    <td>huonontua</td>
                    <td>to get worse</td>
                    <td>heiketä</td>
                    <td>to weaken</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Example Sentences</h3>
            <div class="example-box">
                <p>Minä <strong>vanhenen</strong> joka päivä. (I grow older every day.)</p>
                <p>Ilma <strong>kylmenee</strong> syksyllä. (The air gets cold in autumn.)</p>
                <p>Päivät <strong>pidenevät</strong> keväällä. (Days lengthen in spring.)</p>
                <p>Metalli <strong>pehmenee</strong> kuumuudessa. (Metal softens in heat.)</p>
                <p>Minä en <strong>vanhene</strong> nopeasti. (I don't age quickly.)</p>
                <p>Vesi ei <strong>kylmene</strong> helposti. (Water doesn't get cold easily.)</p>
                <p>Minä <strong>vanhenin</strong> paljon viime vuonna. (I aged a lot last year.)</p>
                <p>Ilma <strong>kylmeni</strong> yöllä. (The air got cold during the night.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Comparison with Type 5 Verbs</h3>
            <p>Type 6 verbs are similar to Type 5 verbs in that both involve a change in the stem. However, the patterns are different:</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Infinitive Ending</th>
                    <th>Stem Change</th>
                    <th>Example</th>
                </tr>
                <tr>
                    <td>Type 5</td>
                    <td>-ita/-itä</td>
                    <td>-ita/-itä → -itse-</td>
                    <td>tarvita → tarvitse-</td>
                </tr>
                <tr>
                    <td>Type 6</td>
                    <td>-eta/-etä</td>
                    <td>-eta/-etä → -ene-</td>
                    <td>vanheta → vanhene-</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















