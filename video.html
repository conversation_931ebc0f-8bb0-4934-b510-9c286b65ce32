<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Videos - Opiskelen <PERSON></title>
    
    <!-- Core styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    
    <!-- YouTube styles -->
    <link rel="stylesheet" href="youtube-styles.css">
    
    <style>
        /* Override styles.css */
        .video-container {
            max-width: 100vw !important;
            width: 100% !important;
            margin: 0 auto !important;
            padding: 0.5rem !important;
            position: relative !important;
            z-index: 1 !important;
            background-color: transparent !important;
            overflow-x: hidden !important;
        }
        
        /* Allow body to scroll naturally */
        html, body {
            overflow-x: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        /* Make sure tabs are not displayed by default */
        .tab-content {
            display: none;
        }
        
        /* Ensure active tabs are properly displayed */
        .tab-content.active {
            display: block !important;
            padding-bottom: 10px !important;
        }
        
        /* Make sure the first YouTube tab is displayed by default */
        #kuulostaa-hyvalta-tab.active {
            display: block !important;
        }
        
        /* Hide the original tab buttons since we've moved them to the navigation bar */
        .video-tab-buttons {
            display: none !important;
            height: 0 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            visibility: hidden !important;
        }
        
        /* Style for theme toggle buttons - unified with other pages */
        .theme-toggle-button,
        .highlight-toggle-button {
            background-color: rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            color: var(--text-color, #333);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .theme-toggle-button:hover,
        .highlight-toggle-button:hover {
            background-color: rgba(0, 0, 0, 0.1);
            color: var(--primary-color, #003580);
        }
        
        /* Style for active highlight tool */
        .highlight-toggle-button.active-tool {
            color: #ffcc00;
            text-shadow: 0 0 3px rgba(255, 204, 0, 0.5);
        }
        
        /* Theme toggle container styles */
        li.theme-toggle-container,
        li.highlight-toggle-container {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1000;
        }
        
        /* Ensure theme toggle buttons are always visible */
        .theme-toggle-button,
        .highlight-toggle-button {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 44px !important;
            height: 44px !important;
            border-radius: 4px !important;
            background-color: rgba(0, 0, 0, 0.05) !important;
            color: inherit !important;
            font-size: 1.2rem !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        /* Add hover effect for toggle buttons */
        .theme-toggle-button:hover,
        .highlight-toggle-button:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
            transform: scale(1.1) !important;
        }
        
        /* Dark mode hover effect */
        .dark-mode .theme-toggle-button:hover,
        .dark-mode .highlight-toggle-button:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: var(--primary-color, #1a5fb4);
        }
        
        /* Mobile-only theme toggle styles */
        .mobile-only-theme-toggle {
            display: none !important;
            visibility: hidden !important;
        }
        
        @media (max-width: 767px) {
            .mobile-only-theme-toggle {
                display: flex !important;
                visibility: visible !important;
                margin-left: auto;
            }
        }
        
        /* Explicitly hide on larger screens */
        @media (min-width: 768px) {
            .mobile-only-theme-toggle {
                display: none !important;
                visibility: hidden !important;
                width: 0 !important;
                height: 0 !important;
                overflow: hidden !important;
                position: absolute !important;
                pointer-events: none !important;
                opacity: 0 !important;
            }
        }
        
        /* Theme toggle container styles */
        .theme-toggle-container {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1000;
        }
        
        /* Override theme toggle button styles to match other pages */
        .theme-toggle-button {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 44px !important;
            height: 44px !important;
            border-radius: 4px !important;
            background-color: rgba(0, 0, 0, 0.05) !important;
            color: var(--text-color, #333) !important;
            font-size: 1.2rem !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .theme-toggle-button:hover {
            background-color: rgba(0, 0, 0, 0.1) !important;
            color: var(--primary-color, #003580) !important;
            transform: none !important;
        }
        
        .dark-mode .theme-toggle-button {
            background-color: rgba(255, 255, 255, 0.05) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: var(--text-color, #e0e0e0) !important;
        }
        
        /* Highlight button styles */
        .highlight-button-container {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1000;
        }
        
        .compact-nav-button {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--text-color);
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0;
            font-size: 1.2rem;
            width: 44px;
            height: 44px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .compact-nav-button:hover {
            background-color: rgba(0, 0, 0, 0.1);
            color: var(--primary-color, #003580);
            border-color: var(--primary-color);
            transform: none;
            box-shadow: none;
        }
        
        .dark-mode .compact-nav-button {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-color, #e0e0e0);
        }
        
        .dark-mode .compact-nav-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color, #1a5fb4);
        }
        
        .compact-nav-button.active-tool {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        /* Mobile styles for theme toggle and highlight buttons */
        @media (max-width: 767px) {
            /* Create a container for the buttons in the navbar */
            .nav-container {
                position: relative !important;
            }
            
            /* Position the theme toggle button */
            .theme-toggle-container {
                position: absolute !important;
                right: 60px !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                z-index: 1002 !important; /* Higher than the mobile menu */
                display: flex !important;
            }
            
            /* Hide highlight button on mobile and tablet */
            .highlight-button-container {
                display: none !important;
            }
            
            /* Make buttons more visible */
            .theme-toggle-button,
            .compact-nav-button {
                background-color: rgba(0, 0, 0, 0.05) !important;
                width: 36px !important;
                height: 36px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            
            /* Dark mode styles */
            .dark-mode .theme-toggle-button,
            .dark-mode .compact-nav-button {
                background-color: rgba(255, 255, 255, 0.1) !important;
                color: #fff !important;
            }
            
            /* Adjust mobile menu toggle position to make room for the buttons */
            .mobile-menu-toggle {
                margin-right: 10px !important;
            }
            
            /* Hide the buttons in the nav-links when menu is expanded */
            .nav-links.show .theme-toggle-container,
            .nav-links.show .highlight-button-container {
                display: none !important;
            }
            
            /* Create copies of the buttons outside of nav-links for mobile */
            .mobile-buttons-container {
                display: flex !important;
                position: absolute !important;
                right: 60px !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                z-index: 1002 !important;
            }
        }
        
        /* Nav container styles to accommodate theme toggle */
        .nav-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            position: relative;
        }
        
        /* Ensure theme toggle containers are always visible */
        .nav-container .theme-toggle-container:not(.mobile-only-theme-toggle),
        .nav-container .highlight-toggle-container {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 1002 !important;
        }
        
        /* Ensure only one theme toggle is visible based on screen size */
        @media (min-width: 768px) {
            /* On desktop, show only the nav menu toggle */
            .nav-links .theme-toggle-container {
                display: flex !important;
                visibility: visible !important;
            }
            
            /* Hide the mobile toggle */
            .mobile-only-theme-toggle {
                display: none !important;
                visibility: hidden !important;
            }
        }
        
        @media (max-width: 767px) {
            /* On mobile, show the mobile toggle */
            .mobile-only-theme-toggle {
                display: flex !important;
                visibility: visible !important;
            }
        }
        
        /* Mobile theme toggle container styles - only apply on mobile */
        @media (max-width: 767px) {
            .mobile-only-theme-toggle {
                position: absolute !important;
                right: 60px !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
        }
        
        /* Ensure desktop screens never show the mobile toggle */
        @media (min-width: 768px) {
            .mobile-only-theme-toggle,
            .theme-toggle-container.mobile-only-theme-toggle {
                display: none !important;
                visibility: hidden !important;
                width: 0 !important;
                height: 0 !important;
                overflow: hidden !important;
                position: absolute !important;
                pointer-events: none !important;
                opacity: 0 !important;
                clip: rect(0, 0, 0, 0) !important;
                margin: -1px !important;
                padding: 0 !important;
                border: 0 !important;
            }
        }
        
        /* Position the toggle buttons in the navbar */
        .nav-container .highlight-toggle-container {
            right: 100px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            position: absolute !important;
        }
        
        /* Logo style */
        .logo {
            order: 1; /* Position first */
            flex-grow: 1; /* Allow it to take available space */
        }
        
        /* Mobile menu toggle style */
        .mobile-menu-toggle {
            order: 4; /* Position after both theme toggles */
            background: none;
            border: none;
            color: inherit;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px 10px;
            display: none; /* Hide by default on desktop */
        }
        
        /* Show mobile menu toggle on mobile */
        @media (max-width: 767px) {
            .mobile-menu-toggle {
                display: block !important; /* Show on mobile */
                margin-left: auto; /* Push to the right */
                z-index: 1001; /* Ensure it's above other elements */
            }
        }
        
        /* Nav links style */
        .nav-links {
            order: 5; /* Position last */
            width: 100%; /* Full width on mobile */
        }
        
        /* Basic styles for nav-links */
        #nav-links {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        /* Ensure nav-links are hidden by default on mobile */
        @media (max-width: 767px) {
            #nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: #fff;
                z-index: 1000;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            
            /* Show nav-links when .show class is applied */
            #nav-links.show {
                display: flex !important;
                flex-direction: column !important;
            }
        }
        
        /* Mobile-only and desktop-only elements */
        .mobile-only {
            display: none !important; /* Hidden by default */
        }
        
        /* Media queries for responsive behavior */
        @media (min-width: 768px) {
            /* On desktop */
            .mobile-only {
                display: none !important; /* Hide mobile elements */
            }
            
            /* Ensure highlight and theme toggles are visible in nav-links on desktop */
            .nav-links .highlight-button-container,
            .nav-links .theme-toggle-container {
                display: flex !important;
            }
            
            /* Only show highlight button on desktop */
            .highlight-button-container {
                display: flex !important;
            }
            
            /* Adjust nav links on desktop */
            .nav-links {
                width: auto; /* Auto width on desktop */
                order: 3; /* Position after logo */
                margin-left: 0; /* Reset margin */
                display: flex !important; /* Always show on desktop */
            }
            
            /* Adjust logo on desktop */
            .logo {
                flex-grow: 0; /* Don't grow on desktop */
            }
        }
        
        @media (max-width: 767px) {
            /* On mobile */
            .mobile-only {
                display: flex !important; /* Show mobile elements */
            }
            
            /* Hide the buttons in nav-links on mobile */
            .nav-links .highlight-button-container,
            .nav-links .theme-toggle-container {
                display: none !important;
            }
        }
        
        /* Style for active channel in dropdown */
        #channels-dropdown a.active {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        
        /* Adjust the video container with reduced height and transparent background */
        html body .container.video-container,
        body .container.video-container,
        .container.video-container,
        div.container.video-container,
        .video-container {
            padding-top: 0px !important;
            position: relative !important;
            height: auto !important; /* Changed from fixed height to auto */
            max-height: none !important; /* Remove height restriction */
            min-height: auto !important;
            background-color: transparent !important;
            overflow-y: visible !important; /* Changed from hidden to visible */
            overflow-x: hidden !important; /* Hide horizontal scrolling */
        }
        
        /* Ensure the video tabs content is displayed properly */
        .video-tabs-content {
            display: block !important;
            position: relative !important;
            min-height: auto !important; /* Changed from fixed height to auto */
            overflow: visible !important; /* Allow content to flow naturally */
            height: auto !important; /* Ensure height is auto */
        }
        
        /* Hide the Rotate API Key button */
        .video-container > button:first-child,
        button[onclick*="rotateApiKey"],
        button:contains("Rotate API Key"),
        button:contains("API Key") {
            display: none !important;
        }
        
        /* Hide the YouTube channel container by default */
        .youtube-channel-container, 
        [id^="youtube-channel-container-"] {
            display: none !important;
            overflow: visible !important; /* Allow content to flow naturally */
        }
        
        /* Fix for player layout */
        .youtube-player-layout {
            display: flex !important;
            flex-wrap: nowrap !important;
            gap: 15px !important;
            align-items: flex-start !important;
            justify-content: space-between !important;
            width: 100% !important;
            max-height: none !important; /* Remove height restriction */
            overflow: visible !important; /* Changed from hidden to visible */
            position: relative !important;
            z-index: 10 !important;
            margin-top: 10px !important;
            margin-bottom: 10px !important;
        }
        
        /* Fix for player container */
        .youtube-player-container {
            flex: 4 !important;
            max-width: 75% !important;
            min-width: 75% !important;
            height: auto !important;
            position: relative !important;
            z-index: 5 !important;
            background-color: transparent !important;
            padding-top: 39% !important; /* Reduced to fix Play button visibility */
        }
        
        /* Fix for list column */
        .youtube-list-column {
            flex: 1 !important;
            max-width: 25% !important;
            min-width: 300px !important;
            max-height: none !important;
            overflow-y: visible !important; /* Changed from hidden to visible */
            overflow-x: hidden !important;
            position: relative !important;
            z-index: 5 !important;
            background-color: transparent !important;
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        
        /* Fix for video list container */
        .youtube-video-list-container {
            width: 100% !important;
            max-height: 100% !important;
            overflow: visible !important;
            overflow-y: visible !important; /* Changed from hidden to visible */
            padding-right: 0 !important;
            height: auto !important; /* Changed from fixed height to auto */
            min-height: 500px !important; /* Ensure minimum height */
        }
        
        /* Fix for video list */
        .youtube-video-list {
            width: 100% !important;
            display: flex !important;
            flex-direction: column !important;
            gap: 5px !important;
            padding-right: 0 !important;
            min-height: 500px !important; /* Ensure minimum height */
        }
        
        /* Fix for video items */
        .youtube-video-item {
            display: flex !important;
            padding: 6px !important;
            gap: 8px !important;
            border-bottom: 1px solid rgba(0,0,0,0.1) !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            border-radius: 4px !important;
            margin-bottom: 2px !important;
        }
        
        .youtube-video-item:hover {
            background-color: rgba(0,0,0,0.05) !important;
            transform: translateY(-1px) !important;
        }
        
        /* Fix for video thumbnails */
        .youtube-video-thumbnail {
            width: 120px !important;
            height: 68px !important;
            flex-shrink: 0 !important;
            border-radius: 4px !important;
            overflow: hidden !important;
        }
        
        .youtube-video-thumbnail img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
        }
        
        /* Fix for video title */
        .youtube-video-title {
            font-size: 13px !important;
            font-weight: 500 !important;
            margin: 0 0 4px 0 !important;
            line-height: 1.3 !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
        }
        
        /* Fix for video date */
        .youtube-video-date {
            font-size: 11px !important;
            color: #606060 !important;
            margin: 0 !important;
            font-weight: 400 !important;
        }
        
        /* Fix for active tab */
        .tab-content.active {
            display: block !important;
            width: 100% !important;
            position: relative !important;
            overflow: visible !important;
        }
        
        /* Responsive styles */
        @media (max-width: 1200px) {
            .youtube-player-container {
                max-width: 70% !important;
            }
            
            .youtube-list-column {
                max-width: 30% !important;
            }
            
            .youtube-video-thumbnail {
                width: 110px !important;
                height: 62px !important;
            }
            
            .youtube-video-title {
                font-size: 12px !important;
            }
        }
        
        /* Fix for mobile menu toggle */
        @media (max-width: 767px) {
            /* Ensure mobile menu is displayed when toggled - using styles from styles.css */
            .nav-links.show {
                display: flex !important; /* Show when toggled with !important to override any other styles */
                flex-direction: column !important; /* Ensure items stack vertically */
                align-items: flex-start !important; /* Align items to the left */
                width: 100% !important;
                padding: 1rem 0 !important;
                position: absolute !important;
                top: 60px !important; /* Fixed position from top of navbar */
                left: 0 !important;
                right: 0 !important;
                background-color: var(--secondary-color) !important;
                z-index: 9999 !important; /* Very high z-index to ensure it's above ALL other elements */
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
                border-top: 1px solid var(--border-color) !important;
                animation: fadeIn 0.3s ease-in-out !important;
            }
            
            /* Dark mode styles for mobile menu - using styles from styles.css */
            .dark-mode .nav-links.show {
                background-color: var(--secondary-color) !important; /* Use secondary color for consistency */
                border-top: 1px solid var(--border-color) !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }
            
            /* Hide theme toggle and highlight buttons in mobile menu */
            .nav-links.show .theme-toggle-container,
            .nav-links.show .highlight-toggle-container {
                display: none !important;
            }
            
            /* Ensure all list items are visible and properly aligned - using styles from styles.css */
            .nav-links.show li {
                margin: 0.3rem 0 !important;
                width: 100% !important;
                text-align: left !important;
                padding: 0 0.5rem !important;
                display: block !important;
            }
            
            /* Make links more tappable and properly aligned - using styles from styles.css */
            .nav-links.show a {
                display: block !important;
                padding: 10px 15px !important;
                font-size: 16px !important;
                text-align: left !important; /* Ensure text is left-aligned */
                width: 100% !important;
            }
            
            /* Reset default dropdown behavior from styles.css */
            .dropdown-content {
                display: none !important; /* Always hidden by default */
            }
            
            /* Ensure dropdown is visible when active */
            .dropdown.active .dropdown-content {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                z-index: 9999 !important;
                position: relative !important;
            }
            
            .dropdown:hover .dropdown-content {
                display: none !important; /* Disable hover behavior completely */
            }
            
            /* Fix for dropdown content visibility */
            .dropdown-content {
                overflow: visible !important;
                height: auto !important;
                max-height: none !important;
                transition: none !important;
                animation: none !important;
                transform: none !important;
                pointer-events: auto !important;
                will-change: auto !important;
                backface-visibility: visible !important;
                -webkit-backface-visibility: visible !important;
            }
            
            /* Prevent any hover effects that might cause flickering */
            .dropdown-content a:hover {
                transition: none !important;
                transform: none !important;
                animation: none !important;
            }
            
            /* Style dropdowns in mobile menu - using styles from styles.css */
            .nav-links.show .dropdown-content {
                position: static !important;
                display: none !important;
                width: 100% !important; /* Full width on mobile */
                box-shadow: none !important;
                margin: 0.5rem 0 0.5rem 0 !important; /* Remove margin */
                padding-left: 20px !important; /* Add padding for indentation */
                text-align: left !important;
                left: 0 !important;
                right: 0 !important;
                transform: none !important;
                animation: none !important; /* Remove animation to prevent issues */
                border-left: 2px solid var(--primary-color) !important; /* Add a left border for visual indication */
                background-color: transparent !important; /* Transparent background */
            }
            
            /* Show dropdown when active class is added - using styles from styles.css */
            .nav-links.show .dropdown.active .dropdown-content {
                display: block !important;
            }
            
            /* Remove animation to prevent flickering */
            
            /* Ensure dropdown links are visible and properly aligned - using styles from styles.css */
            .nav-links.show .dropdown.active .dropdown-content a {
                text-align: left !important;
                padding: 10px 5px 10px 10px !important;
                border-bottom: 1px solid rgba(0,0,0,0.05) !important;
                display: block !important;
                width: 100% !important; /* Full width on mobile */
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                color: var(--text-color) !important;
            }
            
            /* Style for last dropdown item */
            .nav-links.show .dropdown.active .dropdown-content a:last-child {
                border-bottom: none !important;
            }
            
            /* Dark mode styles for dropdown */
            .dark-mode .nav-links.show .dropdown.active .dropdown-content {
                background-color: #1e1e1e !important; /* Solid dark background */
                border-left: 2px solid var(--primary-color) !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
                z-index: 9999 !important; /* Very high z-index */
            }
            
            .dark-mode .nav-links.show .dropdown.active .dropdown-content a {
                border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
            }
            
            /* Disable hover behavior on mobile */
            .nav-links.show .dropdown:hover .dropdown-content {
                display: none !important;
            }
            
            /* Mobile dropdown button styling */
            .nav-links.show .dropbtn {
                display: flex;
                justify-content: flex-start; /* Changed from space-between to flex-start */
                width: 100%;
                padding: 10px 15px !important; /* Match the padding of other links */
                position: relative;
                cursor: pointer;
                background-color: transparent !important; /* Changed from rgba(0, 0, 0, 0.05) to transparent */
                border-radius: 0;
                transition: all 0.3s ease;
                border: none !important; /* Removed border */
                color: var(--text-color);
                font-weight: 500;
                text-align: left !important;
            }
            
            .dropbtn:after {
                content: '\f107'; /* Font Awesome down arrow */
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
                margin-left: 0.5rem;
                transition: transform 0.3s ease;
                color: var(--primary-color);
            }
            
            /* Position the dropdown arrow for mobile view */
            .nav-links.show .dropbtn:after {
                position: absolute;
                right: 15px; /* Position the arrow on the right */
                margin-left: 0;
            }
            
            .dropdown.active .dropbtn {
                background-color: rgba(0, 0, 0, 0.1);
                border-color: var(--primary-color);
            }
            
            .dropdown.active .dropbtn:after {
                content: '\f106'; /* Font Awesome up arrow */
                color: var(--primary-color);
                transform: rotate(0deg);
            }
            
            .dropbtn:active {
                background-color: rgba(0, 0, 0, 0.15);
                transform: translateY(1px);
            }
            
            /* Add visual cue that this is clickable */
            .dropbtn:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle, rgba(0,0,0,0.03) 0%, rgba(0,0,0,0) 70%);
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }
            
            .dropbtn:hover:before {
                opacity: 1;
            }
            
            /* Dark mode styles */
            .dark-mode .dropbtn {
                background-color: rgba(255, 255, 255, 0.05);
                border-color: rgba(255, 255, 255, 0.1);
            }
            
            .dark-mode .dropbtn:after {
                color: var(--primary-color);
            }
            
            .dark-mode .dropdown.active .dropbtn {
                background-color: rgba(255, 255, 255, 0.1);
                border-color: var(--primary-color);
            }
            
            .dark-mode .dropbtn:active {
                background-color: rgba(255, 255, 255, 0.15);
            }
        }
        
        /* Desktop styles */
        @media (min-width: 768px) {
            /* Enable hover behavior for desktop */
            .dropdown:hover .dropdown-content {
                display: block !important;
                position: absolute !important;
                top: 100% !important;
                left: 0 !important;
                width: 220px !important;
                background-color: #f9f9f9 !important;
                box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2) !important;
                z-index: 9999 !important;
                border-radius: 4px !important;
                border: 1px solid rgba(0,0,0,0.1) !important;
                overflow: visible !important;
            }
            
            /* Show dropdown when active class is present (for click behavior) */
            .dropdown.active .dropdown-content {
                display: block !important;
                position: absolute !important;
                top: 100% !important;
                left: 0 !important;
                width: 220px !important;
                background-color: #f9f9f9 !important;
                box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2) !important;
                z-index: 9999 !important;
                border-radius: 4px !important;
                border: 1px solid rgba(0,0,0,0.1) !important;
                overflow: visible !important;
            }
            
            /* Dark mode dropdown on desktop */
            .dark-mode .dropdown.active .dropdown-content,
            .dark-mode .dropdown:hover .dropdown-content {
                background-color: #1e1e1e !important;
                border: 1px solid rgba(255,255,255,0.1) !important;
                box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.5) !important;
            }
        }
        
        /* Extra large screens */
        @media (min-width: 1400px) {
            .video-container {
                max-width: 98vw !important;
                padding: 0.25rem !important;
            }
            
            .youtube-player-layout {
                gap: 10px !important;
            }
            
            .youtube-player-container {
                max-width: 78% !important;
                min-width: 78% !important;
                padding-top: 41% !important; /* Reduced from 43.875% to fix Play button visibility */
            }
            
            .youtube-list-column {
                max-width: 22% !important;
                min-width: 22% !important;
            }
            
            .youtube-video-thumbnail {
                width: 130px !important;
                height: 73px !important;
            }
        }
        
        @media (max-width: 900px) {
            .youtube-player-layout {
                flex-direction: column !important;
                gap: 15px !important;
            }
            
            .youtube-player-container,
            .youtube-list-column {
                max-width: 100% !important;
                width: 100% !important;
                flex: 1 1 auto !important;
            }
            
            .youtube-player-container {
                padding-top: 52% !important; /* Reduced from 56.25% to fix Play button visibility */
            }
            
            .youtube-list-column {
                max-height: none !important; /* Remove height restriction */
                border-top: 1px solid rgba(0,0,0,0.1) !important;
                padding-top: 15px !important;
                margin-top: 15px !important;
                overflow-y: visible !important; /* Changed from auto to visible */
                height: auto !important; /* Allow natural height on mobile */
                min-height: 400px !important;
            }
            
            .youtube-video-list-container {
                min-height: 400px !important;
            }
            
            .youtube-video-list {
                min-height: 400px !important;
                display: grid !important;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
                gap: 10px !important;
            }
            
            .youtube-video-item {
                border: 1px solid rgba(0,0,0,0.1) !important;
                border-radius: 8px !important;
                padding: 8px !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
            }
            
            .youtube-video-thumbnail {
                width: 130px !important;
                height: 73px !important;
            }
            
            .youtube-video-title {
                font-size: 14px !important;
            }
            
            .youtube-video-date {
                font-size: 12px !important;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="index.html">Opiskelen Suomea</a>
            </div>
            <!-- Mobile-only buttons that appear outside the nav menu -->
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="video-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content" id="categories-dropdown">
                        <a href="index.html#daily-life">Daily Life</a>
                        <a href="index.html#web-development">Web Development</a>
                        <a href="index.html#cleaner">Cleaner</a>
                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="video-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="video-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container video-container">
        <!-- Hidden tab buttons (used by JavaScript) -->
        <div class="video-tab-buttons"></div>
        
        <!-- Video tabs content -->
        <div class="video-tabs-content"></div>
    </div>

    <!-- Core scripts -->
    <script src="script.js"></script>
    
    <!-- YouTube scripts -->
    <script src="youtube-channels.js"></script>
    <script src="youtube-core.js"></script>
    
    <!-- Force layout recalculation after page load -->
    <script>
        window.addEventListener('load', function() {
            // Force layout recalculation after page load
            setTimeout(function() {
                // Check URL for channel parameter
                const urlParams = new URLSearchParams(window.location.search);
                const channelParam = urlParams.get('channel');
                
                // Determine which channel to display
                let displayChannelKey;
                if (channelParam && window.YOUTUBE_CHANNELS && window.YOUTUBE_CHANNELS[channelParam]) {
                    displayChannelKey = channelParam;
                    console.log(`Using channel from URL parameter: ${displayChannelKey}`);
                } else if (window.YOUTUBE_CHANNELS) {
                    displayChannelKey = Object.keys(window.YOUTUBE_CHANNELS)[0];
                    console.log(`Using first channel: ${displayChannelKey}`);
                } else {
                    console.log("YouTube channels not available yet");
                    return;
                }
                
                // Find the player layout
                const playerLayout = document.getElementById(`youtube-player-layout-${displayChannelKey}`);
                if (playerLayout) {
                    // Force display flex
                    playerLayout.style.display = 'flex';
                    
                    // Find the tab content
                    const tabContent = document.getElementById(window.YOUTUBE_CHANNELS[displayChannelKey].tabId);
                    if (tabContent) {
                        // Hide all tab content first
                        document.querySelectorAll(".tab-content").forEach((tab) => {
                            tab.classList.remove("active");
                            tab.style.display = "none";
                        });
                        
                        // Force display block for the selected tab
                        tabContent.style.display = 'block';
                        tabContent.classList.add('active');
                        
                        // Make sure the correct tab button is active
                        document.querySelectorAll(".video-tab-button").forEach((button) => {
                            if (button.dataset.channelKey === displayChannelKey) {
                                button.classList.add("active");
                            } else {
                                button.classList.remove("active");
                            }
                        });
                    }
                }
            }, 1000);
        });
    </script>
    
    <!-- Script for channel dropdown navigation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check URL for channel parameter and open the corresponding tab
            function handleUrlParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                const channelParam = urlParams.get('channel');
                
                // Remove any hash fragment that might be causing confusion
                if (window.location.hash) {
                    console.log(`Removing hash fragment: ${window.location.hash}`);
                    const newUrl = new URL(window.location.href);
                    newUrl.hash = '';
                    window.history.replaceState(null, '', newUrl.toString());
                }
                
                if (channelParam) {
                    console.log(`URL has channel parameter: ${channelParam}`);
                    
                    // Make sure the YouTube Core is available
                    if (!window.YouTubeCore) {
                        console.log("YouTube Core not available yet, waiting...");
                        setTimeout(handleUrlParameters, 500);
                        return;
                    }
                    
                    // Make sure this is the active channel
                    window.YouTubeCore.currentChannelKey = channelParam;
                    
                    // Find the corresponding tab button
                    const tabButton = document.querySelector(`.video-tab-button[data-channel-key="${channelParam}"]`);
                    if (tabButton) {
                        console.log(`Found tab button for ${channelParam}, activating it`);
                        
                        // Make sure all other buttons are not active
                        document.querySelectorAll(".video-tab-button").forEach((button) => {
                            button.classList.remove("active");
                        });
                        
                        // Make this button active
                        tabButton.classList.add("active");
                        
                        // Find the tab content
                        const tabId = window.YOUTUBE_CHANNELS[channelParam].tabId;
                        if (tabId) {
                            // Hide all tab content
                            document.querySelectorAll(".tab-content").forEach((tab) => {
                                tab.classList.remove("active");
                                tab.style.display = "none";
                            });
                            
                            // Show the selected tab
                            const tabContent = document.getElementById(tabId);
                            if (tabContent) {
                                tabContent.classList.add("active");
                                tabContent.style.display = "block";
                                tabContent.style.width = "100%";
                                tabContent.style.position = "relative";
                                tabContent.style.overflow = "visible";
                            }
                            
                            // Create player layout if needed
                            if (!document.getElementById(`youtube-player-layout-${channelParam}`)) {
                                window.YouTubeCore.createPlayerLayout(channelParam);
                            }
                            
                            // Fetch videos if not already loaded
                            if (!tabButton.dataset.loaded) {
                                window.YouTubeCore.fetchChannelVideos(channelParam);
                                tabButton.dataset.loaded = "true";
                            }
                        }
                        
                        // Also update the dropdown navigation
                        const navLinks = document.querySelectorAll('#channels-dropdown a[data-channel-key]');
                        navLinks.forEach(link => {
                            if (link.getAttribute('data-channel-key') === channelParam) {
                                link.classList.add('active');
                            } else {
                                link.classList.remove('active');
                            }
                        });
                        
                        // If this is one of the specified channels, ensure pagination is visible
                        const isSpecifiedChannel = [
                            "finnishcrashcourse", 
                            "finnishtogo", 
                            "suomenkurssiyt"
                        ].includes(channelParam);
                        
                        if (isSpecifiedChannel && window.YouTubeCore) {
                            console.log(`Ensuring pagination for specified channel: ${channelParam}`);
                            setTimeout(() => {
                                window.YouTubeCore.ensurePaginationForSpecifiedChannels();
                            }, 1000); // Delay to ensure everything is loaded
                        }
                    } else {
                        console.error(`Tab button for ${channelParam} not found, retrying later...`);
                        setTimeout(handleUrlParameters, 1000);
                    }
                }
            }
            
            // Handle browser back/forward navigation
            window.addEventListener('popstate', function(event) {
                if (event.state && event.state.channelKey) {
                    console.log(`Navigation state changed to: ${event.state.channelKey}`);
                    const channelKey = event.state.channelKey;
                    
                    // Make sure this is the active channel
                    if (window.YouTubeCore) {
                        window.YouTubeCore.currentChannelKey = channelKey;
                    }
                    
                    // Find the corresponding tab button and click it
                    const tabButton = document.querySelector(`.video-tab-button[data-channel-key="${channelKey}"]`);
                    if (tabButton) {
                        console.log(`Found tab button for ${channelKey}, clicking it`);
                        
                        // Click the button to open the tab
                        tabButton.click();
                        
                        // Update active state in the dropdown
                        const navLinks = document.querySelectorAll('#channels-dropdown a[data-channel-key]');
                        navLinks.forEach(link => {
                            if (link.getAttribute('data-channel-key') === channelKey) {
                                link.classList.add('active');
                            } else {
                                link.classList.remove('active');
                            }
                        });
                        
                        // If this is one of the specified channels, ensure pagination is visible
                        const isSpecifiedChannel = [
                            "finnishcrashcourse", 
                            "finnishtogo", 
                            "suomenkurssiyt"
                        ].includes(channelKey);
                        
                        if (isSpecifiedChannel && window.YouTubeCore) {
                            console.log(`Ensuring pagination for specified channel: ${channelKey}`);
                            setTimeout(() => {
                                window.YouTubeCore.ensurePaginationForSpecifiedChannels();
                            }, 1000); // Delay to ensure everything is loaded
                        }
                    } else {
                        console.error(`Tab button for ${channelKey} not found`);
                    }
                } else {
                    // If no state, check URL parameters
                    handleUrlParameters();
                }
            });
            
            // Call the URL parameter handler after a longer delay to ensure everything is loaded
            setTimeout(handleUrlParameters, 2000);
            
            // Also call it when the YouTube API is ready
            window.addEventListener('YouTubeAPIReady', function() {
                console.log("YouTube API is ready, handling URL parameters");
                setTimeout(handleUrlParameters, 500);
            });
            
            // And when tabs are created
            document.addEventListener('YouTubeTabsCreated', function() {
                console.log("YouTube tabs created, handling URL parameters");
                setTimeout(handleUrlParameters, 500);
            });
            
            // And when a player is ready
            document.addEventListener('YouTubePlayerReady', function(event) {
                console.log(`YouTube player ready for channel: ${event.detail.channelKey}, checking URL parameters`);
                setTimeout(handleUrlParameters, 100);
            });
            
            // And when the initial channel is loaded
            document.addEventListener('YouTubeInitialChannelLoaded', function(event) {
                console.log(`Initial channel loaded: ${event.detail.channelKey}, checking URL parameters`);
                setTimeout(handleUrlParameters, 100);
            });
            
            // Set up channel dropdown navigation
            function setupChannelNavigation() {
                console.log("Setting up channel navigation...");
                const channelsDropdown = document.getElementById('channels-dropdown');
                if (channelsDropdown) {
                    const channelLinks = channelsDropdown.querySelectorAll('a[data-channel-key]');
                    console.log(`Found ${channelLinks.length} channel links`);
                    
                    channelLinks.forEach(link => {
                        // Remove any existing event listeners
                        const newLink = link.cloneNode(true);
                        link.parentNode.replaceChild(newLink, link);
                        
                        newLink.addEventListener('click', function(e) {
                            e.preventDefault();
                            const channelKey = this.getAttribute('data-channel-key');
                            console.log(`Channel link clicked: ${channelKey}`);
                            
                            // Remove any hash fragment that might be causing confusion
                            if (window.location.hash) {
                                console.log(`Removing hash fragment: ${window.location.hash}`);
                                const newUrl = new URL(window.location.href);
                                newUrl.hash = '';
                                window.history.replaceState(null, '', newUrl.toString());
                            }
                            
                            // Make sure this is the active channel
                            if (window.YouTubeCore) {
                                window.YouTubeCore.currentChannelKey = channelKey;
                            }
                            
                            // Find the corresponding tab button and click it
                            const tabButton = document.querySelector(`.video-tab-button[data-channel-key="${channelKey}"]`);
                            if (tabButton) {
                                console.log(`Found tab button for ${channelKey}, clicking it`);
                                
                                // Update URL with the channel key without reloading the page
                                const newUrl = new URL(window.location.href);
                                newUrl.searchParams.set('channel', channelKey);
                                newUrl.hash = '';
                                window.history.pushState({ channelKey: channelKey }, '', newUrl.toString());
                                
                                // Click the button to open the tab
                                tabButton.click();
                                
                                // Update active state in the dropdown
                                channelLinks.forEach(cl => cl.classList.remove('active'));
                                this.classList.add('active');
                                
                                // If this is one of the specified channels, ensure pagination is visible
                                const isSpecifiedChannel = [
                                    "finnishcrashcourse", 
                                    "finnishtogo", 
                                    "suomenkurssiyt"
                                ].includes(channelKey);
                                
                                if (isSpecifiedChannel && window.YouTubeCore) {
                                    console.log(`Ensuring pagination for specified channel: ${channelKey}`);
                                    setTimeout(() => {
                                        window.YouTubeCore.ensurePaginationForSpecifiedChannels();
                                    }, 1000); // Delay to ensure everything is loaded
                                }
                            } else {
                                console.error(`Tab button for ${channelKey} not found`);
                            }
                            
                            // Close the mobile menu if it's open
                            const navLinks = document.getElementById('nav-links');
                            if (navLinks && navLinks.classList.contains('show')) {
                                navLinks.classList.remove('show');
                            }
                        });
                    });
                } else {
                    console.error("Channels dropdown not found");
                }
            }
            
            // Call the setup function
            setupChannelNavigation();
            
            // Also set up the navigation when the YouTube API is loaded
            window.addEventListener('YouTubeAPIReady', setupChannelNavigation);
            
            // Ensure navigation works even if tabs are created dynamically
            document.addEventListener('YouTubeTabsCreated', setupChannelNavigation);
            
            // Set up dark mode toggle
            const darkModeToggle = document.getElementById('video-toggle-dark');
            const darkModeToggleMobile = document.getElementById('video-toggle-dark-mobile');
            
            function toggleDarkMode() {
                document.body.classList.toggle('dark-mode');
                
                // Update icon
                const isDarkMode = document.body.classList.contains('dark-mode');
                const updateIcon = (button) => {
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            icon.classList.remove(isDarkMode ? 'fa-moon' : 'fa-sun');
                            icon.classList.add(isDarkMode ? 'fa-sun' : 'fa-moon');
                        }
                    }
                };
                
                // Update both buttons
                updateIcon(darkModeToggle);
                updateIcon(darkModeToggleMobile);
                
                // Save preference
                localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
                
                console.log('Dark mode toggled:', isDarkMode ? 'enabled' : 'disabled');
            }
            
            // Function to handle theme toggle visibility based on screen size
            function handleThemeToggleVisibility() {
                const mobileToggleContainer = document.querySelector('.mobile-only-theme-toggle');
                if (mobileToggleContainer) {
                    if (window.innerWidth > 767) {
                        // On desktop/laptop, hide the mobile toggle
                        mobileToggleContainer.style.display = 'none';
                    } else {
                        // On mobile, show the mobile toggle
                        mobileToggleContainer.style.display = 'flex';
                    }
                }
            }
            
            // Run on page load and whenever window is resized
            window.addEventListener('load', handleThemeToggleVisibility);
            window.addEventListener('resize', handleThemeToggleVisibility);
            
            // Add event listeners to both buttons
            if (darkModeToggle) {
                darkModeToggle.addEventListener('click', toggleDarkMode);
                console.log('Dark mode toggle desktop button initialized');
            }
            
            if (darkModeToggleMobile) {
                darkModeToggleMobile.addEventListener('click', toggleDarkMode);
                console.log('Dark mode toggle mobile button initialized');
            }
            
            // Check for dark mode preference on page load
            document.addEventListener('DOMContentLoaded', function() {
                const darkModePreference = localStorage.getItem('darkMode');
                if (darkModePreference === 'enabled') {
                    document.body.classList.add('dark-mode');
                    
                    // Update icons for both buttons
                    const updateIcon = (buttonId) => {
                        const button = document.getElementById(buttonId);
                        if (button) {
                            const icon = button.querySelector('i');
                            if (icon) {
                                icon.classList.remove('fa-moon');
                                icon.classList.add('fa-sun');
                            }
                        }
                    };
                    
                    updateIcon('video-toggle-dark');
                    updateIcon('video-toggle-dark-mobile');
                    
                    console.log('Dark mode applied from localStorage');
                }
            });
            
            // Set up highlight mode toggle
            const highlightToggle = document.getElementById('video-toggle-highlight');
            const highlightToggleMobile = document.getElementById('video-toggle-highlight-mobile');
            
            function toggleHighlightMode() {
                // Toggle highlight mode
                document.body.classList.toggle('highlight-mode');
                
                // Toggle active class on both buttons
                const isHighlightMode = document.body.classList.contains('highlight-mode');
                const updateButtonState = (button) => {
                    if (button) {
                        if (isHighlightMode) {
                            button.classList.add(button.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                        } else {
                            button.classList.remove(button.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                        }
                    }
                };
                
                updateButtonState(highlightToggle);
                updateButtonState(highlightToggleMobile);
                
                // Set cursor to indicate highlight mode
                if (isHighlightMode) {
                    document.body.style.cursor = 'text';
                    
                    // Add event listener for text selection
                    document.addEventListener('mouseup', handleTextSelection);
                } else {
                    document.body.style.cursor = '';
                    
                    // Remove event listener for text selection
                    document.removeEventListener('mouseup', handleTextSelection);
                    
                    // Remove all highlights
                    removeAllHighlights();
                }
                
                localStorage.setItem('highlightMode', isHighlightMode ? 'enabled' : 'disabled');
            }
            
            // Add event listeners to both buttons
            if (highlightToggle) {
                highlightToggle.addEventListener('click', toggleHighlightMode);
                console.log('Highlight toggle desktop button initialized');
            }
            
            if (highlightToggleMobile) {
                highlightToggleMobile.addEventListener('click', toggleHighlightMode);
                console.log('Highlight toggle mobile button initialized');
            }
            
            // Function to handle text selection
            function handleTextSelection() {
                if (!document.body.classList.contains('highlight-mode')) return;
                
                const selection = window.getSelection();
                if (selection.toString().length > 0) {
                    // Create a span element to wrap the selected text
                    const range = selection.getRangeAt(0);
                    const selectedText = range.extractContents();
                    const span = document.createElement('span');
                    span.className = 'user-highlight';
                    span.appendChild(selectedText);
                    range.insertNode(span);
                    
                    // Clear the selection
                    selection.removeAllRanges();
                }
            }
            
            // Function to remove all highlights
            function removeAllHighlights() {
                const highlights = document.querySelectorAll('.user-highlight');
                highlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    while (highlight.firstChild) {
                        parent.insertBefore(highlight.firstChild, highlight);
                    }
                    parent.removeChild(highlight);
                });
            }
            
            // Check if dark mode was previously enabled
            if (localStorage.getItem('darkMode') === 'enabled') {
                document.body.classList.add('dark-mode');
                // Update icon to sun for both desktop and mobile toggles
                const updateIcon = (buttonId) => {
                    const button = document.getElementById(buttonId);
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            icon.classList.remove('fa-moon');
                            icon.classList.add('fa-sun');
                        }
                    }
                };
                
                updateIcon('video-toggle-dark');
                updateIcon('video-toggle-dark-mobile');
            }
            
            // Check if highlight mode was previously enabled
            document.addEventListener('DOMContentLoaded', function() {
                if (localStorage.getItem('highlightMode') === 'enabled') {
                    document.body.classList.add('highlight-mode');
                    // Set cursor to indicate highlight mode
                    document.body.style.cursor = 'text';
                    
                    // Add active class to both highlight buttons based on their type
                    const updateButtonState = (buttonId) => {
                        const button = document.getElementById(buttonId);
                        if (button) {
                            button.classList.add(button.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                        }
                    };
                    
                    updateButtonState('video-toggle-highlight');
                    updateButtonState('video-toggle-highlight-mobile');
                    
                    // Add event listener for text selection
                    document.addEventListener('mouseup', handleTextSelection);
                    
                    console.log('Highlight mode applied from localStorage');
                }
            });
            
            // Set up mobile menu toggle
            const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            const navLinksContainer = document.getElementById("nav-links");
            
            if (mobileMenuToggle && navLinksContainer) {
                // Function to toggle menu
                function toggleMobileMenu(e) {
                    // Prevent default behavior
                    if (e) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    
                    navLinksContainer.classList.toggle("show");
                    
                    // Change icon based on menu state
                    const icon = mobileMenuToggle.querySelector("i");
                    if (icon) {
                        if (navLinksContainer.classList.contains("show")) {
                            icon.classList.remove("fa-bars");
                            icon.classList.add("fa-times");
                        } else {
                            icon.classList.remove("fa-times");
                            icon.classList.add("fa-bars");
                        }
                    }
                }
                
                // Add click event to toggle button
                mobileMenuToggle.addEventListener("click", toggleMobileMenu);
                
                // Close menu when clicking outside
                document.addEventListener("click", function(e) {
                    if (
                        navLinksContainer.classList.contains("show") &&
                        !navLinksContainer.contains(e.target) &&
                        e.target !== mobileMenuToggle &&
                        !mobileMenuToggle.contains(e.target)
                    ) {
                        toggleMobileMenu();
                    }
                });
            }
            
            // Function to ensure the first YouTube tab is shown
            function ensureFirstYouTubeTabShown() {
                // Check if YOUTUBE_CHANNELS is defined
                if (typeof window.YOUTUBE_CHANNELS === 'undefined' || Object.keys(window.YOUTUBE_CHANNELS).length === 0) {
                    return;
                }
                
                // Check if there's a channel parameter in the URL
                const urlParams = new URLSearchParams(window.location.search);
                const channelParam = urlParams.get('channel');
                
                // If there's a valid channel parameter, don't override it
                if (channelParam && window.YOUTUBE_CHANNELS[channelParam]) {
                    console.log(`URL has channel parameter: ${channelParam}, respecting it and not showing first channel`);
                    return;
                }
                
                // Get the first YouTube channel
                const firstChannelKey = Object.keys(window.YOUTUBE_CHANNELS)[0];
                const firstChannel = window.YOUTUBE_CHANNELS[firstChannelKey];
                
                if (firstChannel && firstChannel.tabId) {
                    // Find the first YouTube tab
                    const firstYouTubeTab = document.getElementById(firstChannel.tabId);
                    if (firstYouTubeTab) {
                        // Make sure it's active
                        firstYouTubeTab.classList.add('active');
                        firstYouTubeTab.style.display = 'block';
                        
                        // Find the first YouTube button
                        const firstYouTubeButton = document.querySelector(`.video-tab-button[data-channel-key="${firstChannelKey}"]`);
                        if (firstYouTubeButton) {
                            // Make sure it's active
                            firstYouTubeButton.classList.add('active');
                            
                            // Simulate a click on the button to load videos if they're not loaded
                            const videosContainer = document.getElementById(`youtube-videos-container-${firstChannelKey}`);
                            if (videosContainer && videosContainer.children.length === 0) {
                                firstYouTubeButton.click();
                            }
                        }
                    }
                }
            }
            
            // Run immediately
            ensureFirstYouTubeTabShown();
            
            // Also run after a delay to catch any late DOM changes
            setTimeout(ensureFirstYouTubeTabShown, 500);
            setTimeout(ensureFirstYouTubeTabShown, 1000);
        });
            
        // Migrate highlight settings from other localStorage variables
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we need to migrate from other highlight variables
            if (!localStorage.getItem('highlightMode')) {
                // Check if we have settings in other variables
                if (localStorage.getItem('highlightMode') === 'enabled') { localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') { localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
        });        // Check for highlight settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Migrate old settings if needed
            if (!localStorage.getItem('highlightMode')) {
                const oldHighlightState = localStorage.getItem('highlight');
                if (oldHighlightState === 'enabled') {
                    localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') {
                    localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
            
            // Apply highlight state on page load
            const isHighlighted = localStorage.getItem('highlightMode') === 'enabled';
            if (isHighlighted) {
                const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                document.body.classList.add(document.body.classList.contains('highlight-mode') ? 'highlight-mode' : 'highlight-enabled');
                
                // Add background color to Finnish words if they exist
                const finnishWords = document.querySelectorAll('.finnish');
                if (finnishWords.length > 0) {
                    finnishWords.forEach(function(element) {
                        element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                    });
                }
            }
        });        // Function to toggle text highlighting
        function toggleHighlight() {
            const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
            
            if (document.body.classList.contains('highlight-mode') || document.body.classList.contains('highlight-enabled')) {
                // Disable highlighting
                document.body.classList.remove('highlight-mode');
                document.body.classList.remove('highlight-enabled');
                localStorage.setItem('highlightMode', 'disabled');
                
                if (highlightButton) {
                    highlightButton.classList.remove('active');
                    highlightButton.classList.remove('active-tool');
                }
                
                // Remove background color from Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = '';
                });
            } else {
                // Enable highlighting
                document.body.classList.add(document.querySelector('.highlight-mode') !== null ? 'highlight-mode' : 'highlight-enabled');
                localStorage.setItem('highlightMode', 'enabled');
                
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                
                // Add background color to Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                });
            }
        }
        
        // Check for highlight settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Migrate old settings if needed
            if (!localStorage.getItem('highlightMode')) {
                const oldHighlightState = localStorage.getItem('highlight');
                if (oldHighlightState === 'enabled') {
                    localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') {
                    localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
            
            // Apply highlight state on page load
            const isHighlighted = localStorage.getItem('highlightMode') === 'enabled';
            if (isHighlighted) {
                const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                
                document.body.classList.add(document.querySelector('.highlight-mode') !== null ? 'highlight-mode' : 'highlight-enabled');
                
                // Add background color to Finnish words if they exist
                const finnishWords = document.querySelectorAll('.finnish');
                if (finnishWords.length > 0) {
                    finnishWords.forEach(function(element) {
                        element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                    });
                }
            }
        });</script>
        <script src="highlight.js"></script>
        
    <!-- Highlight mode notification -->
    <div id="highlight-notification" class="highlight-notification">
        <i class="fas fa-highlighter"></i> Highlight mode enabled. Select text to highlight it.
    </div>
    
    <!-- Fix for mobile menu toggle -->
    <script>
        // Simple direct approach to fix mobile menu toggle
        window.onload = function() {
            console.log("Window loaded - setting up mobile menu");
            
            // Ensure all dropdowns are closed initially
            document.querySelectorAll('.dropdown').forEach(function(dropdown) {
                dropdown.classList.remove('active');
                var dropdownContent = dropdown.querySelector('.dropdown-content');
                if (dropdownContent) {
                    dropdownContent.style.display = 'none';
                }
            });
            
            setupMobileMenu();
        };
        
        function setupMobileMenu() {
            console.log("Setting up mobile menu");
            var mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            var navLinks = document.getElementById('nav-links');
            
            if (mobileMenuToggle && navLinks) {
                console.log("Found mobile menu elements");
                
                // Remove any existing click handlers
                mobileMenuToggle.onclick = null;
                
                // Add new click handler
                mobileMenuToggle.onclick = function(e) {
                    console.log("Mobile menu clicked");
                    e.preventDefault();
                    e.stopPropagation();
                    
                    if (navLinks.classList.contains('show')) {
                        console.log("Hiding mobile menu");
                        navLinks.classList.remove('show');
                        // Change icon to bars
                        var icon = mobileMenuToggle.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-bars';
                        }
                    } else {
                        console.log("Showing mobile menu");
                        navLinks.classList.add('show');
                        // Change icon to times
                        var icon = mobileMenuToggle.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-times';
                        }
                    }
                    
                    return false;
                };
                
                // Setup dropdown click behavior for mobile
                var dropdowns = document.querySelectorAll('.nav-links .dropdown');
                dropdowns.forEach(function(dropdown) {
                    var dropbtn = dropdown.querySelector('.dropbtn');
                    var dropdownContent = dropdown.querySelector('.dropdown-content');
                    
                    if (dropbtn) {
                        console.log("Found dropdown button:", dropbtn.textContent);
                        
                        // Remove any existing click handlers
                        dropbtn.onclick = null;
                        
                        // Add new click handler
                        dropbtn.addEventListener('click', function(e) {
                            console.log("Dropdown button clicked:", this.textContent);
                            
                            // Only handle click behavior on mobile screens
                            if (window.innerWidth <= 767) {
                                e.preventDefault();
                                e.stopPropagation();
                                
                                console.log("Toggle dropdown on mobile:", dropdown.classList.contains('active') ? "closing" : "opening");
                                
                                // Toggle active class on the dropdown
                                if (dropdown.classList.contains('active')) {
                                    console.log("Closing dropdown:", dropdown.querySelector('.dropbtn').textContent);
                                    dropdown.classList.remove('active');
                                
                                // Find and remove any dropdown content
                                var dropdownContents = dropdown.querySelectorAll('.dropdown-content');
                                dropdownContents.forEach(function(content) {
                                    if (content && content.parentNode) {
                                        console.log("Removing dropdown content with ID:", content.id || "no-id");
                                        content.parentNode.removeChild(content);
                                    }
                                });
                                
                                // Recreate the original dropdown content
                                var dropbtn = dropdown.querySelector('.dropbtn');
                                if (dropbtn && dropbtn.textContent === "Categories") {
                                    console.log("Recreating Categories dropdown after close");
                                    var newContent = document.createElement('div');
                                    newContent.className = 'dropdown-content';
                                    newContent.id = 'categories-dropdown';
                                    newContent.innerHTML = `
                                        <a href="index.html#daily-life">Daily Life</a>
                                        <a href="index.html#web-development">Web Development</a>
                                        <a href="index.html#cleaner">Cleaner</a>
                                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                                    `;
                                    dropdown.appendChild(newContent);
                                    newContent.style.display = 'none';
                                } else if (dropbtn && dropbtn.textContent === "Videos") {
                                    console.log("Recreating Videos dropdown after close");
                                    var newContent = document.createElement('div');
                                    newContent.className = 'dropdown-content';
                                    newContent.id = 'channels-dropdown';
                                    newContent.innerHTML = `
                                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                                        <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                        <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                        <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                        <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                        <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                                        <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                        <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                        <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                        <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                                    `;
                                    dropdown.appendChild(newContent);
                                    newContent.style.display = 'none';
                                }
                                
                                console.log("Dropdown closed and recreated");
                            } else {
                                // Close any other open dropdowns
                                dropdowns.forEach(function(otherDropdown) {
                                    if (otherDropdown !== dropdown) {
                                        if (otherDropdown.classList.contains('active')) {
                                            console.log("Closing other dropdown:", otherDropdown.querySelector('.dropbtn').textContent);
                                            otherDropdown.classList.remove('active');
                                            
                                            // Find and remove any dropdown content
                                            var dropdownContents = otherDropdown.querySelectorAll('.dropdown-content');
                                            dropdownContents.forEach(function(content) {
                                                if (content && content.parentNode) {
                                                    console.log("Removing other dropdown content with ID:", content.id || "no-id");
                                                    content.parentNode.removeChild(content);
                                                }
                                            });
                                            
                                            // Recreate the original dropdown content
                                            var dropbtn = otherDropdown.querySelector('.dropbtn');
                                            if (dropbtn && dropbtn.textContent === "Categories") {
                                                console.log("Recreating Categories dropdown");
                                                var newContent = document.createElement('div');
                                                newContent.className = 'dropdown-content';
                                                newContent.id = 'categories-dropdown';
                                                newContent.innerHTML = `
                                                    <a href="index.html#daily-life">Daily Life</a>
                                                    <a href="index.html#web-development">Web Development</a>
                                                    <a href="index.html#cleaner">Cleaner</a>
                                                    <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                                                `;
                                                otherDropdown.appendChild(newContent);
                                                newContent.style.display = 'none';
                                            } else if (dropbtn && dropbtn.textContent === "Videos") {
                                                console.log("Recreating Videos dropdown");
                                                var newContent = document.createElement('div');
                                                newContent.className = 'dropdown-content';
                                                newContent.id = 'channels-dropdown';
                                                newContent.innerHTML = `
                                                    <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                                                    <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                                                    <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                                                    <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                                                    <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                                    <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                                    <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                                    <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                                    <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                                                    <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                    <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                                                    <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                                    <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                                    <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                                    <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                                                `;
                                                otherDropdown.appendChild(newContent);
                                                newContent.style.display = 'none';
                                            }
                                            
                                            console.log("Other dropdown closed and recreated");
                                        }
                                    }
                                });
                                dropdown.classList.add('active');
                                console.log("Dropdown opened");
                                
                                // Force display of dropdown content
                                if (dropdownContent) {
                                    console.log("Processing dropdown content for:", dropdown.querySelector('.dropbtn').textContent);
                                    
                                    // Create a completely new dropdown element to avoid any CSS issues
                                    var originalHTML = dropdownContent.innerHTML;
                                    var originalId = dropdownContent.id || '';
                                    
                                    console.log("Original dropdown ID:", originalId);
                                    
                                    // Remove the original dropdown content
                                    var parentElement = dropdownContent.parentNode;
                                    parentElement.removeChild(dropdownContent);
                                    
                                    // Create a new element
                                    var newDropdown = document.createElement('div');
                                    newDropdown.className = 'dropdown-content';
                                    if (originalId) {
                                        newDropdown.id = originalId;
                                        console.log("Set new dropdown ID:", originalId);
                                    }
                                    newDropdown.innerHTML = originalHTML;
                                    
                                    // Check if dark mode is enabled
                                    var isDarkMode = document.body.classList.contains('dark-mode');
                                    
                                    // Style the new element
                                    var cssText = 'display: block !important; ' +
                                        'position: relative !important; ' +
                                        'z-index: 9999 !important; ' +
                                        'visibility: visible !important; ' +
                                        'opacity: 1 !important; ' +
                                        'pointer-events: auto !important; ' +
                                        'width: 100% !important; ' +
                                        'margin: 5px 0 !important; ' +
                                        'padding: 5px 0 !important; ' +
                                        'transition: none !important; ' +
                                        'animation: none !important; ' +
                                        'transform: none !important;';
                                    
                                    // Add dark or light mode specific styles
                                    if (isDarkMode) {
                                        cssText += 'background-color: #1e1e1e !important; ' +
                                            'box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important; ' +
                                            'border: 1px solid rgba(255, 255, 255, 0.1) !important; ' +
                                            'border-left: 2px solid var(--primary-color) !important; ';
                                    } else {
                                        cssText += 'background-color: #fff !important; ' +
                                            'box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important; ' +
                                            'border: 1px solid rgba(0, 0, 0, 0.1) !important; ' +
                                            'border-left: 2px solid var(--primary-color) !important; ';
                                    }
                                    
                                    newDropdown.style.cssText = cssText;
                                    
                                    // Add the new dropdown to the DOM
                                    parentElement.appendChild(newDropdown);
                                    
                                    // Style all links inside the new dropdown
                                    var links = newDropdown.querySelectorAll('a');
                                    links.forEach(function(link) {
                                        var linkCssText = 'display: block !important; ' +
                                            'padding: 10px 15px !important; ' +
                                            'color: var(--text-color) !important; ' +
                                            'text-decoration: none !important; ' +
                                            'transition: none !important; ' +
                                            'animation: none !important; ' +
                                            'transform: none !important;';
                                        
                                        // Add dark or light mode specific styles
                                        if (isDarkMode) {
                                            linkCssText += 'border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;';
                                        } else {
                                            linkCssText += 'border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;';
                                        }
                                        
                                        link.style.cssText = linkCssText;
                                        
                                        // Add hover effect
                                        link.addEventListener('mouseover', function() {
                                            if (isDarkMode) {
                                                this.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                                            } else {
                                                this.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
                                            }
                                        });
                                        
                                        link.addEventListener('mouseout', function() {
                                            this.style.backgroundColor = 'transparent';
                                        });
                                        
                                        // Add click handler for dropdown links
                                        link.addEventListener('click', function(e) {
                                            // If this is a Categories dropdown link, don't prevent default
                                            if (originalId === 'categories-dropdown') {
                                                console.log("Categories link clicked, allowing default behavior");
                                                // Close the dropdown and mobile menu
                                                dropdown.classList.remove('active');
                                                var navLinks = document.getElementById('nav-links');
                                                if (navLinks && navLinks.classList.contains('show')) {
                                                    navLinks.classList.remove('show');
                                                    var mobileMenuToggle = document.getElementById('mobile-menu-toggle');
                                                    if (mobileMenuToggle) {
                                                        var icon = mobileMenuToggle.querySelector('i');
                                                        if (icon) {
                                                            icon.className = 'fas fa-bars';
                                                        }
                                                    }
                                                }
                                            }
                                        });
                                    });
                                    
                                    // Style the last link
                                    if (links.length > 0) {
                                        links[links.length - 1].style.borderBottom = 'none !important';
                                    }
                                    
                                    console.log("Created new dropdown content element");
                                }
                            }
                            } else {
                                // On desktop, let the hover behavior work
                                return true;
                            }
                            
                            return false;
                        });
                    }
                });
                
                // Add click handlers for dropdown links
                document.querySelectorAll('.dropdown-content a').forEach(function(link) {
                    link.addEventListener('click', function() {
                        // Close the mobile menu when a dropdown item is clicked
                        if (window.innerWidth <= 767) {
                            // Close the dropdown
                            var dropdown = this.closest('.dropdown');
                            if (dropdown) {
                                dropdown.classList.remove('active');
                            }
                            
                            // Close the mobile menu
                            if (navLinks) {
                                navLinks.classList.remove('show');
                                // Change icon to bars
                                var icon = mobileMenuToggle.querySelector('i');
                                if (icon) {
                                    icon.className = 'fas fa-bars';
                                }
                            }
                        }
                    });
                });
                
                // Close menu and dropdowns when clicking outside
                document.onclick = function(e) {
                    // Close mobile menu when clicking outside
                    if (navLinks.classList.contains('show') && 
                        e.target !== mobileMenuToggle && 
                        !mobileMenuToggle.contains(e.target) &&
                        e.target !== navLinks && 
                        !navLinks.contains(e.target)) {
                        
                        navLinks.classList.remove('show');
                        // Change icon to bars
                        var icon = mobileMenuToggle.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-bars';
                        }
                    }
                    
                    // Close dropdowns when clicking outside - only on mobile
                    if (window.innerWidth <= 767) {
                        var isClickInsideDropdown = false;
                        dropdowns.forEach(function(dropdown) {
                            if (dropdown.contains(e.target)) {
                                isClickInsideDropdown = true;
                            }
                        });
                        
                        if (!isClickInsideDropdown) {
                        console.log("Click outside dropdown detected");
                        dropdowns.forEach(function(dropdown) {
                            if (dropdown.classList.contains('active')) {
                                console.log("Closing dropdown:", dropdown.querySelector('.dropbtn').textContent);
                                dropdown.classList.remove('active');
                                // Find and remove any dropdown content
                                var dropdownContents = dropdown.querySelectorAll('.dropdown-content');
                                dropdownContents.forEach(function(content) {
                                    if (content && content.parentNode) {
                                        console.log("Removing dropdown content with ID:", content.id || "no-id");
                                        content.parentNode.removeChild(content);
                                    }
                                });
                                
                                // Recreate the original dropdown content
                                var dropbtn = dropdown.querySelector('.dropbtn');
                                if (dropbtn && dropbtn.textContent === "Categories") {
                                    console.log("Recreating Categories dropdown");
                                    var newContent = document.createElement('div');
                                    newContent.className = 'dropdown-content';
                                    newContent.id = 'categories-dropdown';
                                    newContent.innerHTML = `
                                        <a href="index.html#daily-life">Daily Life</a>
                                        <a href="index.html#web-development">Web Development</a>
                                        <a href="index.html#cleaner">Cleaner</a>
                                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                                    `;
                                    dropdown.appendChild(newContent);
                                    newContent.style.display = 'none';
                                } else if (dropbtn && dropbtn.textContent === "Videos") {
                                    console.log("Recreating Videos dropdown");
                                    var newContent = document.createElement('div');
                                    newContent.className = 'dropdown-content';
                                    newContent.id = 'channels-dropdown';
                                    newContent.innerHTML = `
                                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                                        <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                        <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                        <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                        <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                        <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                                        <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                        <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                        <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                        <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                                    `;
                                    dropdown.appendChild(newContent);
                                    newContent.style.display = 'none';
                                }
                            }
                        });
                        console.log("All dropdowns closed on outside click");
                    }
                    }
                };
                
                console.log("Mobile menu setup complete");
            } else {
                console.log("Could not find mobile menu elements");
            }
        }
        
        // Try to set up immediately as well
        setupMobileMenu();
    </script>
</body>

<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        console.log("Setting up unified mobile menu toggle");
        mobileMenuToggle.addEventListener('click', function(e) {
            console.log("Mobile menu toggle clicked");
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Handle dropdown menus in mobile view
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const dropbtn = dropdown.querySelector('.dropbtn');
        if (dropbtn) {
            dropbtn.addEventListener('click', function(e) {
                // Only in mobile view
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('active');
                }
            });
        }
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (navLinks && navLinks.classList.contains('show')) {
            // Check if click is outside the nav menu
            if (!navLinks.contains(e.target) && e.target !== mobileMenuToggle) {
                navLinks.classList.remove('show');
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                    
                    // Change icon back to bars
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-bars';
                    }
                }
            }
        }
    });

    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Only handle submenu on desktop or when mobile menu is open
                if (window.innerWidth > 767 || (navLinks && navLinks.classList.contains('show'))) {
                    // Close other active submenus
                    dropdownSubmenus.forEach(otherSubmenu => {
                        if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                            otherSubmenu.classList.remove('active');
                        }
                    });

                    // Toggle current submenu
                    submenu.classList.toggle('active');
                }
            });
        }
    });

    // Close submenus when clicking outside (but not when mobile menu is involved)
    document.addEventListener('click', function(e) {
        // Don't close submenus if clicking on mobile menu elements
        if (!e.target.closest('.dropdown-submenu') &&
            !e.target.closest('.mobile-menu-toggle') &&
            !e.target.closest('.nav-links')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });
});
</script>
</html>







