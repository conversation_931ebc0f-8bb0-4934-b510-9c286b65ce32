﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Negative Sentences in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Negative Sentences</span>
        </div>
        
        <section class="grammar-section">
            <h2>Negative Sentences in Finnish</h2>
            <p>Finnish has a unique way of forming negative sentences using a special negative verb "ei" that changes according to person. This page explains the basic principles of negation in Finnish and how to form negative sentences in the present tense.</p>
        </section>

        <section class="grammar-category">
            <h3>THE NEGATIVE VERB</h3>
            
            <div class="grammar-content">
                <p>In Finnish, negation is formed using the negative verb "ei" which is conjugated according to person:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Negative Verb</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>minä (I)</td>
                        <td>en</td>
                        <td><span class="finnish">En puhu suomea.</span> <span class="english">I don't speak Finnish.</span></td>
                    </tr>
                    <tr>
                        <td>sinä (you)</td>
                        <td>et</td>
                        <td><span class="finnish">Et puhu suomea.</span> <span class="english">You don't speak Finnish.</span></td>
                    </tr>
                    <tr>
                        <td>hän (he/she)</td>
                        <td>ei</td>
                        <td><span class="finnish">Hän ei puhu suomea.</span> <span class="english">He/she doesn't speak Finnish.</span></td>
                    </tr>
                    <tr>
                        <td>me (we)</td>
                        <td>emme</td>
                        <td><span class="finnish">Emme puhu suomea.</span> <span class="english">We don't speak Finnish.</span></td>
                    </tr>
                    <tr>
                        <td>te (you plural)</td>
                        <td>ette</td>
                        <td><span class="finnish">Ette puhu suomea.</span> <span class="english">You don't speak Finnish.</span></td>
                    </tr>
                    <tr>
                        <td>he (they)</td>
                        <td>eivät</td>
                        <td><span class="finnish">He eivät puhu suomea.</span> <span class="english">They don't speak Finnish.</span></td>
                    </tr>
                </table>
                
                <p>The negative verb carries the person information, while the main verb appears in a special form called the "connegative" form, which is usually identical to the second person singular imperative form.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>FORMING NEGATIVE SENTENCES</h3>
            
            <div class="grammar-content">
                <p>To form a negative sentence in the present tense:</p>
                
                <ol>
                    <li>Use the appropriate form of the negative verb "ei" according to the subject</li>
                    <li>Add the main verb in its connegative form (which is the same as the imperative singular form without any personal endings)</li>
                </ol>
                
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Minä puhun suomea.</span> <span class="english">I speak Finnish.</span></p>
                    <p>Negative: <span class="finnish">Minä en puhu suomea.</span> <span class="english">I don't speak Finnish.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Hän asuu Helsingissä.</span> <span class="english">He/she lives in Helsinki.</span></p>
                    <p>Negative: <span class="finnish">Hän ei asu Helsingissä.</span> <span class="english">He/she doesn't live in Helsinki.</span></p>
                </div>
                
                <p>Note that in the negative form, the main verb loses its personal ending and appears in its basic stem form.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>VERB TYPES IN NEGATION</h3>
            
            <div class="grammar-content">
                <p>Different verb types have different connegative forms:</p>
                
                <h4>1. Type 1 verbs (ending in -a/-ä, two syllables)</h4>
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Minä puhun.</span> <span class="english">I speak.</span></p>
                    <p>Negative: <span class="finnish">Minä en puhu.</span> <span class="english">I don't speak.</span></p>
                    <p>Connegative form: <span class="finnish">puhu</span> (from <span class="finnish">puhua</span>)</p>
                </div>
                
                <h4>2. Type 2 verbs (ending in -da/-dä)</h4>
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Minä syön.</span> <span class="english">I eat.</span></p>
                    <p>Negative: <span class="finnish">Minä en syö.</span> <span class="english">I don't eat.</span></p>
                    <p>Connegative form: <span class="finnish">syö</span> (from <span class="finnish">syödä</span>)</p>
                </div>
                
                <h4>3. Type 3 verbs (ending in -la/-lä, -na/-nä, -ra/-rä, etc.)</h4>
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Minä menen.</span> <span class="english">I go.</span></p>
                    <p>Negative: <span class="finnish">Minä en mene.</span> <span class="english">I don't go.</span></p>
                    <p>Connegative form: <span class="finnish">mene</span> (from <span class="finnish">mennä</span>)</p>
                </div>
                
                <h4>4. Type 4 verbs (ending in -ta/-tä)</h4>
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Minä haluan.</span> <span class="english">I want.</span></p>
                    <p>Negative: <span class="finnish">Minä en halua.</span> <span class="english">I don't want.</span></p>
                    <p>Connegative form: <span class="finnish">halua</span> (from <span class="finnish">haluta</span>)</p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>NEGATION WITH "OLLA" (TO BE)</h3>
            
            <div class="grammar-content">
                <p>The verb "olla" (to be) follows the same negation pattern as other verbs:</p>
                
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Minä olen kotona.</span> <span class="english">I am at home.</span></p>
                    <p>Negative: <span class="finnish">Minä en ole kotona.</span> <span class="english">I am not at home.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Hän on opettaja.</span> <span class="english">He/she is a teacher.</span></p>
                    <p>Negative: <span class="finnish">Hän ei ole opettaja.</span> <span class="english">He/she is not a teacher.</span></p>
                </div>
                
                <p>The connegative form of "olla" is "ole".</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>WORD ORDER IN NEGATIVE SENTENCES</h3>
            
            <div class="grammar-content">
                <p>The word order in negative sentences typically follows this pattern:</p>
                
                <ol>
                    <li>Subject</li>
                    <li>Negative verb (en, et, ei, emme, ette, eivät)</li>
                    <li>Main verb in connegative form</li>
                    <li>Other elements (objects, adverbials, etc.)</li>
                </ol>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä en puhu suomea hyvin.</span> <span class="english">I don't speak Finnish well.</span></p>
                    <p><span class="finnish">Hän ei asu Helsingissä enää.</span> <span class="english">He/she doesn't live in Helsinki anymore.</span></p>
                </div>
                
                <p>However, as with affirmative sentences, the word order can be varied for emphasis:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Suomea minä en puhu.</span> <span class="english">Finnish is what I don't speak. (emphasis on "Finnish")</span></p>
                    <p><span class="finnish">Helsingissä hän ei asu.</span> <span class="english">In Helsinki is where he/she doesn't live. (emphasis on "Helsinki")</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>NEGATIVE EXISTENTIAL SENTENCES</h3>
            
            <div class="grammar-content">
                <p>Existential sentences (sentences that express existence) have a special negative pattern where the subject is in the partitive case:</p>
                
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Pöydällä on kirja.</span> <span class="english">There is a book on the table.</span></p>
                    <p>Negative: <span class="finnish">Pöydällä ei ole kirjaa.</span> <span class="english">There is no book on the table.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Affirmative: <span class="finnish">Kaupungissa on ihmisiä.</span> <span class="english">There are people in the city.</span></p>
                    <p>Negative: <span class="finnish">Kaupungissa ei ole ihmisiä.</span> <span class="english">There are no people in the city.</span></p>
                </div>
                
                <p>Note that in the negative existential sentences, the subject changes from nominative (kirja, ihmisiä) to partitive (kirjaa, ihmisiä).</p>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












