﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potential Mood - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="potential-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="potential-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="potential-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Potential Mood</span>
        </div>
        
        <section class="grammar-section">
            <h2>Potential Mood in Finnish</h2>
            <p>The potential mood (potentiaali) in Finnish is used to express probability, possibility, or likelihood. It's similar to the English expressions "may," "might," or "probably." The potential mood is less common in everyday speech but appears in formal writing, news, and literature.</p>
            
            <div class="note-box">
                <p><strong>Note:</strong> In modern spoken Finnish, the potential mood is often replaced by modal verbs like "saattaa" (might), "voi" (can/may), or adverbs like "ehkä" (maybe), "todennäköisesti" (probably).</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Formation of the Potential</h3>
            <p>The potential mood is formed by adding the marker -ne- to the verb stem, followed by personal endings. The formation varies slightly depending on the verb type.</p>
            
            <h4>Basic Formation</h4>
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-nen</td>
                    <td>puhunen</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-net</td>
                    <td>puhunet</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>-nee</td>
                    <td>puhunee</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-nemme</td>
                    <td>puhunemme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-nette</td>
                    <td>puhunette</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-nevat</td>
                    <td>puhunevat</td>
                </tr>
            </table>
            
            <h4>Formation by Verb Type</h4>
            <p>The formation varies slightly depending on the verb type:</p>
            
            <table class="example-table">
                <tr>
                    <th>Verb Type</th>
                    <th>Infinitive</th>
                    <th>Potential Stem</th>
                    <th>Example (hän)</th>
                </tr>
                <tr>
                    <td>Type 1</td>
                    <td>puhua (to speak)</td>
                    <td>puhu + ne</td>
                    <td>puhunee</td>
                </tr>
                <tr>
                    <td>Type 2</td>
                    <td>syödä (to eat)</td>
                    <td>syö + ne</td>
                    <td>syönee</td>
                </tr>
                <tr>
                    <td>Type 3</td>
                    <td>tulla (to come)</td>
                    <td>tul + le</td>
                    <td>tullee</td>
                </tr>
                <tr>
                    <td>Type 4</td>
                    <td>haluta (to want)</td>
                    <td>halun + ne</td>
                    <td>halunnee</td>
                </tr>
                <tr>
                    <td>Type 5</td>
                    <td>tarvita (to need)</td>
                    <td>tarvin + ne</td>
                    <td>tarvinnee</td>
                </tr>
                <tr>
                    <td>Type 6</td>
                    <td>vanheta (to grow old)</td>
                    <td>vanhen + ne</td>
                    <td>vanhennee</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> For Type 3 verbs, the potential marker is -le- instead of -ne-.</p>
                <p>For Types 4, 5, and 6, the potential is formed from a special stem that often includes consonant gradation.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Potential</h3>
            <p>To form the negative potential, use the negative verb "ei" conjugated for person, followed by the potential form without the personal ending.</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative verb</th>
                    <th>Potential stem</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en</td>
                    <td>puhune</td>
                    <td>en puhune</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et</td>
                    <td>puhune</td>
                    <td>et puhune</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei</td>
                    <td>puhune</td>
                    <td>ei puhune</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme</td>
                    <td>puhune</td>
                    <td>emme puhune</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette</td>
                    <td>puhune</td>
                    <td>ette puhune</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät</td>
                    <td>puhune</td>
                    <td>eivät puhune</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Perfect Potential</h3>
            <p>The perfect potential is used to express probability about past events. It's formed using the potential of "olla" (to be) followed by the past participle of the main verb.</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>olla (potential)</th>
                    <th>Past participle</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>lienen</td>
                    <td>puhunut</td>
                    <td>lienen puhunut</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>lienet</td>
                    <td>puhunut</td>
                    <td>lienet puhunut</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>lienee</td>
                    <td>puhunut</td>
                    <td>lienee puhunut</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>lienemme</td>
                    <td>puhuneet</td>
                    <td>lienemme puhuneet</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>lienette</td>
                    <td>puhuneet</td>
                    <td>lienette puhuneet</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>lienevät</td>
                    <td>puhuneet</td>
                    <td>lienevät puhuneet</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> The potential form of "olla" is irregular: lienen, lienet, lienee, etc.</p>
            </div>
            
            <h4>Negative Perfect Potential</h4>
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative olla (potential)</th>
                    <th>Past participle</th>
                    <th>puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en liene</td>
                    <td>puhunut</td>
                    <td>en liene puhunut</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et liene</td>
                    <td>puhunut</td>
                    <td>et liene puhunut</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei liene</td>
                    <td>puhunut</td>
                    <td>ei liene puhunut</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme liene</td>
                    <td>puhuneet</td>
                    <td>emme liene puhuneet</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette liene</td>
                    <td>puhuneet</td>
                    <td>ette liene puhuneet</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät liene</td>
                    <td>puhuneet</td>
                    <td>eivät liene puhuneet</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Uses of the Potential Mood</h3>
            
            <h4>1. Expressing Probability</h4>
            <div class="example-box">
                <p>Hän <strong>tullee</strong> huomenna. (He/she will probably come tomorrow.)</p>
                <p>Sää <strong>lienee</strong> huomenna parempi. (The weather is probably going to be better tomorrow.)</p>
            </div>
            
            <h4>2. Expressing Possibility</h4>
            <div class="example-box">
                <p>Hän <strong>osannee</strong> vastata kysymykseen. (He/she may be able to answer the question.)</p>
                <p>Tämä <strong>riittänee</strong>. (This might be enough.)</p>
            </div>
            
            <h4>3. Expressing Uncertainty</h4>
            <div class="example-box">
                <p>Mitä hän <strong>tehnee</strong> nyt? (What might he/she be doing now?)</p>
                <p>Missä he <strong>lienevät</strong>? (Where might they be?)</p>
            </div>
            
            <h4>4. In Formal Writing</h4>
            <div class="example-box">
                <p>Hallitus <strong>esittänee</strong> uuden lakiehdotuksen ensi viikolla. (The government will probably present a new bill next week.)</p>
                <p>Tämä <strong>johtunee</strong> monista tekijöistä. (This is probably due to many factors.)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Alternative Expressions in Spoken Finnish</h3>
            <p>In everyday spoken Finnish, the potential mood is often replaced by other constructions:</p>
            
            <table class="example-table">
                <tr>
                    <th>Potential Mood</th>
                    <th>Spoken Alternative</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Hän tullee huomenna.</td>
                    <td>Hän tulee varmaan/ehkä/todennäköisesti huomenna.</td>
                    <td>He/she will probably/maybe/likely come tomorrow.</td>
                </tr>
                <tr>
                    <td>Sää lienee huomenna parempi.</td>
                    <td>Sää on varmaan/ehkä huomenna parempi.</td>
                    <td>The weather is probably/maybe going to be better tomorrow.</td>
                </tr>
                <tr>
                    <td>Hän osannee vastata.</td>
                    <td>Hän varmaan/ehkä osaa vastata.</td>
                    <td>He/she probably/maybe knows how to answer.</td>
                </tr>
                <tr>
                    <td>Tämä riittänee.</td>
                    <td>Tämä varmaan/ehkä riittää.</td>
                    <td>This is probably/maybe enough.</td>
                </tr>
            </table>
            
            <h4>Common Modal Verbs and Adverbs</h4>
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>varmaan, varmaankin</td>
                    <td>probably</td>
                </tr>
                <tr>
                    <td>ehkä</td>
                    <td>maybe, perhaps</td>
                </tr>
                <tr>
                    <td>todennäköisesti</td>
                    <td>likely, probably</td>
                </tr>
                <tr>
                    <td>mahdollisesti</td>
                    <td>possibly</td>
                </tr>
                <tr>
                    <td>saattaa</td>
                    <td>might, may</td>
                </tr>
                <tr>
                    <td>voi olla että</td>
                    <td>it may be that</td>
                </tr>
                <tr>
                    <td>taitaa</td>
                    <td>seems to, appears to</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>The Verb "Lienee"</h3>
            <p>The potential form of "olla" (to be), "lienee," is sometimes used as a standalone verb meaning "might be" or "is probably." It's one of the most common potential forms in modern Finnish.</p>
            
            <div class="example-box">
                <p>Missä hän <strong>lienee</strong>? (Where might he/she be?)</p>
                <p>Se <strong>lienee</strong> totta. (It is probably true.)</p>
                <p>Kuka hän <strong>lieneekin</strong>. (Whoever he/she might be.)</p>
                <p>Mikä <strong>lienee</strong> syy? (What might be the reason?)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Hän tullee myöhässä.</td>
                    <td>He/she will probably come late.</td>
                </tr>
                <tr>
                    <td>Tämä riittänee kaikille.</td>
                    <td>This will probably be enough for everyone.</td>
                </tr>
                <tr>
                    <td>He lienevät jo kotona.</td>
                    <td>They are probably already at home.</td>
                </tr>
                <tr>
                    <td>Syy lienee selvä.</td>
                    <td>The reason is probably clear.</td>
                </tr>
                <tr>
                    <td>En tiennee vastausta.</td>
                    <td>I probably don't know the answer.</td>
                </tr>
                <tr>
                    <td>Hän lienee puhunut totta.</td>
                    <td>He/she probably spoke the truth.</td>
                </tr>
                <tr>
                    <td>Missä he lienevät olleet?</td>
                    <td>Where might they have been?</td>
                </tr>
                <tr>
                    <td>Tämä ei liene yllätys kenellekään.</td>
                    <td>This is probably not a surprise to anyone.</td>
                </tr>
                <tr>
                    <td>Kukaan ei tiennee totuutta.</td>
                    <td>Probably no one knows the truth.</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















