﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type 2 Verbs - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="type2-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="type2-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="type2-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Type 2 Verbs</span>
        </div>
        
        <section class="grammar-section">
            <h2>Type 2 Verbs: -da/-dä</h2>
            <p>Type 2 verbs in Finnish end in -da or -dä (depending on vowel harmony). These verbs have a vowel before the -da/-dä ending.</p>
            
            <h3>Characteristics</h3>
            <ul>
                <li>End in -da or -dä preceded by a vowel</li>
                <li>The stem is formed by removing the final -da/-dä</li>
                <li>Examples: syödä (to eat), juoda (to drink), saada (to get/receive), tehdä (to do/make)</li>
            </ul>
            
            <div class="example-box">
                <p><strong>syödä</strong> (to eat) → stem: <strong>syö-</strong></p>
                <p><strong>juoda</strong> (to drink) → stem: <strong>juo-</strong></p>
                <p><strong>saada</strong> (to get/receive) → stem: <strong>saa-</strong></p>
                <p><strong>tehdä</strong> (to do/make) → stem: <strong>teh-</strong> (irregular)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> The verb <strong>tehdä</strong> (to do/make) is slightly irregular in its conjugation.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Present Tense Conjugation</h3>
            <p>To form the present tense, add the personal endings to the stem:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>syödä (to eat)</th>
                    <th>juoda (to drink)</th>
                    <th>tehdä (to do/make)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-n</td>
                    <td>syön</td>
                    <td>juon</td>
                    <td>teen</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-t</td>
                    <td>syöt</td>
                    <td>juot</td>
                    <td>teet</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>-</td>
                    <td>syö</td>
                    <td>juo</td>
                    <td>tekee</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-mme</td>
                    <td>syömme</td>
                    <td>juomme</td>
                    <td>teemme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-tte</td>
                    <td>syötte</td>
                    <td>juotte</td>
                    <td>teette</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-vat/-vät</td>
                    <td>syövät</td>
                    <td>juovat</td>
                    <td>tekevät</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Unlike Type 1 verbs, the 3rd person singular (hän) form of Type 2 verbs typically doesn't change the stem vowel. However, some irregular verbs like <strong>tehdä</strong> have special forms (teh- → tekee).</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Past Tense Conjugation</h3>
            <p>To form the past tense (imperfect), add -i- to the stem, followed by the personal endings:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>syödä (to eat)</th>
                    <th>juoda (to drink)</th>
                    <th>tehdä (to do/make)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>söin</td>
                    <td>join</td>
                    <td>tein</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>söit</td>
                    <td>joit</td>
                    <td>teit</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>söi</td>
                    <td>joi</td>
                    <td>teki</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>söimme</td>
                    <td>joimme</td>
                    <td>teimme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>söitte</td>
                    <td>joitte</td>
                    <td>teitte</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>söivät</td>
                    <td>joivat</td>
                    <td>tekivät</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Many Type 2 verbs undergo vowel changes in the past tense. For example:</p>
                <ul>
                    <li>syö- → sö- (ö replaces yö)</li>
                    <li>juo- → jo- (o replaces uo)</li>
                    <li>vie- → ve- (e replaces ie)</li>
                </ul>
                <p>These are regular sound changes in Finnish.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Forms</h3>
            <p>To form negative sentences, use the negative verb "ei" conjugated for person, followed by the basic stem:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative verb</th>
                    <th>syödä (to eat)</th>
                    <th>juoda (to drink)</th>
                    <th>tehdä (to do/make)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en</td>
                    <td>en syö</td>
                    <td>en juo</td>
                    <td>en tee</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et</td>
                    <td>et syö</td>
                    <td>et juo</td>
                    <td>et tee</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei</td>
                    <td>ei syö</td>
                    <td>ei juo</td>
                    <td>ei tee</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme</td>
                    <td>emme syö</td>
                    <td>emme juo</td>
                    <td>emme tee</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette</td>
                    <td>ette syö</td>
                    <td>ette juo</td>
                    <td>ette tee</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät</td>
                    <td>eivät syö</td>
                    <td>eivät juo</td>
                    <td>eivät tee</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Common Type 2 Verbs</h3>
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>syödä</td>
                    <td>to eat</td>
                    <td>juoda</td>
                    <td>to drink</td>
                </tr>
                <tr>
                    <td>saada</td>
                    <td>to get, receive</td>
                    <td>tehdä</td>
                    <td>to do, make</td>
                </tr>
                <tr>
                    <td>viedä</td>
                    <td>to take (to)</td>
                    <td>tuoda</td>
                    <td>to bring</td>
                </tr>
                <tr>
                    <td>myydä</td>
                    <td>to sell</td>
                    <td>lyödä</td>
                    <td>to hit</td>
                </tr>
                <tr>
                    <td>jäädä</td>
                    <td>to stay, remain</td>
                    <td>voida</td>
                    <td>can, to be able to</td>
                </tr>
                <tr>
                    <td>uida</td>
                    <td>to swim</td>
                    <td>luoda</td>
                    <td>to create</td>
                </tr>
                <tr>
                    <td>käydä</td>
                    <td>to visit, go</td>
                    <td>pyydä</td>
                    <td>to ask for, request</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Example Sentences</h3>
            <div class="example-box">
                <p>Minä <strong>syön</strong> aamiaista. (I eat breakfast.)</p>
                <p>Hän <strong>juo</strong> kahvia. (He/she drinks coffee.)</p>
                <p>Me <strong>teemme</strong> ruokaa. (We make food.)</p>
                <p>He <strong>tuovat</strong> lahjoja. (They bring gifts.)</p>
                <p>Minä en <strong>syö</strong> lihaa. (I don't eat meat.)</p>
                <p>Hän ei <strong>juo</strong> alkoholia. (He/she doesn't drink alcohol.)</p>
                <p>Minä <strong>söin</strong> eilen ravintolassa. (I ate at a restaurant yesterday.)</p>
                <p>He <strong>joivat</strong> teetä. (They drank tea.)</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















