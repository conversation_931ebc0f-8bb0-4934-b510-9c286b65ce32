﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Present Tense - Finnish Grammar - Opiskelen <PERSON></title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="present-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="present-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="present-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Present Tense</span>
        </div>
        
        <section class="grammar-section">
            <h2>Present Tense in Finnish</h2>
            <p>The present tense (preesens) in Finnish is used to express actions happening now, habitual actions, and general truths. It's also commonly used to express future actions when the context is clear.</p>
            
            <h3>Formation</h3>
            <p>The present tense is formed by adding personal endings to the verb stem. The formation varies slightly depending on the verb type.</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Ending</th>
                    <th>Example: puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>-n</td>
                    <td>puhun</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>-t</td>
                    <td>puhut</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>varies by verb type</td>
                    <td>puhuu</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>-mme</td>
                    <td>puhumme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>-tte</td>
                    <td>puhutte</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>-vat/-vät</td>
                    <td>puhuvat</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Present Tense by Verb Type</h3>
            
            <h4>Type 1 Verbs (-a/-ä)</h4>
            <p>For Type 1 verbs, the 3rd person singular form lengthens the final vowel of the stem.</p>
            
            <div class="example-box">
                <p><strong>puhua</strong> (to speak)</p>
                <p>minä <strong>puhun</strong>, sinä <strong>puhut</strong>, hän <strong>puhuu</strong></p>
                <p>me <strong>puhumme</strong>, te <strong>puhutte</strong>, he <strong>puhuvat</strong></p>
            </div>
            
            <h4>Type 2 Verbs (-da/-dä)</h4>
            <p>For Type 2 verbs, the 3rd person singular typically uses the basic stem.</p>
            
            <div class="example-box">
                <p><strong>syödä</strong> (to eat)</p>
                <p>minä <strong>syön</strong>, sinä <strong>syöt</strong>, hän <strong>syö</strong></p>
                <p>me <strong>syömme</strong>, te <strong>syötte</strong>, he <strong>syövät</strong></p>
            </div>
            
            <h4>Type 3 Verbs (consonant + a/ä)</h4>
            <p>For Type 3 verbs, the 3rd person singular lengthens the final -e of the stem.</p>
            
            <div class="example-box">
                <p><strong>tulla</strong> (to come)</p>
                <p>minä <strong>tulen</strong>, sinä <strong>tulet</strong>, hän <strong>tulee</strong></p>
                <p>me <strong>tulemme</strong>, te <strong>tulette</strong>, he <strong>tulevat</strong></p>
            </div>
            
            <h4>Type 4 Verbs (-ta/-tä)</h4>
            <p>For Type 4 verbs, the -t- changes to -a-/-ä- in all forms.</p>
            
            <div class="example-box">
                <p><strong>haluta</strong> (to want)</p>
                <p>minä <strong>haluan</strong>, sinä <strong>haluat</strong>, hän <strong>haluaa</strong></p>
                <p>me <strong>haluamme</strong>, te <strong>haluatte</strong>, he <strong>haluavat</strong></p>
            </div>
            
            <h4>Type 5 Verbs (-ita/-itä)</h4>
            <p>For Type 5 verbs, the -it- changes to -itse- in all forms, with the 3rd person singular lengthening the final -e.</p>
            
            <div class="example-box">
                <p><strong>tarvita</strong> (to need)</p>
                <p>minä <strong>tarvitsen</strong>, sinä <strong>tarvitset</strong>, hän <strong>tarvitsee</strong></p>
                <p>me <strong>tarvitsemme</strong>, te <strong>tarvitsette</strong>, he <strong>tarvitsevat</strong></p>
            </div>
            
            <h4>Type 6 Verbs (-eta/-etä)</h4>
            <p>For Type 6 verbs, the -et- changes to -ene- in all forms, with the 3rd person singular lengthening the final -e.</p>
            
            <div class="example-box">
                <p><strong>vanheta</strong> (to grow old)</p>
                <p>minä <strong>vanhenen</strong>, sinä <strong>vanhenet</strong>, hän <strong>vanhenee</strong></p>
                <p>me <strong>vanhenemme</strong>, te <strong>vanhenette</strong>, he <strong>vanhenevat</strong></p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Negative Present Tense</h3>
            <p>To form the negative present tense, use the negative verb "ei" conjugated for person, followed by the basic stem of the main verb.</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Negative verb</th>
                    <th>Example: puhua (to speak)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>en</td>
                    <td>en puhu</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>et</td>
                    <td>et puhu</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>ei</td>
                    <td>ei puhu</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>emme</td>
                    <td>emme puhu</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>ette</td>
                    <td>ette puhu</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>eivät</td>
                    <td>eivät puhu</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In negative sentences, the main verb always appears in its basic form, regardless of the verb type.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Uses of the Present Tense</h3>
            
            <h4>1. Current Actions</h4>
            <div class="example-box">
                <p>Minä <strong>puhun</strong> nyt suomea. (I am speaking Finnish now.)</p>
                <p>Hän <strong>lukee</strong> kirjaa. (He/she is reading a book.)</p>
            </div>
            
            <h4>2. Habitual Actions</h4>
            <div class="example-box">
                <p>Minä <strong>käyn</strong> kuntosalilla joka päivä. (I go to the gym every day.)</p>
                <p>He <strong>syövät</strong> aamiaista kello 8. (They eat breakfast at 8 o'clock.)</p>
            </div>
            
            <h4>3. General Truths</h4>
            <div class="example-box">
                <p>Vesi <strong>kiehuu</strong> 100 asteessa. (Water boils at 100 degrees.)</p>
                <p>Aurinko <strong>nousee</strong> idästä. (The sun rises in the east.)</p>
            </div>
            
            <h4>4. Future Actions</h4>
            <div class="example-box">
                <p>Minä <strong>menen</strong> huomenna elokuviin. (I will go to the movies tomorrow.)</p>
                <p>Hän <strong>tulee</strong> ensi viikolla. (He/she will come next week.)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> Finnish doesn't have a separate future tense. The present tense is used to express future actions, often with time expressions like "huomenna" (tomorrow) or "ensi viikolla" (next week).</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Special Cases</h3>
            
            <h4>The Verb "olla" (to be)</h4>
            <p>The verb "olla" (to be) is irregular in the present tense:</p>
            
            <div class="example-box">
                <p>minä <strong>olen</strong> (I am)</p>
                <p>sinä <strong>olet</strong> (you are)</p>
                <p>hän <strong>on</strong> (he/she is)</p>
                <p>me <strong>olemme</strong> (we are)</p>
                <p>te <strong>olette</strong> (you are, plural)</p>
                <p>he <strong>ovat</strong> (they are)</p>
            </div>
            
            <h4>Consonant Gradation</h4>
            <p>Many Finnish verbs undergo consonant gradation in certain forms. This affects the present tense conjugation.</p>
            
            <div class="example-box">
                <p><strong>lukea</strong> (to read): minä <strong>luen</strong>, sinä <strong>luet</strong>, hän <strong>lukee</strong></p>
                <p><strong>tietää</strong> (to know): minä <strong>tiedän</strong>, sinä <strong>tiedät</strong>, hän <strong>tietää</strong></p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Minä puhun suomea.</td>
                    <td>I speak Finnish.</td>
                </tr>
                <tr>
                    <td>Sinä asut Helsingissä.</td>
                    <td>You live in Helsinki.</td>
                </tr>
                <tr>
                    <td>Hän opiskelee yliopistossa.</td>
                    <td>He/she studies at the university.</td>
                </tr>
                <tr>
                    <td>Me menemme kauppaan.</td>
                    <td>We go to the store.</td>
                </tr>
                <tr>
                    <td>Te puhutte englantia.</td>
                    <td>You (plural) speak English.</td>
                </tr>
                <tr>
                    <td>He tulevat Suomeen.</td>
                    <td>They come to Finland.</td>
                </tr>
                <tr>
                    <td>Minä en puhu ruotsia.</td>
                    <td>I don't speak Swedish.</td>
                </tr>
                <tr>
                    <td>Hän ei asu Turussa.</td>
                    <td>He/she doesn't live in Turku.</td>
                </tr>
                <tr>
                    <td>Me emme mene kotiin.</td>
                    <td>We don't go home.</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












