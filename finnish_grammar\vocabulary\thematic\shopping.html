﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="shopping-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="shopping-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="shopping-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Shopping</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Shopping Vocabulary in Finnish</h2>
            <p>This page covers essential Finnish vocabulary related to shopping, stores, products, and payment methods. Whether you're grocery shopping, buying clothes, or just browsing, these terms will help you navigate shopping experiences in Finland.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>General Shopping Terms</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>kauppa</td>
                    <td class="pronunciation">kaup-pa</td>
                    <td>shop, store</td>
                </tr>
                <tr>
                    <td>ostaa</td>
                    <td class="pronunciation">os-taa</td>
                    <td>to buy</td>
                </tr>
                <tr>
                    <td>myydä</td>
                    <td class="pronunciation">myy-dä</td>
                    <td>to sell</td>
                </tr>
                <tr>
                    <td>maksaa</td>
                    <td class="pronunciation">mak-saa</td>
                    <td>to pay</td>
                </tr>
                <tr>
                    <td>hinta</td>
                    <td class="pronunciation">hin-ta</td>
                    <td>price</td>
                </tr>
                <tr>
                    <td>alennus</td>
                    <td class="pronunciation">a-len-nus</td>
                    <td>discount</td>
                </tr>
                <tr>
                    <td>alennusmyynti</td>
                    <td class="pronunciation">a-len-nus-myyn-ti</td>
                    <td>sale</td>
                </tr>
                <tr>
                    <td>ostoslista</td>
                    <td class="pronunciation">os-tos-lis-ta</td>
                    <td>shopping list</td>
                </tr>
                <tr>
                    <td>ostoskori</td>
                    <td class="pronunciation">os-tos-ko-ri</td>
                    <td>shopping basket</td>
                </tr>
                <tr>
                    <td>ostoskärry</td>
                    <td class="pronunciation">os-tos-kär-ry</td>
                    <td>shopping cart</td>
                </tr>
                <tr>
                    <td>asiakas</td>
                    <td class="pronunciation">a-si-a-kas</td>
                    <td>customer</td>
                </tr>
                <tr>
                    <td>myyjä</td>
                    <td class="pronunciation">myy-jä</td>
                    <td>salesperson</td>
                </tr>
                <tr>
                    <td>kassa</td>
                    <td class="pronunciation">kas-sa</td>
                    <td>cash register, checkout</td>
                </tr>
                <tr>
                    <td>kuitti</td>
                    <td class="pronunciation">kuit-ti</td>
                    <td>receipt</td>
                </tr>
                <tr>
                    <td>takuu</td>
                    <td class="pronunciation">ta-kuu</td>
                    <td>warranty, guarantee</td>
                </tr>
                <tr>
                    <td>palautus</td>
                    <td class="pronunciation">pa-lau-tus</td>
                    <td>return</td>
                </tr>
                <tr>
                    <td>vaihto</td>
                    <td class="pronunciation">vaih-to</td>
                    <td>exchange</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Paljonko tämä maksaa?</p>
                <p><strong>English:</strong> How much does this cost?</p>
                <p><strong>Finnish:</strong> Onko teillä alennusmyyntiä?</p>
                <p><strong>English:</strong> Do you have a sale?</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Types of Stores</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>ruokakauppa</td>
                    <td class="pronunciation">ruo-ka-kaup-pa</td>
                    <td>grocery store</td>
                </tr>
                <tr>
                    <td>supermarketti</td>
                    <td class="pronunciation">su-per-mar-ket-ti</td>
                    <td>supermarket</td>
                </tr>
                <tr>
                    <td>tavaratalo</td>
                    <td class="pronunciation">ta-va-ra-ta-lo</td>
                    <td>department store</td>
                </tr>
                <tr>
                    <td>ostoskeskus</td>
                    <td class="pronunciation">os-tos-kes-kus</td>
                    <td>shopping center, mall</td>
                </tr>
                <tr>
                    <td>vaatekauppa</td>
                    <td class="pronunciation">vaa-te-kaup-pa</td>
                    <td>clothing store</td>
                </tr>
                <tr>
                    <td>kirjakauppa</td>
                    <td class="pronunciation">kir-ja-kaup-pa</td>
                    <td>bookstore</td>
                </tr>
                <tr>
                    <td>apteekki</td>
                    <td class="pronunciation">ap-teek-ki</td>
                    <td>pharmacy</td>
                </tr>
                <tr>
                    <td>leipomo</td>
                    <td class="pronunciation">lei-po-mo</td>
                    <td>bakery</td>
                </tr>
                <tr>
                    <td>lihakauppa</td>
                    <td class="pronunciation">li-ha-kaup-pa</td>
                    <td>butcher shop</td>
                </tr>
                <tr>
                    <td>kalakauppa</td>
                    <td class="pronunciation">ka-la-kaup-pa</td>
                    <td>fish market</td>
                </tr>
                <tr>
                    <td>kukkakauppa</td>
                    <td class="pronunciation">kuk-ka-kaup-pa</td>
                    <td>flower shop</td>
                </tr>
                <tr>
                    <td>elektroniikkakauppa</td>
                    <td class="pronunciation">e-lek-tro-niik-ka-kaup-pa</td>
                    <td>electronics store</td>
                </tr>
                <tr>
                    <td>huonekalukauppa</td>
                    <td class="pronunciation">huo-ne-ka-lu-kaup-pa</td>
                    <td>furniture store</td>
                </tr>
                <tr>
                    <td>rautakauppa</td>
                    <td class="pronunciation">rau-ta-kaup-pa</td>
                    <td>hardware store</td>
                </tr>
                <tr>
                    <td>tori</td>
                    <td class="pronunciation">to-ri</td>
                    <td>market square</td>
                </tr>
                <tr>
                    <td>kirpputori</td>
                    <td class="pronunciation">kirp-pu-to-ri</td>
                    <td>flea market</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finland, common grocery store chains include K-Market, S-Market, Lidl, and Prisma. Department stores include Stockmann and Sokos. Many Finns also enjoy shopping at local markets (tori) for fresh produce, especially in summer.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Clothing and Accessories</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>vaatteet</td>
                    <td class="pronunciation">vaat-teet</td>
                    <td>clothes</td>
                </tr>
                <tr>
                    <td>paita</td>
                    <td class="pronunciation">pai-ta</td>
                    <td>shirt</td>
                </tr>
                <tr>
                    <td>t-paita</td>
                    <td class="pronunciation">tee-pai-ta</td>
                    <td>t-shirt</td>
                </tr>
                <tr>
                    <td>pusero</td>
                    <td class="pronunciation">pu-se-ro</td>
                    <td>blouse</td>
                </tr>
                <tr>
                    <td>housut</td>
                    <td class="pronunciation">hou-sut</td>
                    <td>pants, trousers</td>
                </tr>
                <tr>
                    <td>farkut</td>
                    <td class="pronunciation">far-kut</td>
                    <td>jeans</td>
                </tr>
                <tr>
                    <td>hame</td>
                    <td class="pronunciation">ha-me</td>
                    <td>skirt</td>
                </tr>
                <tr>
                    <td>mekko</td>
                    <td class="pronunciation">mek-ko</td>
                    <td>dress</td>
                </tr>
                <tr>
                    <td>takki</td>
                    <td class="pronunciation">tak-ki</td>
                    <td>coat, jacket</td>
                </tr>
                <tr>
                    <td>villapaita</td>
                    <td class="pronunciation">vil-la-pai-ta</td>
                    <td>sweater</td>
                </tr>
                <tr>
                    <td>sukat</td>
                    <td class="pronunciation">su-kat</td>
                    <td>socks</td>
                </tr>
                <tr>
                    <td>kengät</td>
                    <td class="pronunciation">ken-gät</td>
                    <td>shoes</td>
                </tr>
                <tr>
                    <td>saappaat</td>
                    <td class="pronunciation">saap-paat</td>
                    <td>boots</td>
                </tr>
                <tr>
                    <td>laukku</td>
                    <td class="pronunciation">lauk-ku</td>
                    <td>bag</td>
                </tr>
                <tr>
                    <td>käsilaukku</td>
                    <td class="pronunciation">kä-si-lauk-ku</td>
                    <td>handbag</td>
                </tr>
                <tr>
                    <td>vyö</td>
                    <td class="pronunciation">vyö</td>
                    <td>belt</td>
                </tr>
                <tr>
                    <td>koru</td>
                    <td class="pronunciation">ko-ru</td>
                    <td>jewelry</td>
                </tr>
                <tr>
                    <td>kello</td>
                    <td class="pronunciation">kel-lo</td>
                    <td>watch</td>
                </tr>
                <tr>
                    <td>sovittaa</td>
                    <td class="pronunciation">so-vit-taa</td>
                    <td>to try on</td>
                </tr>
                <tr>
                    <td>sovituskoppi</td>
                    <td class="pronunciation">so-vi-tus-kop-pi</td>
                    <td>fitting room</td>
                </tr>
                <tr>
                    <td>koko</td>
                    <td class="pronunciation">ko-ko</td>
                    <td>size</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>In a clothing store:</strong></p>
                <p><strong>A:</strong> Voinko sovittaa tätä paitaa?</p>
                <p><strong>B:</strong> Tietenkin. Sovituskopit ovat tuolla.</p>
                <p><strong>A:</strong> Onko teillä tätä isommassa koossa?</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Can I try on this shirt?</p>
                <p><strong>B:</strong> Of course. The fitting rooms are over there.</p>
                <p><strong>A:</strong> Do you have this in a larger size?</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Grocery Shopping</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>ruokaosasto</td>
                    <td class="pronunciation">ruo-ka-o-sas-to</td>
                    <td>food section</td>
                </tr>
                <tr>
                    <td>hedelmät ja vihannekset</td>
                    <td class="pronunciation">he-del-mät ja vi-han-nek-set</td>
                    <td>fruits and vegetables</td>
                </tr>
                <tr>
                    <td>lihaosasto</td>
                    <td class="pronunciation">li-ha-o-sas-to</td>
                    <td>meat section</td>
                </tr>
                <tr>
                    <td>kalaosasto</td>
                    <td class="pronunciation">ka-la-o-sas-to</td>
                    <td>fish section</td>
                </tr>
                <tr>
                    <td>maitotuotteet</td>
                    <td class="pronunciation">mai-to-tuot-teet</td>
                    <td>dairy products</td>
                </tr>
                <tr>
                    <td>leipäosasto</td>
                    <td class="pronunciation">lei-pä-o-sas-to</td>
                    <td>bread section</td>
                </tr>
                <tr>
                    <td>pakasteet</td>
                    <td class="pronunciation">pa-kas-teet</td>
                    <td>frozen foods</td>
                </tr>
                <tr>
                    <td>juomat</td>
                    <td class="pronunciation">juo-mat</td>
                    <td>drinks</td>
                </tr>
                <tr>
                    <td>mausteet</td>
                    <td class="pronunciation">maus-teet</td>
                    <td>spices</td>
                </tr>
                <tr>
                    <td>säilykkeet</td>
                    <td class="pronunciation">säi-lyk-keet</td>
                    <td>canned goods</td>
                </tr>
                <tr>
                    <td>kilo</td>
                    <td class="pronunciation">ki-lo</td>
                    <td>kilogram</td>
                </tr>
                <tr>
                    <td>gramma</td>
                    <td class="pronunciation">gram-ma</td>
                    <td>gram</td>
                </tr>
                <tr>
                    <td>litra</td>
                    <td class="pronunciation">lit-ra</td>
                    <td>liter</td>
                </tr>
                <tr>
                    <td>pullo</td>
                    <td class="pronunciation">pul-lo</td>
                    <td>bottle</td>
                </tr>
                <tr>
                    <td>tölkki</td>
                    <td class="pronunciation">tölk-ki</td>
                    <td>can</td>
                </tr>
                <tr>
                    <td>paketti</td>
                    <td class="pronunciation">pa-ket-ti</td>
                    <td>package</td>
                </tr>
                <tr>
                    <td>pussi</td>
                    <td class="pronunciation">pus-si</td>
                    <td>bag</td>
                </tr>
                <tr>
                    <td>muovikassi</td>
                    <td class="pronunciation">muo-vi-kas-si</td>
                    <td>plastic bag</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finnish grocery stores, you often need to weigh your own fruits and vegetables and print a price tag. Also, many Finns bring their own reusable bags to reduce plastic waste.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Payment Methods</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>käteinen</td>
                    <td class="pronunciation">kä-tei-nen</td>
                    <td>cash</td>
                </tr>
                <tr>
                    <td>pankkikortti</td>
                    <td class="pronunciation">pank-ki-kort-ti</td>
                    <td>debit card</td>
                </tr>
                <tr>
                    <td>luottokortti</td>
                    <td class="pronunciation">luot-to-kort-ti</td>
                    <td>credit card</td>
                </tr>
                <tr>
                    <td>maksaa kortilla</td>
                    <td class="pronunciation">mak-saa kor-til-la</td>
                    <td>to pay by card</td>
                </tr>
                <tr>
                    <td>maksaa käteisellä</td>
                    <td class="pronunciation">mak-saa kä-tei-sel-lä</td>
                    <td>to pay in cash</td>
                </tr>
                <tr>
                    <td>PIN-koodi</td>
                    <td class="pronunciation">pin-koo-di</td>
                    <td>PIN code</td>
                </tr>
                <tr>
                    <td>lähimaksu</td>
                    <td class="pronunciation">lä-hi-mak-su</td>
                    <td>contactless payment</td>
                </tr>
                <tr>
                    <td>mobiilimaksu</td>
                    <td class="pronunciation">mo-bii-li-mak-su</td>
                    <td>mobile payment</td>
                </tr>
                <tr>
                    <td>verkkokauppa</td>
                    <td class="pronunciation">verk-ko-kaup-pa</td>
                    <td>online store</td>
                </tr>
                <tr>
                    <td>verkkopankki</td>
                    <td class="pronunciation">verk-ko-pank-ki</td>
                    <td>online banking</td>
                </tr>
                <tr>
                    <td>lasku</td>
                    <td class="pronunciation">las-ku</td>
                    <td>bill, invoice</td>
                </tr>
                <tr>
                    <td>vaihtorahat</td>
                    <td class="pronunciation">vaih-to-ra-hat</td>
                    <td>change (money)</td>
                </tr>
                <tr>
                    <td>euro</td>
                    <td class="pronunciation">eu-ro</td>
                    <td>euro</td>
                </tr>
                <tr>
                    <td>sentti</td>
                    <td class="pronunciation">sent-ti</td>
                    <td>cent</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>At the checkout:</strong></p>
                <p><strong>Myyjä:</strong> Se tekee 24 euroa ja 50 senttiä.</p>
                <p><strong>Asiakas:</strong> Maksaisin kortilla.</p>
                <p><strong>Myyjä:</strong> Sopii. Ole hyvä ja syötä PIN-koodi.</p>
                <p><em>Translation:</em></p>
                <p><strong>Cashier:</strong> That will be 24 euros and 50 cents.</p>
                <p><strong>Customer:</strong> I would like to pay by card.</p>
                <p><strong>Cashier:</strong> That's fine. Please enter your PIN code.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Useful Shopping Phrases</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Voinko auttaa?</td>
                    <td>Can I help you?</td>
                </tr>
                <tr>
                    <td>Etsin...</td>
                    <td>I'm looking for...</td>
                </tr>
                <tr>
                    <td>Paljonko tämä maksaa?</td>
                    <td>How much does this cost?</td>
                </tr>
                <tr>
                    <td>Onko teillä...?</td>
                    <td>Do you have...?</td>
                </tr>
                <tr>
                    <td>Missä on...?</td>
                    <td>Where is...?</td>
                </tr>
                <tr>
                    <td>Otan tämän.</td>
                    <td>I'll take this.</td>
                </tr>
                <tr>
                    <td>Voinko sovittaa tätä?</td>
                    <td>Can I try this on?</td>
                </tr>
                <tr>
                    <td>Onko tätä muissa väreissä?</td>
                    <td>Do you have this in other colors?</td>
                </tr>
                <tr>
                    <td>Onko tätä isommassa/pienemmässä koossa?</td>
                    <td>Do you have this in a larger/smaller size?</td>
                </tr>
                <tr>
                    <td>Tarvitsen kuittia.</td>
                    <td>I need a receipt.</td>
                </tr>
                <tr>
                    <td>Haluaisin palauttaa tämän.</td>
                    <td>I would like to return this.</td>
                </tr>
                <tr>
                    <td>Haluaisin vaihtaa tämän.</td>
                    <td>I would like to exchange this.</td>
                </tr>
                <tr>
                    <td>Onko alennusta?</td>
                    <td>Is there a discount?</td>
                </tr>
                <tr>
                    <td>Tarvitsenko ostoskassia?</td>
                    <td>Do I need a shopping bag?</td>
                </tr>
                <tr>
                    <td>Kiitos, vain katselen.</td>
                    <td>Thank you, I'm just looking.</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















