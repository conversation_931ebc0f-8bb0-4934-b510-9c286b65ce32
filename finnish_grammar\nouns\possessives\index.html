<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Possessive Suffixes - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Nouns</a>
            <span class="separator">></span>
            <span>Possessive Suffixes</span>
        </div>
        
        <section class="grammar-section">
            <h2>Possessive Suffixes in Finnish</h2>
            <p>Finnish uses possessive suffixes (possessiivisuffiksit) to indicate ownership or possession. Unlike many other languages that use separate possessive pronouns, Finnish attaches these suffixes directly to the noun. This page explains how to form and use possessive suffixes in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>THE POSSESSIVE SUFFIXES</h3>
            
            <div class="grammar-content">
                <p>Here are the possessive suffixes in Finnish:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>1st person</td>
                        <td>-ni (my)</td>
                        <td>-mme (our)</td>
                    </tr>
                    <tr>
                        <td>2nd person</td>
                        <td>-si (your)</td>
                        <td>-nne (your, plural)</td>
                    </tr>
                    <tr>
                        <td>3rd person</td>
                        <td>-nsa/-nsä (his/her/its)</td>
                        <td>-nsa/-nsä (their)</td>
                    </tr>
                </table>
                
                <p>Note that the 3rd person suffix is the same for both singular and plural, and it follows vowel harmony (-nsa for back vowel words, -nsä for front vowel words).</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ATTACHING POSSESSIVE SUFFIXES</h3>
            
            <div class="grammar-content">
                <p>Possessive suffixes are attached to the end of the noun, after any case endings:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">talo (house) + -ni → taloni</span> <span class="english">my house</span></p>
                    <p><span class="finnish">kirja (book) + -si → kirjasi</span> <span class="english">your book</span></p>
                    <p><span class="finnish">auto (car) + -nsa → autonsa</span> <span class="english">his/her/their car</span></p>
                </div>
                
                <p>When a possessive suffix is added to a noun in a case other than the nominative, the suffix comes after the case ending:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">talo (house) + -ssa (inessive) + -ni → talossani</span> <span class="english">in my house</span></p>
                    <p><span class="finnish">kirja (book) + -lla (adessive) + -si → kirjallasi</span> <span class="english">on your book</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CHANGES WHEN ADDING POSSESSIVE SUFFIXES</h3>
            
            <div class="grammar-content">
                <p>When adding possessive suffixes, some changes may occur in the word:</p>
                
                <h4>1. Genitive case and possessive suffixes</h4>
                <p>When using a possessive suffix with the genitive case, the final -n of the genitive is dropped:</p>
                <div class="grammar-example">
                    <p><span class="finnish">talon (of the house) + -ni → taloni</span> <span class="english">my house</span></p>
                    <p><span class="finnish">kirjan (of the book) + -si → kirjasi</span> <span class="english">your book</span></p>
                </div>
                
                <h4>2. Partitive case and possessive suffixes</h4>
                <p>When using a possessive suffix with the partitive case, the final vowel of the partitive is retained:</p>
                <div class="grammar-example">
                    <p><span class="finnish">taloa (house, partitive) + -ni → taloani</span> <span class="english">my house (partitive)</span></p>
                    <p><span class="finnish">kirjaa (book, partitive) + -si → kirjaasi</span> <span class="english">your book (partitive)</span></p>
                </div>
                
                <h4>3. Consonant gradation</h4>
                <p>Possessive suffixes can affect consonant gradation in some words:</p>
                <div class="grammar-example">
                    <p><span class="finnish">käsi (hand) → käteni</span> <span class="english">my hand</span></p>
                    <p><span class="finnish">vesi (water) → vetesi</span> <span class="english">your water</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF POSSESSIVE SUFFIXES</h3>
            
            <div class="grammar-content">
                <p>Possessive suffixes are used in the following situations:</p>
                
                <h4>1. To indicate possession</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Taloni on suuri.</span> <span class="english">My house is big.</span></p>
                    <p><span class="finnish">Autosi on punainen.</span> <span class="english">Your car is red.</span></p>
                </div>
                
                <h4>2. With personal pronouns</h4>
                <p>In formal or emphatic speech, possessive suffixes can be used together with the genitive form of personal pronouns:</p>
                <div class="grammar-example">
                    <p><span class="finnish">minun taloni</span> <span class="english">my house (emphatic)</span></p>
                    <p><span class="finnish">sinun kirjasi</span> <span class="english">your book (emphatic)</span></p>
                </div>
                
                <p>In colloquial speech, the personal pronoun is often used without the possessive suffix:</p>
                <div class="grammar-example">
                    <p><span class="finnish">minun talo</span> <span class="english">my house (colloquial)</span></p>
                    <p><span class="finnish">sun kirja</span> <span class="english">your book (colloquial)</span></p>
                </div>
                
                <h4>3. With reflexive pronouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">itseni</span> <span class="english">myself</span></p>
                    <p><span class="finnish">itsesi</span> <span class="english">yourself</span></p>
                </div>
                
                <h4>4. With postpositions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">edessäni</span> <span class="english">in front of me</span></p>
                    <p><span class="finnish">kanssasi</span> <span class="english">with you</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES WITH DIFFERENT NOUN TYPES</h3>
            
            <div class="grammar-content">
                <p>Here are examples of possessive suffixes with different noun types:</p>
                
                <h4>Type 1 (-i) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">kieli (language) → kieleni</span> <span class="english">my language</span></p>
                    <p><span class="finnish">tuoli (chair) → tuolisi</span> <span class="english">your chair</span></p>
                </div>
                
                <h4>Type 2 (-e) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">perhe (family) → perheeni</span> <span class="english">my family</span></p>
                    <p><span class="finnish">huone (room) → huoneesi</span> <span class="english">your room</span></p>
                </div>
                
                <h4>Type 4 (-a/-ä) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">kala (fish) → kalani</span> <span class="english">my fish</span></p>
                    <p><span class="finnish">päivä (day) → päiväsi</span> <span class="english">your day</span></p>
                </div>
                
                <h4>Type 5 (-o/-ö/-u/-y) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">auto (car) → autoni</span> <span class="english">my car</span></p>
                    <p><span class="finnish">pöytä (table) → pöytäsi</span> <span class="english">your table</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









