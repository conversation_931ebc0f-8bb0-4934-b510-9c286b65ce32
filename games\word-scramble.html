﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>San<PERSON><PERSON> (Word Game) - Opiskelen <PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../styles.css">
    <style>
        /* Navigation container styles */
        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            height: 60px;
            position: relative;
        }
        
        /* Mobile menu toggle - always defined */
        .mobile-menu-toggle {
            display: none; /* Hidden by default on desktop */
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: transparent;
            border: none;
            cursor: pointer;
            z-index: 1001;
            position: relative;
            padding: 8px;
            margin-left: auto;
        }
        
        /* Mobile menu specific styles */
        @media (max-width: 767px) {
            /* Ensure mobile menu toggle is visible and clickable */
            .mobile-menu-toggle {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 40px !important;
                height: 40px !important;
                background-color: transparent !important;
                border: none !important;
                cursor: pointer !important;
                z-index: 1001 !important; /* Higher than the menu */
                position: relative !important;
                padding: 8px !important;
                margin-left: auto !important; /* Push to the right */
            }
            
            .mobile-menu-toggle i {
                font-size: 1.5rem !important;
                color: var(--primary-color) !important;
            }
            
            .dark-mode .mobile-menu-toggle i {
                color: #fff !important;
            }
            
            .mobile-menu-toggle:hover, 
            .mobile-menu-toggle:focus {
                background-color: rgba(0, 0, 0, 0.05) !important;
                outline: none !important;
            }
            
            .dark-mode .mobile-menu-toggle:hover,
            .dark-mode .mobile-menu-toggle:focus {
                background-color: rgba(255, 255, 255, 0.1) !important;
            }
            
            .mobile-menu-toggle.active {
                background-color: rgba(0, 0, 0, 0.1) !important;
            }
            
            .dark-mode .mobile-menu-toggle.active {
                background-color: rgba(255, 255, 255, 0.15) !important;
            }
            
            /* Menu styles - using direct style manipulation for better control */
            .nav-links {
                display: flex; /* Show by default on larger screens */
            }
            
            /* Only hide on mobile */
            @media (max-width: 767px) {
                .nav-links {
                    display: none !important; /* Hide by default on mobile */
                    visibility: hidden !important;
                    opacity: 0 !important;
                    transition: none !important;
                }
            }
            
            /* Explicitly override any conflicting styles */
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
                visibility: visible !important;
                opacity: 1 !important;
                height: auto !important;
                overflow: visible !important;
                transform: none !important;
                pointer-events: auto !important;
            }
            
            .dark-mode .nav-links.show {
                background-color: #252525 !important;
            }
            
            /* Dropdown styles for mobile */
            .dropdown-content {
                display: none !important;
            }
            
            .dropdown.active .dropdown-content {
                display: block !important;
                position: static !important;
                width: 100% !important;
                background-color: rgba(0, 0, 0, 0.03) !important;
                box-shadow: none !important;
                border-top: 1px solid #eee !important;
            }
            
            .dark-mode .dropdown.active .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05) !important;
                border-top: 1px solid #333 !important;
            }
        }
        
        /* Game specific styles */
        .game-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .dark-mode .game-container {
            background-color: #1e1e1e;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .game-top-row {
            width: 100%;
            margin-bottom: 0px;
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .dark-mode .game-top-row {
            background-color: #252525;
        }
        
        .control-row {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .game-title {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .game-stats {
            display: flex;
            gap: 15px;
            flex: 1;
            flex-wrap: wrap;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stat-label {
            font-weight: bold;
            font-size: 0.9rem;
            color: #666;
        }
        
        .dark-mode .stat-label {
            color: #aaa;
        }
        
        .stat-value {
            font-size: 1rem;
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .game-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .game-button, .back-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            min-width: 120px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .game-button:hover, .back-button:hover {
            background-color: #002a66;
            transform: translateY(-2px);
        }
        
        .game-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .dark-mode .game-button:disabled {
            background-color: #555555;
        }
        
        .game-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .option-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .option-group label {
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .game-select {
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #fff;
            font-size: 0.9rem;
            cursor: pointer;
        }
        
        .dark-mode .game-select {
            background-color: #333;
            border-color: #444;
            color: #fff;
        }
        
        /* Word Scramble specific styles */
        .word-scramble-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin-top: 20px;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
        }
        
        .dark-mode .word-scramble-area {
            background-color: #2a2a2a;
        }
        
        .scrambled-word {
            font-size: 2.5rem;
            font-weight: bold;
            letter-spacing: 5px;
            color: var(--primary-color);
            text-align: center;
            margin: 0;
            user-select: none;
        }
        
        .multiple-choice-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
            max-width: 600px;
        }
        
        .options-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            width: 100%;
        }
        
        /* Difficulty-specific grid layouts */
        .options-container.easy {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .options-container.medium {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .options-container.hard {
            grid-template-columns: repeat(4, 1fr);
        }
        
        @media (max-width: 768px) {
            .options-container.medium,
            .options-container.hard {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        .option-button {
            padding: 15px;
            font-size: 1.1rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            text-align: center;
            background-color: #fff;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .option-button:hover:not(:disabled) {
            background-color: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .option-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .dark-mode .option-button {
            background-color: #333;
            border-color: #444;
            color: #fff;
        }
        
        .dark-mode .option-button:hover:not(:disabled) {
            background-color: #444;
        }
        
        .option-button.correct {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        
        .option-button.incorrect {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        
        .dark-mode .option-button.correct {
            background-color: #5cb85c;
            border-color: #5cb85c;
        }
        
        .dark-mode .option-button.incorrect {
            background-color: #d9534f;
            border-color: #d9534f;
        }
        
        /* Notification overlay */
        .notification-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .notification-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .notification-content {
            background-color: #fff;
            padding: 20px 40px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            max-width: 80%;
            transform: scale(0.8);
            transition: all 0.3s ease;
        }
        
        .notification-overlay.show .notification-content {
            transform: scale(1);
        }
        
        .dark-mode .notification-content {
            background-color: #252525;
            color: #fff;
        }
        
        #notification-message {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0;
        }
        
        #notification-message.correct {
            color: #28a745;
        }
        
        #notification-message.incorrect {
            color: #dc3545;
        }
        
        .dark-mode #notification-message.correct {
            color: #5cb85c;
        }
        
        .dark-mode #notification-message.incorrect {
            color: #d9534f;
        }
        
        .hint-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .hint-text {
            font-style: italic;
            color: #666;
            text-align: center;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .dark-mode .hint-text {
            color: #aaa;
        }
        
        .result-message {
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            min-height: 30px;
            transition: all 0.3s ease;
        }
        
        .result-message.correct {
            color: #28a745;
        }
        
        .result-message.incorrect {
            color: #dc3545;
        }
        
        .dark-mode .result-message.correct {
            color: #5cb85c;
        }
        
        .dark-mode .result-message.incorrect {
            color: #d9534f;
        }
        
        .progress-container {
            width: 100%;
            max-width: 400px;
            height: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .dark-mode .progress-container {
            background-color: #444;
        }
        
        .progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        
        .stats-timer-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            max-width: 600px;
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 8px;
        }
        
        .dark-mode .stats-timer-container {
            background-color: #333;
        }
        
        .timer-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .timer-icon {
            color: var(--primary-color);
        }
        
        .timer-text {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
        }
        
        .dark-mode .timer-text {
            color: #eee;
        }
        
        /* Responsive styles */
        @media (max-width: 767px) {
            .game-container {
                padding: 15px;
            }
            
            .game-top-row {
                padding: 10px;
                gap: 10px;
            }
            
            .control-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .game-title {
                font-size: 1.3rem;
                width: 100%;
                text-align: center;
            }
            
            .game-stats {
                width: 100%;
                justify-content: space-between;
            }
            
            .game-options {
                width: 100%;
                justify-content: space-between;
            }
            
            .game-actions {
                width: 100%;
                justify-content: space-between;
            }
            
            .scrambled-word {
                font-size: 2rem;
                letter-spacing: 3px;
            }
            
            .word-input {
                font-size: 1.1rem;
            }
        }
        
        /* Game completion modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active .modal-content {
            transform: translateY(0);
        }
        
        .dark-mode .modal-content {
            background-color: #252525;
            color: #fff;
        }
        
        .modal-title {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .modal-stats {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 20px 0;
        }
        
        .modal-stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .dark-mode .modal-stat-item {
            border-bottom: 1px solid #444;
        }
        
        .modal-stat-label {
            font-weight: bold;
        }
        
        .modal-stat-value {
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }
        
        .modal-button {
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .modal-button.primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }
        
        .modal-button.secondary {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .dark-mode .modal-button.secondary {
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
        }
        
        .modal-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .modal-button.primary:hover {
            background-color: #0055aa;
        }
        
        .modal-button.secondary:hover {
            background-color: #e9ecef;
        }
        
        .dark-mode .modal-button.secondary:hover {
            background-color: #444;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="word-scramble-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../index.html">Home</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content">
                        <a href="../video.html?channel=kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../video.html?channel=finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=yleareena">Yle Areena 1</a>
                        <a href="../video.html?channel=yleareena2">Yle Areena 2</a>
                        <a href="../video.html?channel=yleareena3">Yle Areena 3</a>
                        <a href="../video.html?channel=yleareena4">Yle Areena 4</a>
                        <a href="../video.html?channel=yleareena5">Yle Areena 5</a>
                        <a href="../video.html?channel=pipsapossu">Pipsa Possu</a>
                                                <a href="../video.html?channel=katchatsfinnish">KatChats Finnish</a>
                        <a href="../video.html?channel=kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../video.html?channel=kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../video.html?channel=kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../video.html?channel=kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../video.html?channel=ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../video.html?channel=ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../video.html?channel=ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../video.html?channel=ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../video.html?channel=ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../video.html?channel=ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../audio.html">Audio</a></li>
                <li><a href="../finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../index.html#daily-life">Daily Life</a>
                        <a href="../index.html#web-development">Web Development</a>
                        <a href="../index.html#cleaner">Cleaner</a>
                        <a href="../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="word-scramble-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <div class="game-container">
            <div class="game-top-row">
                <div class="control-row">
                    <h2 class="game-title">Sanapeli (Word Game)</h2>
                </div>
                <div class="control-row">
                    <div class="game-options">
                        <div class="option-group">
                            <label for="language-mode">Mode:</label>
                            <select id="language-mode" class="game-select">
                                <option value="finnish-to-english">Finnish â†’ English</option>
                                <option value="english-to-finnish">English â†’ Finnish</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label for="difficulty">Difficulty:</label>
                            <select id="difficulty" class="game-select">
                                <option value="easy">Easy</option>
                                <option value="medium">Medium</option>
                                <option value="hard">Hard</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label for="category">Category:</label>
                            <select id="category" class="game-select">
                                <option value="all">All Words</option>
                                <option value="greetings">Greetings</option>
                                <option value="family">Family</option>
                                <option value="food">Food</option>
                                <option value="colors">Colors</option>
                                <option value="numbers">Numbers</option>
                                <option value="time">Time</option>
                            </select>
                        </div>
                    </div>
                    <div class="game-actions">
                        <button id="new-game" class="game-button">New Game</button>
                        <button id="hint-button" class="game-button" disabled>Hint</button>
                        <a href="../games.html" class="back-button">Back to Games</a>
                    </div>
                </div>
            </div>
            
            <div class="word-scramble-area">

                <div class="stats-timer-container">
                    <div class="stat-item">
                        <span class="stat-label">Score:</span>
                        <span class="stat-value" id="score-display">0</span>
                    </div>
                    
                    <div class="timer-container">
                        <i class="fas fa-clock timer-icon"></i>
                        <span class="timer-text" id="timer">00:00</span>
                    </div>
                    
                    <div class="stat-item">
                        <span class="stat-label">Words:</span>
                        <span class="stat-value"><span id="current-word-display">0</span>/<span id="total-words-display">10</span></span>
                    </div>
                </div>
                
                <h3 class="scrambled-word" id="scrambled-word">PRESS NEW GAME TO START</h3>
                
                <div class="multiple-choice-area">
                    <div class="options-container" id="options-container">
                        <!-- Options will be dynamically generated based on difficulty -->
                    </div>
                </div>
                
                <div class="hint-area">
                    <p class="hint-text" id="hint-text"></p>
                </div>
                
                <div class="result-message" id="result-message"></div>
                
                <!-- Notification overlay for showing results -->
                <div class="notification-overlay" id="notification-overlay">
                    <div class="notification-content">
                        <p id="notification-message"></p>
                    </div>
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game Completion Modal -->
    <div class="modal-overlay" id="completion-modal">
        <div class="modal-content">
            <h2 class="modal-title">Game Completed!</h2>
            <p>Congratulations! You've completed the word scramble game.</p>
            
            <div class="modal-stats">
                <div class="modal-stat-item">
                    <span class="modal-stat-label">Final Score:</span>
                    <span class="modal-stat-value" id="final-score">0</span>
                </div>
                <div class="modal-stat-item">
                    <span class="modal-stat-label">Time Taken:</span>
                    <span class="modal-stat-value" id="final-time">00:00</span>
                </div>
                <div class="modal-stat-item">
                    <span class="modal-stat-label">Correct Words:</span>
                    <span class="modal-stat-value" id="correct-words">0/10</span>
                </div>
                <div class="modal-stat-item">
                    <span class="modal-stat-label">Hints Used:</span>
                    <span class="modal-stat-value" id="hints-used">0</span>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button id="play-again" class="modal-button primary">Play Again</button>
                <a href="../games.html" class="modal-button secondary">Back to Games</a>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Opiskelen Suomea. All rights reserved.</p>
        </div>
    </footer>

    <script src="../script.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            console.log("Word Scramble - DOM loaded");
            
            // Check if mobile menu toggle exists
            const mobileMenuToggleCheck = document.getElementById("mobile-menu-toggle");
            console.log("Word Scramble - Mobile menu toggle exists:", !!mobileMenuToggleCheck);
            if (mobileMenuToggleCheck) {
                console.log("Word Scramble - Mobile menu toggle display:", window.getComputedStyle(mobileMenuToggleCheck).display);
                
                // Add a direct click handler
                mobileMenuToggleCheck.onclick = function(e) {
                    console.log("Word Scramble - Direct click handler fired");
                    e.preventDefault();
                    
                    // Get nav links
                    const navLinks = document.getElementById("nav-links");
                    if (navLinks) {
                        // Toggle show class
                        navLinks.classList.toggle("show");
                        this.classList.toggle("active");
                        
                        // Direct style manipulation
                        if (navLinks.classList.contains("show")) {
                            navLinks.style.cssText = `
                                display: flex !important;
                                flex-direction: column !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                                position: absolute !important;
                                top: 60px !important;
                                left: 0 !important;
                                width: 100% !important;
                                z-index: 1000 !important;
                                background-color: #fff !important;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                                margin: 0 !important;
                                padding: 0 !important;
                                border-top: 1px solid #ddd !important;
                                height: auto !important;
                                overflow: visible !important;
                                transform: none !important;
                                pointer-events: auto !important;
                            `;
                            
                            // Apply dark mode styles if needed
                            if (document.body.classList.contains('dark-mode')) {
                                navLinks.style.backgroundColor = '#252525 !important';
                                navLinks.style.borderTop = '1px solid #333 !important';
                            }
                        } else {
                            navLinks.style.display = "none";
                            navLinks.style.visibility = "hidden";
                            navLinks.style.opacity = "0";
                        }
                        
                        console.log("Word Scramble - Direct handler - Nav links show:", navLinks.classList.contains("show"));
                    }
                    
                    return false;
                };
            }
            // Game elements
            const scrambledWordElement = document.getElementById('scrambled-word');
            const optionsContainer = document.getElementById('options-container');
            let optionButtons = document.querySelectorAll('.option-button'); // Changed to let since we'll reassign this
            const hintButton = document.getElementById('hint-button');
            const newGameButton = document.getElementById('new-game');
            const languageModeSelect = document.getElementById('language-mode');
            const difficultySelect = document.getElementById('difficulty');
            const categorySelect = document.getElementById('category');
            // We only need the display elements now
            const scoreDisplayElement = document.getElementById('score-display');
            const currentWordDisplayElement = document.getElementById('current-word-display');
            const totalWordsDisplayElement = document.getElementById('total-words-display');
            const resultMessageElement = document.getElementById('result-message');
            const hintTextElement = document.getElementById('hint-text');
            const progressBarElement = document.getElementById('progress-bar');
            const timerElement = document.getElementById('timer');
            const notificationOverlay = document.getElementById('notification-overlay');
            const notificationMessage = document.getElementById('notification-message');
            
            // Modal elements
            const completionModal = document.getElementById('completion-modal');
            const finalScoreElement = document.getElementById('final-score');
            const finalTimeElement = document.getElementById('final-time');
            const correctWordsElement = document.getElementById('correct-words');
            const hintsUsedElement = document.getElementById('hints-used');
            const playAgainButton = document.getElementById('play-again');
            
            // Game state variables
            let currentWord = '';
            let scrambledWord = '';
            let score = 0;
            let currentWordIndex = 0;
            let totalWords = 10;
            let gameActive = false;
            let hintsUsed = 0;
            let correctWords = 0;
            let timerInterval;
            let startTime;
            let elapsedTime = 0;
            
            // Multiple choice variables
            let currentOptions = [];
            let correctOptionIndex = -1;
            let wrongAttempts = 0;
            let expectedAnswer = '';
            let gameWords = [];
            
            // Word lists by category
            const wordLists = {
                greetings: [
                    { word: 'hei', hint: 'Hi' },
                    { word: 'moi', hint: 'Hello' },
                    { word: 'terve', hint: 'Hello' },
                    { word: 'nÃ¤hdÃ¤Ã¤n', hint: 'See you' },
                    { word: 'kiitos', hint: 'Thank you' },
                    { word: 'anteeksi', hint: 'Sorry/Excuse me' },
                    { word: 'huomenta', hint: 'Good morning' },
                    { word: 'pÃ¤ivÃ¤Ã¤', hint: 'Good day' },
                    { word: 'iltaa', hint: 'Good evening' },
                    { word: 'nÃ¤kemiin', hint: 'Goodbye (formal)' },
                    { word: 'moikka', hint: 'Hi/Bye (casual)' },
                    { word: 'tervetuloa', hint: 'Welcome' },
                    { word: 'hyvÃ¤Ã¤', hint: 'Good' },
                    { word: 'hauska', hint: 'Nice/Fun' },
                    { word: 'tavata', hint: 'To meet' },
                    { word: 'tutustua', hint: 'To get to know' },
                    { word: 'ystÃ¤vÃ¤', hint: 'Friend' },
                    { word: 'kaveri', hint: 'Buddy/Pal' },
                    { word: 'ole hyvÃ¤', hint: 'Please/You are welcome' },
                    { word: 'kiva', hint: 'Nice' },
                    { word: 'mukava', hint: 'Pleasant' },
                    { word: 'hyvÃ¤Ã¤ pÃ¤ivÃ¤Ã¤', hint: 'Good day' },
                    { word: 'hyvÃ¤Ã¤ iltaa', hint: 'Good evening' },
                    { word: 'hyvÃ¤Ã¤ yÃ¶tÃ¤', hint: 'Good night' },
                    { word: 'hyvÃ¤Ã¤ huomenta', hint: 'Good morning' },
                    { word: 'paljon kiitoksia', hint: 'Thank you very much' },
                    { word: 'kiitti', hint: 'Thanks (casual)' },
                    { word: 'moro', hint: 'Hi/Hello (casual)' },
                    { word: 'heippa', hint: 'Bye (casual)' },
                    { word: 'hei hei', hint: 'Bye bye' },
                    { word: 'kuulemiin', hint: 'Goodbye (until we hear again)' },
                    { word: 'mitÃ¤ kuuluu', hint: 'How are you' },
                    { word: 'hauska tutustua', hint: 'Nice to meet you' },
                    { word: 'hyvÃ¤Ã¤ matkaa', hint: 'Have a good trip' },
                    { word: 'onnea', hint: 'Good luck/Congratulations' },
                    { word: 'hyvÃ¤Ã¤ jatkoa', hint: 'All the best' },
                    { word: 'hyvÃ¤Ã¤ viikonloppua', hint: 'Have a good weekend' },
                    { word: 'hyvÃ¤Ã¤ lomaa', hint: 'Have a good vacation' },
                    { word: 'hyvÃ¤Ã¤ joulua', hint: 'Merry Christmas' },
                    { word: 'hyvÃ¤Ã¤ uutta vuotta', hint: 'Happy New Year' },
                    { word: 'hyvÃ¤Ã¤ pÃ¤Ã¤siÃ¤istÃ¤', hint: 'Happy Easter' },
                    { word: 'hyvÃ¤Ã¤ syntymÃ¤pÃ¤ivÃ¤Ã¤', hint: 'Happy Birthday' },
                    { word: 'onnellista', hint: 'Happy' },
                    { word: 'pahoillani', hint: 'I am sorry' },
                    { word: 'ei se mitÃ¤Ã¤n', hint: 'It\'s nothing/No problem' },
                    { word: 'ei haittaa', hint: 'No problem' },
                    { word: 'kyllÃ¤', hint: 'Yes' },
                    { word: 'joo', hint: 'Yeah' },
                    { word: 'ei', hint: 'No' },
                    { word: 'ehkÃ¤', hint: 'Maybe' },
                    { word: 'totta kai', hint: 'Of course' },
                    { word: 'varmasti', hint: 'Certainly' },
                    { word: 'ehdottomasti', hint: 'Absolutely' },
                    { word: 'mahtavaa', hint: 'Great/Awesome' },
                    { word: 'loistavaa', hint: 'Excellent' },
                    { word: 'hienoa', hint: 'Fine/Great' },
                    { word: 'upeaa', hint: 'Wonderful' },
                    { word: 'ihanaa', hint: 'Lovely' },
                    { word: 'kiitollinen', hint: 'Grateful' },
                    { word: 'onnellinen', hint: 'Happy' },
                    { word: 'iloinen', hint: 'Joyful' },
                    { word: 'surullinen', hint: 'Sad' },
                    { word: 'vihainen', hint: 'Angry' },
                    { word: 'vÃ¤synyt', hint: 'Tired' },
                    { word: 'nÃ¤lkÃ¤inen', hint: 'Hungry' },
                    { word: 'janoinen', hint: 'Thirsty' },
                    { word: 'kyllÃ¤inen', hint: 'Full (after eating)' },
                    { word: 'tyytyvÃ¤inen', hint: 'Satisfied' },
                    { word: 'pettynyt', hint: 'Disappointed' },
                    { word: 'yllÃ¤ttynyt', hint: 'Surprised' },
                    { word: 'innostunut', hint: 'Excited' },
                    { word: 'hermostunut', hint: 'Nervous' },
                    { word: 'rauhallinen', hint: 'Calm' },
                    { word: 'stressaantunut', hint: 'Stressed' },
                    { word: 'rentoutunut', hint: 'Relaxed' },
                    { word: 'huolestunut', hint: 'Worried' },
                    { word: 'helpottunut', hint: 'Relieved' },
                    { word: 'hÃ¤mmentynyt', hint: 'Confused' },
                    { word: 'keskittynyt', hint: 'Focused' },
                    { word: 'hajamielinen', hint: 'Absent-minded' },
                    { word: 'utelias', hint: 'Curious' },
                    { word: 'kiireinen', hint: 'Busy' },
                    { word: 'vapaa', hint: 'Free' },
                    { word: 'valmis', hint: 'Ready' },
                    { word: 'myÃ¶hÃ¤ssÃ¤', hint: 'Late' },
                    { word: 'ajoissa', hint: 'On time' },
                    { word: 'tervehdys', hint: 'Greeting' },
                    { word: 'hyvÃ¤sti', hint: 'Farewell' },
                    { word: 'anteeksianto', hint: 'Forgiveness' },
                    { word: 'ystÃ¤vÃ¤llinen', hint: 'Friendly' },
                    { word: 'kohtelias', hint: 'Polite' },
                    { word: 'epÃ¤kohtelias', hint: 'Impolite' },
                    { word: 'avulias', hint: 'Helpful' },
                    { word: 'huomaavainen', hint: 'Considerate' },
                    { word: 'rehellinen', hint: 'Honest' },
                    { word: 'luotettava', hint: 'Reliable' },
                    { word: 'uskollinen', hint: 'Faithful' },
                    { word: 'rohkea', hint: 'Brave' },
                    { word: 'viisas', hint: 'Wise' },
                    { word: 'Ã¤lykÃ¤s', hint: 'Intelligent' }
                ],
                family: [
                    { word: 'Ã¤iti', hint: 'Mother' },
                    { word: 'isÃ¤', hint: 'Father' },
                    { word: 'veli', hint: 'Brother' },
                    { word: 'sisko', hint: 'Sister' },
                    { word: 'perhe', hint: 'Family' },
                    { word: 'lapsi', hint: 'Child' },
                    { word: 'vauva', hint: 'Baby' },
                    { word: 'mummo', hint: 'Grandmother' },
                    { word: 'ukki', hint: 'Grandfather' },
                    { word: 'tÃ¤ti', hint: 'Aunt' },
                    { word: 'setÃ¤', hint: 'Uncle' },
                    { word: 'serkku', hint: 'Cousin' },
                    { word: 'vanhemmat', hint: 'Parents' },
                    { word: 'isovanhemmat', hint: 'Grandparents' },
                    { word: 'lapsenlapsi', hint: 'Grandchild' },
                    { word: 'sisarukset', hint: 'Siblings' },
                    { word: 'puoliso', hint: 'Spouse' },
                    { word: 'aviomies', hint: 'Husband' },
                    { word: 'vaimo', hint: 'Wife' },
                    { word: 'morsian', hint: 'Bride' },
                    { word: 'sulhanen', hint: 'Groom' },
                    { word: 'kihlattu', hint: 'FiancÃ©/FiancÃ©e' },
                    { word: 'poikaystÃ¤vÃ¤', hint: 'Boyfriend' },
                    { word: 'tyttÃ¶ystÃ¤vÃ¤', hint: 'Girlfriend' },
                    { word: 'kumppani', hint: 'Partner' },
                    { word: 'anoppi', hint: 'Mother-in-law' },
                    { word: 'appi', hint: 'Father-in-law' },
                    { word: 'vÃ¤vy', hint: 'Son-in-law' },
                    { word: 'miniÃ¤', hint: 'Daughter-in-law' },
                    { word: 'lanko', hint: 'Brother-in-law' },
                    { word: 'kÃ¤ly', hint: 'Sister-in-law' },
                    { word: 'poika', hint: 'Son' },
                    { word: 'tytÃ¤r', hint: 'Daughter' },
                    { word: 'esikoinen', hint: 'Firstborn' },
                    { word: 'kuopus', hint: 'Youngest child' },
                    { word: 'kaksonen', hint: 'Twin' },
                    { word: 'kolmoset', hint: 'Triplets' },
                    { word: 'ottolapsi', hint: 'Adopted child' },
                    { word: 'kasvattivanhempi', hint: 'Foster parent' },
                    { word: 'isÃ¤puoli', hint: 'Stepfather' },
                    { word: 'Ã¤itipuoli', hint: 'Stepmother' },
                    { word: 'velipuoli', hint: 'Stepbrother' },
                    { word: 'siskopuoli', hint: 'Stepsister' },
                    { word: 'kummi', hint: 'Godparent' },
                    { word: 'kummilapsi', hint: 'Godchild' },
                    { word: 'sukulainen', hint: 'Relative' },
                    { word: 'sukupolvi', hint: 'Generation' },
                    { word: 'suku', hint: 'Extended family/Kin' },
                    { word: 'sukupuu', hint: 'Family tree' },
                    { word: 'perintÃ¶', hint: 'Inheritance' },
                    { word: 'perinne', hint: 'Tradition' },
                    { word: 'syntymÃ¤', hint: 'Birth' },
                    { word: 'kuolema', hint: 'Death' },
                    { word: 'hÃ¤Ã¤t', hint: 'Wedding' },
                    { word: 'avioliitto', hint: 'Marriage' },
                    { word: 'avioero', hint: 'Divorce' },
                    { word: 'kihlaus', hint: 'Engagement' },
                    { word: 'ristiÃ¤iset', hint: 'Christening' },
                    { word: 'hautajaiset', hint: 'Funeral' },
                    { word: 'syntymÃ¤pÃ¤ivÃ¤', hint: 'Birthday' },
                    { word: 'vuosipÃ¤ivÃ¤', hint: 'Anniversary' },
                    { word: 'sukujuhla', hint: 'Family celebration' },
                    { word: 'perhejuhla', hint: 'Family party' },
                    { word: 'koti', hint: 'Home' },
                    { word: 'kotitalous', hint: 'Household' },
                    { word: 'kotityÃ¶t', hint: 'Housework' },
                    { word: 'lastenhoito', hint: 'Childcare' },
                    { word: 'kasvatus', hint: 'Upbringing' },
                    { word: 'rakkaus', hint: 'Love' },
                    { word: 'huolenpito', hint: 'Care' },
                    { word: 'tuki', hint: 'Support' },
                    { word: 'vastuu', hint: 'Responsibility' },
                    { word: 'kunnioitus', hint: 'Respect' },
                    { word: 'luottamus', hint: 'Trust' },
                    { word: 'riita', hint: 'Argument' },
                    { word: 'sovinto', hint: 'Reconciliation' },
                    { word: 'anteeksianto', hint: 'Forgiveness' },
                    { word: 'ymmÃ¤rrys', hint: 'Understanding' },
                    { word: 'kÃ¤rsivÃ¤llisyys', hint: 'Patience' },
                    { word: 'huumori', hint: 'Humor' },
                    { word: 'ilo', hint: 'Joy' },
                    { word: 'suru', hint: 'Sorrow' },
                    { word: 'muisto', hint: 'Memory' },
                    { word: 'valokuva', hint: 'Photograph' },
                    { word: 'albumi', hint: 'Album' },
                    { word: 'perintÃ¶kalleus', hint: 'Family heirloom' },
                    { word: 'sukuvaakuna', hint: 'Family crest' },
                    { word: 'sukunimi', hint: 'Last name' },
                    { word: 'etunimi', hint: 'First name' },
                    { word: 'lempinimi', hint: 'Nickname' },
                    { word: 'perheneuvonta', hint: 'Family counseling' },
                    { word: 'perheterapia', hint: 'Family therapy' },
                    { word: 'perheside', hint: 'Family bond' },
                    { word: 'perhearvo', hint: 'Family value' },
                    { word: 'perhesalaisuus', hint: 'Family secret' },
                    { word: 'perinnÃ¶llisyys', hint: 'Heredity' },
                    { word: 'geeni', hint: 'Gene' },
                    { word: 'DNA', hint: 'DNA' },
                    { word: 'verisukulainen', hint: 'Blood relative' },
                    { word: 'adoptio', hint: 'Adoption' }
                ],
                food: [
                    { word: 'leipÃ¤', hint: 'Bread' },
                    { word: 'maito', hint: 'Milk' },
                    { word: 'kahvi', hint: 'Coffee' },
                    { word: 'vesi', hint: 'Water' },
                    { word: 'ruoka', hint: 'Food' },
                    { word: 'juoma', hint: 'Drink' },
                    { word: 'kala', hint: 'Fish' },
                    { word: 'liha', hint: 'Meat' },
                    { word: 'juusto', hint: 'Cheese' },
                    { word: 'omena', hint: 'Apple' },
                    { word: 'peruna', hint: 'Potato' },
                    { word: 'pulla', hint: 'Sweet bun' },
                    { word: 'voi', hint: 'Butter' },
                    { word: 'sokeri', hint: 'Sugar' },
                    { word: 'suola', hint: 'Salt' },
                    { word: 'pippuri', hint: 'Pepper' },
                    { word: 'jauhot', hint: 'Flour' },
                    { word: 'riisi', hint: 'Rice' },
                    { word: 'pasta', hint: 'Pasta' },
                    { word: 'kananmuna', hint: 'Egg' },
                    { word: 'kana', hint: 'Chicken' },
                    { word: 'nauta', hint: 'Beef' },
                    { word: 'sika', hint: 'Pork' },
                    { word: 'lohi', hint: 'Salmon' },
                    { word: 'silakka', hint: 'Baltic herring' },
                    { word: 'mustikka', hint: 'Blueberry' },
                    { word: 'mansikka', hint: 'Strawberry' },
                    { word: 'vadelma', hint: 'Raspberry' },
                    { word: 'puolukka', hint: 'Lingonberry' },
                    { word: 'banaani', hint: 'Banana' },
                    { word: 'appelsiini', hint: 'Orange' },
                    { word: 'sitruuna', hint: 'Lemon' },
                    { word: 'porkkana', hint: 'Carrot' },
                    { word: 'tomaatti', hint: 'Tomato' },
                    { word: 'kurkku', hint: 'Cucumber' },
                    { word: 'sipuli', hint: 'Onion' },
                    { word: 'valkosipuli', hint: 'Garlic' },
                    { word: 'salaatti', hint: 'Lettuce/Salad' },
                    { word: 'kaali', hint: 'Cabbage' },
                    { word: 'paprika', hint: 'Bell pepper' },
                    { word: 'sienet', hint: 'Mushrooms' },
                    { word: 'herneet', hint: 'Peas' },
                    { word: 'pavut', hint: 'Beans' },
                    { word: 'linssi', hint: 'Lentil' },
                    { word: 'pÃ¤hkinÃ¤t', hint: 'Nuts' },
                    { word: 'hunaja', hint: 'Honey' },
                    { word: 'hillo', hint: 'Jam' },
                    { word: 'jogurtti', hint: 'Yogurt' },
                    { word: 'kerma', hint: 'Cream' },
                    { word: 'jÃ¤Ã¤telÃ¶', hint: 'Ice cream' },
                    { word: 'suklaa', hint: 'Chocolate' },
                    { word: 'kakku', hint: 'Cake' },
                    { word: 'piirakka', hint: 'Pie' },
                    { word: 'karjalanpiirakka', hint: 'Karelian pie' },
                    { word: 'korvapuusti', hint: 'Cinnamon roll' },
                    { word: 'munkki', hint: 'Donut' },
                    { word: 'lihapiirakka', hint: 'Meat pie' },
                    { word: 'makkara', hint: 'Sausage' },
                    { word: 'nakki', hint: 'Frankfurter' },
                    { word: 'hernekeitto', hint: 'Pea soup' },
                    { word: 'kalakeitto', hint: 'Fish soup' },
                    { word: 'lihakeitto', hint: 'Meat soup' },
                    { word: 'karjalanpaisti', hint: 'Karelian stew' },
                    { word: 'makaronilaatikko', hint: 'Macaroni casserole' },
                    { word: 'maksalaatikko', hint: 'Liver casserole' },
                    { word: 'perunalaatikko', hint: 'Potato casserole' },
                    { word: 'porkkanalaatikko', hint: 'Carrot casserole' },
                    { word: 'lanttulaatikko', hint: 'Rutabaga casserole' },
                    { word: 'kalapuikot', hint: 'Fish fingers' },
                    { word: 'lihapullat', hint: 'Meatballs' },
                    { word: 'muusi', hint: 'Mashed potatoes' },
                    { word: 'ranskalaiset', hint: 'French fries' },
                    { word: 'pizza', hint: 'Pizza' },
                    { word: 'hampurilainen', hint: 'Hamburger' },
                    { word: 'kebab', hint: 'Kebab' },
                    { word: 'sushi', hint: 'Sushi' },
                    { word: 'tee', hint: 'Tea' },
                    { word: 'olut', hint: 'Beer' },
                    { word: 'viini', hint: 'Wine' },
                    { word: 'mehu', hint: 'Juice' },
                    { word: 'limonadi', hint: 'Soda' },
                    { word: 'kivennÃ¤isvesi', hint: 'Mineral water' },
                    { word: 'kaakao', hint: 'Cocoa' },
                    { word: 'mausteet', hint: 'Spices' },
                    { word: 'yrtit', hint: 'Herbs' },
                    { word: 'Ã¶ljy', hint: 'Oil' },
                    { word: 'etikka', hint: 'Vinegar' },
                    { word: 'sinappi', hint: 'Mustard' },
                    { word: 'ketsuppi', hint: 'Ketchup' },
                    { word: 'majoneesi', hint: 'Mayonnaise' },
                    { word: 'kastike', hint: 'Sauce' },
                    { word: 'keitto', hint: 'Soup' },
                    { word: 'salaatinkastike', hint: 'Salad dressing' },
                    { word: 'ateria', hint: 'Meal' },
                    { word: 'aamiainen', hint: 'Breakfast' },
                    { word: 'lounas', hint: 'Lunch' },
                    { word: 'pÃ¤ivÃ¤llinen', hint: 'Dinner' },
                    { word: 'illallinen', hint: 'Supper' },
                    { word: 'vÃ¤lipala', hint: 'Snack' },
                    { word: 'jÃ¤lkiruoka', hint: 'Dessert' },
                    { word: 'alkuruoka', hint: 'Appetizer' },
                    { word: 'pÃ¤Ã¤ruoka', hint: 'Main course' }
                ],
                colors: [
                    { word: 'punainen', hint: 'Red' },
                    { word: 'sininen', hint: 'Blue' },
                    { word: 'keltainen', hint: 'Yellow' },
                    { word: 'vihreÃ¤', hint: 'Green' },
                    { word: 'musta', hint: 'Black' },
                    { word: 'valkoinen', hint: 'White' },
                    { word: 'harmaa', hint: 'Gray' },
                    { word: 'ruskea', hint: 'Brown' },
                    { word: 'oranssi', hint: 'Orange' },
                    { word: 'violetti', hint: 'Purple' },
                    { word: 'pinkki', hint: 'Pink' },
                    { word: 'turkoosi', hint: 'Turquoise' },
                    { word: 'beige', hint: 'Beige' },
                    { word: 'kulta', hint: 'Gold' },
                    { word: 'hopea', hint: 'Silver' },
                    { word: 'pronssi', hint: 'Bronze' },
                    { word: 'vaaleanpunainen', hint: 'Light pink' },
                    { word: 'tummanpunainen', hint: 'Dark red' },
                    { word: 'vaaleansininen', hint: 'Light blue' },
                    { word: 'tummansininen', hint: 'Dark blue' },
                    { word: 'vaaleanvihreÃ¤', hint: 'Light green' },
                    { word: 'tummanvihreÃ¤', hint: 'Dark green' },
                    { word: 'vaaleankeltainen', hint: 'Light yellow' },
                    { word: 'tummankeltainen', hint: 'Dark yellow' },
                    { word: 'vaaleanruskea', hint: 'Light brown' },
                    { word: 'tummanruskea', hint: 'Dark brown' },
                    { word: 'vaaleanharmaa', hint: 'Light gray' },
                    { word: 'tummanharmaa', hint: 'Dark gray' },
                    { word: 'vaaleanvioletti', hint: 'Light purple' },
                    { word: 'tummanvioletti', hint: 'Dark purple' },
                    { word: 'vaaleanturkoosi', hint: 'Light turquoise' },
                    { word: 'tummanturkoosi', hint: 'Dark turquoise' },
                    { word: 'vaaleanpinkki', hint: 'Light pink' },
                    { word: 'tummanpinkki', hint: 'Dark pink' },
                    { word: 'vaaleanoranssi', hint: 'Light orange' },
                    { word: 'tummanoranssi', hint: 'Dark orange' },
                    { word: 'purppura', hint: 'Purple' },
                    { word: 'indigo', hint: 'Indigo' },
                    { word: 'magenta', hint: 'Magenta' },
                    { word: 'syaani', hint: 'Cyan' },
                    { word: 'lila', hint: 'Lilac' },
                    { word: 'persikka', hint: 'Peach' },
                    { word: 'luonnonvalkoinen', hint: 'Off-white' },
                    { word: 'norsunluu', hint: 'Ivory' },
                    { word: 'khaki', hint: 'Khaki' },
                    { word: 'oliivinvihreÃ¤', hint: 'Olive green' },
                    { word: 'smaragdi', hint: 'Emerald' },
                    { word: 'rubiini', hint: 'Ruby' },
                    { word: 'safiiri', hint: 'Sapphire' },
                    { word: 'timantti', hint: 'Diamond' },
                    { word: 'jade', hint: 'Jade' },
                    { word: 'meripihka', hint: 'Amber' },
                    { word: 'koralli', hint: 'Coral' },
                    { word: 'kastanja', hint: 'Chestnut' },
                    { word: 'mahonki', hint: 'Mahogany' },
                    { word: 'okra', hint: 'Ochre' },
                    { word: 'sienna', hint: 'Sienna' },
                    { word: 'terrakotta', hint: 'Terracotta' },
                    { word: 'laventeli', hint: 'Lavender' },
                    { word: 'fuksia', hint: 'Fuchsia' },
                    { word: 'karmiini', hint: 'Carmine' },
                    { word: 'sinivioletti', hint: 'Blue-violet' },
                    { word: 'sinivihreÃ¤', hint: 'Blue-green' },
                    { word: 'keltavihreÃ¤', hint: 'Yellow-green' },
                    { word: 'keltaoranssi', hint: 'Yellow-orange' },
                    { word: 'punavioletti', hint: 'Red-violet' },
                    { word: 'punaoranssi', hint: 'Red-orange' },
                    { word: 'sateenkaari', hint: 'Rainbow' },
                    { word: 'spektri', hint: 'Spectrum' },
                    { word: 'sÃ¤vy', hint: 'Shade/Tone' },
                    { word: 'kirkas', hint: 'Bright' },
                    { word: 'haalea', hint: 'Pale' },
                    { word: 'tumma', hint: 'Dark' },
                    { word: 'vaalea', hint: 'Light' },
                    { word: 'himmeÃ¤', hint: 'Dull' },
                    { word: 'kiiltÃ¤vÃ¤', hint: 'Shiny' },
                    { word: 'metallinen', hint: 'Metallic' },
                    { word: 'lÃ¤pinÃ¤kyvÃ¤', hint: 'Transparent' },
                    { word: 'lÃ¤pikuultava', hint: 'Translucent' },
                    { word: 'samea', hint: 'Opaque' },
                    { word: 'vÃ¤rikÃ¤s', hint: 'Colorful' },
                    { word: 'vÃ¤ritÃ¶n', hint: 'Colorless' },
                    { word: 'monivÃ¤rinen', hint: 'Multicolored' },
                    { word: 'yksivÃ¤rinen', hint: 'Monochrome' },
                    { word: 'kaksivÃ¤rinen', hint: 'Two-colored' },
                    { word: 'kirjava', hint: 'Variegated' },
                    { word: 'raidallinen', hint: 'Striped' },
                    { word: 'pilkullinen', hint: 'Dotted' },
                    { word: 'kuviollinen', hint: 'Patterned' },
                    { word: 'ruudullinen', hint: 'Checkered' },
                    { word: 'vÃ¤rikartta', hint: 'Color chart' },
                    { word: 'vÃ¤riympyrÃ¤', hint: 'Color wheel' },
                    { word: 'vÃ¤ripaletti', hint: 'Color palette' },
                    { word: 'vÃ¤risÃ¤vy', hint: 'Color tone' },
                    { word: 'vÃ¤rikyllÃ¤isyys', hint: 'Color saturation' },
                    { word: 'vÃ¤rikontrasti', hint: 'Color contrast' },
                    { word: 'vÃ¤riharmonia', hint: 'Color harmony' },
                    { word: 'vÃ¤riteoria', hint: 'Color theory' },
                    { word: 'pÃ¤Ã¤vÃ¤ri', hint: 'Primary color' },
                    { word: 'vÃ¤livÃ¤ri', hint: 'Secondary color' },
                    { word: 'vastavÃ¤ri', hint: 'Complementary color' }
                ],
                numbers: [
                    { word: 'yksi', hint: 'One' },
                    { word: 'kaksi', hint: 'Two' },
                    { word: 'kolme', hint: 'Three' },
                    { word: 'neljÃ¤', hint: 'Four' },
                    { word: 'viisi', hint: 'Five' },
                    { word: 'kuusi', hint: 'Six' },
                    { word: 'seitsemÃ¤n', hint: 'Seven' },
                    { word: 'kahdeksan', hint: 'Eight' },
                    { word: 'yhdeksÃ¤n', hint: 'Nine' },
                    { word: 'kymmenen', hint: 'Ten' },
                    { word: 'sata', hint: 'Hundred' },
                    { word: 'tuhat', hint: 'Thousand' },
                    { word: 'nolla', hint: 'Zero' },
                    { word: 'yksitoista', hint: 'Eleven' },
                    { word: 'kaksitoista', hint: 'Twelve' },
                    { word: 'kolmetoista', hint: 'Thirteen' },
                    { word: 'neljÃ¤toista', hint: 'Fourteen' },
                    { word: 'viisitoista', hint: 'Fifteen' },
                    { word: 'kuusitoista', hint: 'Sixteen' },
                    { word: 'seitsemÃ¤ntoista', hint: 'Seventeen' },
                    { word: 'kahdeksantoista', hint: 'Eighteen' },
                    { word: 'yhdeksÃ¤ntoista', hint: 'Nineteen' },
                    { word: 'kaksikymmentÃ¤', hint: 'Twenty' },
                    { word: 'kolmekymmentÃ¤', hint: 'Thirty' },
                    { word: 'neljÃ¤kymmentÃ¤', hint: 'Forty' },
                    { word: 'viisikymmentÃ¤', hint: 'Fifty' },
                    { word: 'kuusikymmentÃ¤', hint: 'Sixty' },
                    { word: 'seitsemÃ¤nkymmentÃ¤', hint: 'Seventy' },
                    { word: 'kahdeksankymmentÃ¤', hint: 'Eighty' },
                    { word: 'yhdeksÃ¤nkymmentÃ¤', hint: 'Ninety' },
                    { word: 'kaksisataa', hint: 'Two hundred' },
                    { word: 'kolmesataa', hint: 'Three hundred' },
                    { word: 'neljÃ¤sataa', hint: 'Four hundred' },
                    { word: 'viisisataa', hint: 'Five hundred' },
                    { word: 'kuusisataa', hint: 'Six hundred' },
                    { word: 'seitsemÃ¤nsataa', hint: 'Seven hundred' },
                    { word: 'kahdeksansataa', hint: 'Eight hundred' },
                    { word: 'yhdeksÃ¤nsataa', hint: 'Nine hundred' },
                    { word: 'kaksituhatta', hint: 'Two thousand' },
                    { word: 'kolmetuhatta', hint: 'Three thousand' },
                    { word: 'miljoona', hint: 'Million' },
                    { word: 'miljardi', hint: 'Billion' },
                    { word: 'ensimmÃ¤inen', hint: 'First' },
                    { word: 'toinen', hint: 'Second' },
                    { word: 'kolmas', hint: 'Third' },
                    { word: 'neljÃ¤s', hint: 'Fourth' },
                    { word: 'viides', hint: 'Fifth' },
                    { word: 'kuudes', hint: 'Sixth' },
                    { word: 'seitsemÃ¤s', hint: 'Seventh' },
                    { word: 'kahdeksas', hint: 'Eighth' },
                    { word: 'yhdeksÃ¤s', hint: 'Ninth' },
                    { word: 'kymmenes', hint: 'Tenth' },
                    { word: 'sadas', hint: 'Hundredth' },
                    { word: 'tuhannes', hint: 'Thousandth' },
                    { word: 'puoli', hint: 'Half' },
                    { word: 'neljÃ¤nnes', hint: 'Quarter' },
                    { word: 'kolmannes', hint: 'Third (fraction)' },
                    { word: 'kymmenesosa', hint: 'Tenth (fraction)' },
                    { word: 'sadasosa', hint: 'Hundredth (fraction)' },
                    { word: 'tuhannesosa', hint: 'Thousandth (fraction)' },
                    { word: 'plus', hint: 'Plus' },
                    { word: 'miinus', hint: 'Minus' },
                    { word: 'kertaa', hint: 'Times (multiplication)' },
                    { word: 'jaettuna', hint: 'Divided by' },
                    { word: 'yhtÃ¤ suuri kuin', hint: 'Equal to' },
                    { word: 'suurempi kuin', hint: 'Greater than' },
                    { word: 'pienempi kuin', hint: 'Less than' },
                    { word: 'prosentti', hint: 'Percent' },
                    { word: 'murtoluku', hint: 'Fraction' },
                    { word: 'desimaaliluku', hint: 'Decimal' },
                    { word: 'kokonaisluku', hint: 'Integer' },
                    { word: 'parillinen', hint: 'Even' },
                    { word: 'pariton', hint: 'Odd' },
                    { word: 'positiivinen', hint: 'Positive' },
                    { word: 'negatiivinen', hint: 'Negative' },
                    { word: 'summa', hint: 'Sum' },
                    { word: 'erotus', hint: 'Difference' },
                    { word: 'tulo', hint: 'Product' },
                    { word: 'osamÃ¤Ã¤rÃ¤', hint: 'Quotient' },
                    { word: 'neliÃ¶', hint: 'Square' },
                    { word: 'kuutio', hint: 'Cube' },
                    { word: 'neliÃ¶juuri', hint: 'Square root' },
                    { word: 'kuutiojuuri', hint: 'Cube root' },
                    { word: 'potenssi', hint: 'Power' },
                    { word: 'eksponentti', hint: 'Exponent' },
                    { word: 'logaritmi', hint: 'Logarithm' },
                    { word: 'sini', hint: 'Sine' },
                    { word: 'kosini', hint: 'Cosine' },
                    { word: 'tangentti', hint: 'Tangent' },
                    { word: 'pii', hint: 'Pi' },
                    { word: 'Ã¤Ã¤retÃ¶n', hint: 'Infinity' },
                    { word: 'noin', hint: 'Approximately' },
                    { word: 'laskea', hint: 'To calculate' },
                    { word: 'laskutoimitus', hint: 'Calculation' },
                    { word: 'yhteenlasku', hint: 'Addition' },
                    { word: 'vÃ¤hennyslasku', hint: 'Subtraction' },
                    { word: 'kertolasku', hint: 'Multiplication' },
                    { word: 'jakolasku', hint: 'Division' },
                    { word: 'yhtÃ¤lÃ¶', hint: 'Equation' },
                    { word: 'kaava', hint: 'Formula' },
                    { word: 'lukujono', hint: 'Number sequence' },
                    { word: 'lukusuora', hint: 'Number line' }
                ],
                time: [
                    { word: 'aika', hint: 'Time' },
                    { word: 'kello', hint: 'Clock' },
                    { word: 'tunti', hint: 'Hour' },
                    { word: 'minuutti', hint: 'Minute' },
                    { word: 'sekunti', hint: 'Second' },
                    { word: 'pÃ¤ivÃ¤', hint: 'Day' },
                    { word: 'viikko', hint: 'Week' },
                    { word: 'kuukausi', hint: 'Month' },
                    { word: 'vuosi', hint: 'Year' },
                    { word: 'eilen', hint: 'Yesterday' },
                    { word: 'tÃ¤nÃ¤Ã¤n', hint: 'Today' },
                    { word: 'huomenna', hint: 'Tomorrow' },
                    { word: 'aamu', hint: 'Morning' },
                    { word: 'aamupÃ¤ivÃ¤', hint: 'Late morning' },
                    { word: 'keskipÃ¤ivÃ¤', hint: 'Noon' },
                    { word: 'iltapÃ¤ivÃ¤', hint: 'Afternoon' },
                    { word: 'ilta', hint: 'Evening' },
                    { word: 'yÃ¶', hint: 'Night' },
                    { word: 'keskiyÃ¶', hint: 'Midnight' },
                    { word: 'maanantai', hint: 'Monday' },
                    { word: 'tiistai', hint: 'Tuesday' },
                    { word: 'keskiviikko', hint: 'Wednesday' },
                    { word: 'torstai', hint: 'Thursday' },
                    { word: 'perjantai', hint: 'Friday' },
                    { word: 'lauantai', hint: 'Saturday' },
                    { word: 'sunnuntai', hint: 'Sunday' },
                    { word: 'viikonloppu', hint: 'Weekend' },
                    { word: 'arkipÃ¤ivÃ¤', hint: 'Weekday' },
                    { word: 'tammikuu', hint: 'January' },
                    { word: 'helmikuu', hint: 'February' },
                    { word: 'maaliskuu', hint: 'March' },
                    { word: 'huhtikuu', hint: 'April' },
                    { word: 'toukokuu', hint: 'May' },
                    { word: 'kesÃ¤kuu', hint: 'June' },
                    { word: 'heinÃ¤kuu', hint: 'July' },
                    { word: 'elokuu', hint: 'August' },
                    { word: 'syyskuu', hint: 'September' },
                    { word: 'lokakuu', hint: 'October' },
                    { word: 'marraskuu', hint: 'November' },
                    { word: 'joulukuu', hint: 'December' },
                    { word: 'kevÃ¤t', hint: 'Spring' },
                    { word: 'kesÃ¤', hint: 'Summer' },
                    { word: 'syksy', hint: 'Autumn' },
                    { word: 'talvi', hint: 'Winter' },
                    { word: 'vuosikymmen', hint: 'Decade' },
                    { word: 'vuosisata', hint: 'Century' },
                    { word: 'vuosituhat', hint: 'Millennium' },
                    { word: 'aikataulu', hint: 'Schedule' },
                    { word: 'kalenteri', hint: 'Calendar' },
                    { word: 'pÃ¤ivÃ¤ys', hint: 'Date' },
                    { word: 'syntymÃ¤pÃ¤ivÃ¤', hint: 'Birthday' },
                    { word: 'vuosipÃ¤ivÃ¤', hint: 'Anniversary' },
                    { word: 'juhlapÃ¤ivÃ¤', hint: 'Holiday' },
                    { word: 'loma', hint: 'Vacation' },
                    { word: 'vapaa-aika', hint: 'Free time' },
                    { word: 'tyÃ¶aika', hint: 'Working hours' },
                    { word: 'aikavyÃ¶hyke', hint: 'Time zone' },
                    { word: 'kesÃ¤aika', hint: 'Daylight saving time' },
                    { word: 'talviaika', hint: 'Standard time' },
                    { word: 'menneisyys', hint: 'Past' },
                    { word: 'nykyisyys', hint: 'Present' },
                    { word: 'tulevaisuus', hint: 'Future' },
                    { word: 'hetki', hint: 'Moment' },
                    { word: 'aikainen', hint: 'Early' },
                    { word: 'myÃ¶hÃ¤inen', hint: 'Late' },
                    { word: 'tÃ¤smÃ¤llinen', hint: 'Punctual' },
                    { word: 'myÃ¶hÃ¤ssÃ¤', hint: 'Delayed' },
                    { word: 'ajoissa', hint: 'On time' },
                    { word: 'heti', hint: 'Immediately' },
                    { word: 'pian', hint: 'Soon' },
                    { word: 'myÃ¶hemmin', hint: 'Later' },
                    { word: 'ennen', hint: 'Before' },
                    { word: 'jÃ¤lkeen', hint: 'After' },
                    { word: 'aikana', hint: 'During' },
                    { word: 'sitten', hint: 'Ago' },
                    { word: 'kulua', hint: 'To pass (time)' },
                    { word: 'kestÃ¤Ã¤', hint: 'To last' },
                    { word: 'viivÃ¤styÃ¤', hint: 'To be delayed' },
                    { word: 'odottaa', hint: 'To wait' },
                    { word: 'kiire', hint: 'Hurry' },
                    { word: 'hidas', hint: 'Slow' },
                    { word: 'nopea', hint: 'Fast' },
                    { word: 'ikuinen', hint: 'Eternal' },
                    { word: 'vÃ¤liaikainen', hint: 'Temporary' },
                    { word: 'pysyvÃ¤', hint: 'Permanent' },
                    { word: 'sÃ¤Ã¤nnÃ¶llinen', hint: 'Regular' },
                    { word: 'epÃ¤sÃ¤Ã¤nnÃ¶llinen', hint: 'Irregular' },
                    { word: 'jatkuva', hint: 'Continuous' },
                    { word: 'ajoittainen', hint: 'Occasional' },
                    { word: 'pÃ¤ivittÃ¤inen', hint: 'Daily' },
                    { word: 'viikoittainen', hint: 'Weekly' },
                    { word: 'kuukausittainen', hint: 'Monthly' },
                    { word: 'vuosittainen', hint: 'Annual' },
                    { word: 'aikakone', hint: 'Time machine' },
                    { word: 'aikajana', hint: 'Timeline' },
                    { word: 'aikakÃ¤sitys', hint: 'Concept of time' },
                    { word: 'ajanhallinta', hint: 'Time management' },
                    { word: 'ajanjakso', hint: 'Period of time' },
                    { word: 'aikakausi', hint: 'Era' },
                    { word: 'sukupolvi', hint: 'Generation' },
                    { word: 'historia', hint: 'History' }
                ]
            };
            
            // Combined word list for "All Words" category
            let allWords = [];
            for (const category in wordLists) {
                allWords = allWords.concat(wordLists[category]);
            }
            
            // Initialize game
            function initGame() {
                // Reset game state
                score = 0;
                currentWordIndex = 0;
                hintsUsed = 0;
                correctWords = 0;
                elapsedTime = 0;
                wrongAttempts = 0;
                
                // Update UI
                scoreDisplayElement.textContent = score;
                currentWordDisplayElement.textContent = currentWordIndex;
                totalWordsDisplayElement.textContent = totalWords;
                resultMessageElement.textContent = '';
                resultMessageElement.className = 'result-message';
                hintTextElement.textContent = '';
                progressBarElement.style.width = '0%';
                timerElement.textContent = '00:00';
                
                // Clear existing options
                optionsContainer.innerHTML = '';
                
                // Enable hint button
                hintButton.disabled = false;
                
                // Start timer
                startTime = Date.now();
                if (timerInterval) clearInterval(timerInterval);
                timerInterval = setInterval(updateTimer, 1000);
                
                // Get words for the selected category
                let selectedCategory = categorySelect.value;
                let wordsForGame = selectedCategory === 'all' ? allWords : wordLists[selectedCategory];
                
                // Shuffle and select words based on total words
                wordsForGame = shuffleArray(wordsForGame).slice(0, totalWords);
                
                // Normalize words to ensure consistent capitalization
                wordsForGame = wordsForGame.map(wordObj => {
                    // Create a new object to avoid modifying the original
                    return {
                        word: wordObj.word.trim(), // Trim whitespace
                        hint: wordObj.hint
                    };
                });
                
                console.log("Selected words for game:", wordsForGame);
                
                // Store selected words
                gameWords = wordsForGame;
                
                // Start with the first word
                nextWord();
                
                // Set game as active
                gameActive = true;
            }
            
            // Get the next word
            function nextWord() {
                if (currentWordIndex >= totalWords) {
                    endGame();
                    return;
                }
                
                // Reset wrong attempts for new word
                wrongAttempts = 0;
                
                // Get current word
                const wordObj = gameWords[currentWordIndex];
                currentWord = wordObj.word.trim(); // Ensure no whitespace
                
                console.log("Current word set to:", currentWord);
                
                // Scramble the word based on difficulty
                scrambledWord = scrambleWord(currentWord, difficultySelect.value);
                
                // Update UI
                scrambledWordElement.textContent = scrambledWord;
                hintTextElement.textContent = '';
                resultMessageElement.textContent = '';
                resultMessageElement.className = 'result-message';
                currentWordDisplayElement.textContent = currentWordIndex + 1;
                
                // Update progress bar
                const progress = (currentWordIndex / totalWords) * 100;
                progressBarElement.style.width = `${progress}%`;
                
                // Generate multiple choice options
                generateMultipleChoiceOptions();
                
                // Re-enable hint button for new word
                hintButton.disabled = false;
            }
            
            // Generate multiple choice options
            function generateMultipleChoiceOptions() {
                // Determine which answer is expected based on language mode
                const languageMode = languageModeSelect.value;
                const difficulty = difficultySelect.value;
                const wordObj = gameWords[currentWordIndex];
                
                if (languageMode === 'finnish-to-english') {
                    // Finnish word is displayed, expect English answer
                    expectedAnswer = wordObj.hint.toLowerCase();
                } else {
                    // English word is displayed, expect Finnish answer
                    expectedAnswer = currentWord.toLowerCase();
                }
                
                // Determine number of options based on difficulty
                let numOptions;
                switch (difficulty) {
                    case 'easy':
                        numOptions = 4;
                        break;
                    case 'medium':
                        numOptions = 6;
                        break;
                    case 'hard':
                        numOptions = 8;
                        break;
                    default:
                        numOptions = 4;
                }
                
                // Get random wrong options from other words
                let allPossibleOptions = [];
                
                // Get all possible options based on language mode
                for (const category in wordLists) {
                    wordLists[category].forEach(word => {
                        const option = languageMode === 'finnish-to-english' ? 
                            word.hint.toLowerCase() : word.word.toLowerCase();
                        
                        // Don't include the correct answer
                        if (option !== expectedAnswer) {
                            allPossibleOptions.push(option);
                        }
                    });
                }
                
                // Shuffle and select wrong options
                allPossibleOptions = shuffleArray(allPossibleOptions);
                const wrongOptions = allPossibleOptions.slice(0, numOptions - 1);
                
                // Combine with correct answer and shuffle
                currentOptions = [...wrongOptions, expectedAnswer];
                currentOptions = shuffleArray(currentOptions);
                
                // Find index of correct answer
                correctOptionIndex = currentOptions.indexOf(expectedAnswer);
                
                // Clear existing options
                optionsContainer.innerHTML = '';
                optionsContainer.className = 'options-container ' + difficulty;
                
                // Create new option buttons
                for (let i = 0; i < numOptions; i++) {
                    const button = document.createElement('button');
                    button.className = 'option-button';
                    button.setAttribute('data-index', i);
                    button.textContent = currentOptions[i];
                    
                    // Add event listener
                    button.addEventListener('click', function() {
                        if (!this.disabled && gameActive) {
                            checkAnswer(i);
                        }
                    });
                    
                    optionsContainer.appendChild(button);
                }
                
                // Update optionButtons reference
                optionButtons = document.querySelectorAll('.option-button');
            }
            
            // Display the word based on language mode
            function scrambleWord(word, difficulty) {
                // Normalize the input word
                word = word.trim();
                
                // Get the current word object to access the hint (English translation)
                const wordObj = gameWords[currentWordIndex];
                const englishTranslation = wordObj.hint;
                
                // Determine which word to display based on language mode
                const languageMode = languageModeSelect.value;
                let displayWord;
                
                if (languageMode === 'finnish-to-english') {
                    // Show Finnish word, user will type English
                    displayWord = word;
                    console.log("Displaying Finnish word:", displayWord, "- User should type English:", englishTranslation);
                } else {
                    // Show English word, user will type Finnish
                    displayWord = englishTranslation;
                    console.log("Displaying English word:", displayWord, "- User should type Finnish:", word);
                }
                
                // Return the word in uppercase without any scrambling
                return displayWord.toUpperCase();
            }
            
            // Check the user's answer based on selected option
            function checkAnswer(selectedIndex) {
                if (!gameActive) return;
                
                const selectedOption = currentOptions[selectedIndex];
                const selectedButton = optionButtons[selectedIndex];
                
                console.log("Checking answer - Selected option:", selectedOption, 
                           "Expected answer:", expectedAnswer);
                
                // Check if the selected option matches the expected answer
                if (selectedOption === expectedAnswer) {
                    // Correct answer
                    selectedButton.classList.add('correct');
                    
                    // Show notification
                    showNotification('Correct! +10 points', 'correct');
                    
                    // Add 10 points for correct answer
                    score += 10;
                    scoreDisplayElement.textContent = score;
                    correctWords++;
                    
                    // Disable all option buttons
                    optionButtons.forEach(button => {
                        button.disabled = true;
                    });
                    
                    // Move to next word after a short delay
                    setTimeout(() => {
                        currentWordIndex++;
                        nextWord();
                    }, 1500);
                } else {
                    // Incorrect answer
                    selectedButton.classList.add('incorrect');
                    wrongAttempts++;
                    
                    if (wrongAttempts === 1) {
                        // First wrong attempt: -5 points
                        score = Math.max(0, score - 5); // Prevent negative score
                        scoreDisplayElement.textContent = score;
                        showNotification('Incorrect! -5 points', 'incorrect');
                        
                        // Disable the selected button
                        selectedButton.disabled = true;
                    } else {
                        // Second wrong attempt: show correct answer and move to next word
                        // Find and highlight the correct option
                        if (correctOptionIndex >= 0 && correctOptionIndex < optionButtons.length) {
                            optionButtons[correctOptionIndex].classList.add('correct');
                        }
                        
                        // Disable all option buttons
                        optionButtons.forEach(button => {
                            button.disabled = true;
                        });
                        
                        showNotification('Incorrect! The correct answer is: ' + expectedAnswer, 'incorrect');
                        
                        // Move to next word after a delay
                        setTimeout(() => {
                            currentWordIndex++;
                            nextWord();
                        }, 2000);
                    }
                }
            }
            
            // Show notification in the center of the screen
            function showNotification(message, type) {
                notificationMessage.textContent = message;
                notificationMessage.className = type;
                notificationOverlay.classList.add('show');
                
                // Hide notification after a delay
                setTimeout(() => {
                    notificationOverlay.classList.remove('show');
                }, 1500);
            }
            
            // Show hint for the current word based on language mode
            function showHint() {
                if (!gameActive) return;
                
                const wordObj = gameWords[currentWordIndex];
                const languageMode = languageModeSelect.value;
                
                // Show the expected answer based on language mode
                if (languageMode === 'finnish-to-english') {
                    // Finnish word is displayed, show English answer
                    hintTextElement.textContent = wordObj.hint;
                } else {
                    // English word is displayed, show Finnish answer
                    hintTextElement.textContent = currentWord;
                }
                
                // Highlight the correct option if it exists
                if (correctOptionIndex >= 0 && correctOptionIndex < optionButtons.length) {
                    optionButtons[correctOptionIndex].style.borderColor = 'orange';
                    optionButtons[correctOptionIndex].style.borderWidth = '3px';
                }
                
                // Deduct 5 points for using hint
                score = Math.max(0, score - 5); // Prevent negative score
                scoreDisplayElement.textContent = score;
                
                // Show notification
                showNotification('Hint used! -5 points', 'incorrect');
                
                hintsUsed++;
                
                // Disable hint button for this word
                hintButton.disabled = true;
            }
            
            // End the game
            function endGame() {
                // Stop the timer
                clearInterval(timerInterval);
                
                // Update final stats
                finalScoreElement.textContent = score;
                finalTimeElement.textContent = timerElement.textContent;
                correctWordsElement.textContent = `${correctWords}/${totalWords}`;
                hintsUsedElement.textContent = hintsUsed;
                
                // Show completion modal
                completionModal.classList.add('active');
                
                // Disable game controls
                wordInputElement.disabled = true;
                submitButton.disabled = true;
                hintButton.disabled = true;
                
                // Set game as inactive
                gameActive = false;
            }
            
            // Update the timer
            function updateTimer() {
                if (!gameActive) return;
                
                elapsedTime = Math.floor((Date.now() - startTime) / 1000);
                const minutes = Math.floor(elapsedTime / 60);
                const seconds = elapsedTime % 60;
                
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            // Completely redesigned shuffle function for maximum randomization
            function shuffleArray(array) {
                // Make a copy of the array
                const newArray = [...array];
                const originalString = array.join('');
                
                // Generate all possible permutations for very short arrays (up to 4 characters)
                if (array.length <= 4) {
                    // For short arrays, we can try all permutations
                    const allPermutations = [];
                    
                    // Helper function to generate permutations
                    function generatePermutations(arr, start = 0) {
                        if (start === arr.length - 1) {
                            allPermutations.push([...arr]);
                            return;
                        }
                        
                        for (let i = start; i < arr.length; i++) {
                            // Swap elements
                            [arr[start], arr[i]] = [arr[i], arr[start]];
                            
                            // Recurse
                            generatePermutations(arr, start + 1);
                            
                            // Backtrack
                            [arr[start], arr[i]] = [arr[i], arr[start]];
                        }
                    }
                    
                    // Generate all permutations
                    generatePermutations(newArray);
                    
                    // Filter out the original arrangement
                    const differentPermutations = allPermutations.filter(perm => 
                        perm.join('') !== originalString
                    );
                    
                    // If we have different permutations, pick one randomly
                    if (differentPermutations.length > 0) {
                        const randomIndex = Math.floor(Math.random() * differentPermutations.length);
                        return differentPermutations[randomIndex];
                    }
                }
                
                // For longer arrays or if permutation approach didn't work, use enhanced shuffling
                
                // First approach: Multiple Fisher-Yates shuffles
                for (let attempt = 0; attempt < 10; attempt++) {
                    // Reset to a copy of the original for each attempt
                    const shuffleAttempt = [...array];
                    
                    // Multiple passes of Fisher-Yates
                    for (let pass = 0; pass < 5; pass++) {
                        for (let i = shuffleAttempt.length - 1; i > 0; i--) {
                            const j = Math.floor(Math.random() * (i + 1));
                            [shuffleAttempt[i], shuffleAttempt[j]] = [shuffleAttempt[j], shuffleAttempt[i]];
                        }
                    }
                    
                    // Check if this attempt produced a different result
                    if (shuffleAttempt.join('') !== originalString) {
                        return shuffleAttempt;
                    }
                }
                
                // Second approach: Force maximum difference
                // Count how many characters are in the same position
                let maxDifferentPositions = 0;
                let bestShuffle = [...newArray];
                
                for (let attempt = 0; attempt < 20; attempt++) {
                    const shuffleAttempt = [...array];
                    
                    // Shuffle
                    for (let i = shuffleAttempt.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [shuffleAttempt[i], shuffleAttempt[j]] = [shuffleAttempt[j], shuffleAttempt[i]];
                    }
                    
                    // Count different positions
                    let differentPositions = 0;
                    for (let i = 0; i < array.length; i++) {
                        if (shuffleAttempt[i] !== array[i]) {
                            differentPositions++;
                        }
                    }
                    
                    // Keep the shuffle with the most differences
                    if (differentPositions > maxDifferentPositions) {
                        maxDifferentPositions = differentPositions;
                        bestShuffle = [...shuffleAttempt];
                    }
                    
                    // If we've achieved maximum difference, stop
                    if (differentPositions === array.length) {
                        return shuffleAttempt;
                    }
                }
                
                // If we still haven't found a good shuffle, use the best one we found
                if (maxDifferentPositions > 0) {
                    return bestShuffle;
                }
                
                // Last resort: reverse the array
                return array.slice().reverse();
            }
            
            // Function to update the displayed word when language mode changes
            function updateDisplayedWord() {
                // If game is active, update the displayed word
                if (gameActive && currentWordIndex < totalWords) {
                    scrambledWord = scrambleWord(currentWord, difficultySelect.value);
                    scrambledWordElement.textContent = scrambledWord;
                    
                    // Clear hint and result
                    hintTextElement.textContent = '';
                    resultMessageElement.textContent = '';
                    resultMessageElement.className = 'result-message';
                    
                    // Reset wrong attempts
                    wrongAttempts = 0;
                    
                    // Generate new multiple choice options
                    generateMultipleChoiceOptions();
                    
                    // Re-enable hint button
                    hintButton.disabled = false;
                }
            }
            
            // Event listeners
            newGameButton.addEventListener('click', initGame);
            
            hintButton.addEventListener('click', showHint);
            
            // Add event listener for language mode changes
            languageModeSelect.addEventListener('change', updateDisplayedWord);
            
            // Add event listener for difficulty changes
            difficultySelect.addEventListener('change', updateDisplayedWord);
            
            playAgainButton.addEventListener('click', function() {
                completionModal.classList.remove('active');
                initGame();
            });
            
            // Mobile menu toggle functionality
            const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            const navLinks = document.getElementById("nav-links");
            
            if (mobileMenuToggle && navLinks) {
                console.log("Word Scramble - Mobile menu toggle found");
                
                // Define the toggle function
                function toggleMenu() {
                    // Only toggle on mobile screens
                    if (window.innerWidth <= 767) {
                        console.log("Word Scramble - Toggle menu clicked");
                        
                        // Check if menu is currently visible
                        const isVisible = navLinks.classList.contains("show");
                        
                        if (isVisible) {
                            // Hide the menu
                            navLinks.classList.remove("show");
                            mobileMenuToggle.classList.remove("active");
                            
                            // Direct style manipulation as backup
                            navLinks.style.display = "none";
                            navLinks.style.visibility = "hidden";
                            navLinks.style.opacity = "0";
                        } else {
                            // Show the menu
                            navLinks.classList.add("show");
                            mobileMenuToggle.classList.add("active");
                            
                            // Direct style manipulation as backup - with !important flags
                            navLinks.style.cssText = `
                                display: flex !important;
                                flex-direction: column !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                                position: absolute !important;
                                top: 60px !important;
                                left: 0 !important;
                                width: 100% !important;
                                z-index: 1000 !important;
                                background-color: #fff !important;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                                margin: 0 !important;
                                padding: 0 !important;
                                border-top: 1px solid #ddd !important;
                                height: auto !important;
                                overflow: visible !important;
                                transform: none !important;
                                pointer-events: auto !important;
                            `;
                            
                            // Apply dark mode styles if needed
                            if (document.body.classList.contains('dark-mode')) {
                                navLinks.style.backgroundColor = '#252525 !important';
                                navLinks.style.borderTop = '1px solid #333 !important';
                            }
                        }
                        
                        // Log the state for debugging
                        console.log("Word Scramble - Nav links show:", navLinks.classList.contains("show"));
                        console.log("Word Scramble - Mobile toggle active:", mobileMenuToggle.classList.contains("active"));
                    } else {
                        console.log("Word Scramble - Toggle menu ignored on desktop view");
                    }
                }
                
                // Remove any existing event listeners to prevent duplicates
                mobileMenuToggle.removeEventListener("click", toggleMenu);
                
                // Add the click event listener with capture and prevent default
                mobileMenuToggle.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log("Word Scramble - Mobile menu toggle clicked");
                    toggleMenu();
                }, true); // Use capture phase
                
                // Add a debug click handler to verify the element is clickable
                mobileMenuToggle.addEventListener("mousedown", function() {
                    console.log("Word Scramble - Menu button mousedown");
                });
                
                // Log initial state
                console.log("Word Scramble - Initial nav-links display:", window.getComputedStyle(navLinks).display);
                
                // Add window resize handler to adjust menu visibility based on screen size
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 767) {
                        // Desktop view - ensure menu is visible
                        navLinks.style.display = "flex";
                        navLinks.style.visibility = "visible";
                        navLinks.style.opacity = "1";
                        navLinks.style.position = "static";
                        navLinks.style.flexDirection = "row";
                    } else {
                        // Mobile view - hide menu unless it's explicitly shown
                        if (!navLinks.classList.contains("show")) {
                            navLinks.style.display = "none";
                            navLinks.style.visibility = "hidden";
                            navLinks.style.opacity = "0";
                        }
                    }
                });
                
                // Set initial state based on screen size
                if (window.innerWidth <= 767) {
                    // Mobile view - hide menu
                    navLinks.classList.remove("show");
                    navLinks.style.display = "none";
                    navLinks.style.visibility = "hidden";
                    navLinks.style.opacity = "0";
                } else {
                    // Desktop view - show menu
                    navLinks.style.display = "flex";
                    navLinks.style.visibility = "visible";
                    navLinks.style.opacity = "1";
                }
            } else {
                console.error("Word Scramble - Mobile menu elements not found:", {
                    mobileMenuToggle: !!mobileMenuToggle,
                    navLinks: !!navLinks
                });
            }
            
            // Check for saved dark mode preference
            const darkMode = localStorage.getItem("darkMode");
            
            // Apply dark mode if previously enabled
            if (darkMode === "enabled") {
                document.body.classList.add("dark-mode");
                
                // Update icon to sun
                const darkModeBtn = document.getElementById("word-scramble-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Update mobile icon too
                const mobileDarkModeBtn = document.getElementById("word-scramble-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            }
            
            // Set up dark mode toggle
            const darkModeToggle = document.getElementById("word-scramble-toggle-dark");
            if (darkModeToggle) {
                darkModeToggle.addEventListener("click", toggleDarkMode);
            }
            
            // Set up mobile dark mode toggle
            const mobileDarkModeToggle = document.getElementById("word-scramble-toggle-dark-mobile");
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.addEventListener("click", toggleDarkMode);
            }
            
            // Toggle dark mode function
            function toggleDarkMode() {
                document.body.classList.toggle("dark-mode");
                
                // Store preference in localStorage
                if (document.body.classList.contains("dark-mode")) {
                    localStorage.setItem("darkMode", "enabled");
                    
                    // Change icon to sun
                    const darkModeBtn = document.getElementById("word-scramble-toggle-dark");
                    if (darkModeBtn) {
                        const icon = darkModeBtn.querySelector("i");
                        if (icon) {
                            icon.classList.remove("fa-moon");
                            icon.classList.add("fa-sun");
                        }
                    }
                    
                    // Change mobile icon to sun
                    const mobileDarkModeBtn = document.getElementById("word-scramble-toggle-dark-mobile");
                    if (mobileDarkModeBtn) {
                        const mobileIcon = mobileDarkModeBtn.querySelector("i");
                        if (mobileIcon) {
                            mobileIcon.classList.remove("fa-moon");
                            mobileIcon.classList.add("fa-sun");
                        }
                    }
                } else {
                    localStorage.setItem("darkMode", "disabled");
                    
                    // Change icon back to moon
                    const darkModeBtn = document.getElementById("word-scramble-toggle-dark");
                    if (darkModeBtn) {
                        const icon = darkModeBtn.querySelector("i");
                        if (icon) {
                            icon.classList.remove("fa-sun");
                            icon.classList.add("fa-moon");
                        }
                    }
                    
                    // Change mobile icon back to moon
                    const mobileDarkModeBtn = document.getElementById("word-scramble-toggle-dark-mobile");
                    if (mobileDarkModeBtn) {
                        const mobileIcon = mobileDarkModeBtn.querySelector("i");
                        if (mobileIcon) {
                            mobileIcon.classList.remove("fa-sun");
                            mobileIcon.classList.add("fa-moon");
                        }
                    }
                }
            }
            
            // Handle dropdown menus in mobile view
            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach((dropdown) => {
                const dropbtn = dropdown.querySelector(".dropbtn");
                if (dropbtn) {
                    dropbtn.addEventListener("click", function (e) {
                        // Only in mobile view
                        if (window.innerWidth <= 767) {
                            e.preventDefault();
                            e.stopPropagation();
                            
                            // Close other active dropdowns
                            dropdowns.forEach((otherDropdown) => {
                                if (
                                    otherDropdown !== dropdown &&
                                    otherDropdown.classList.contains("active")
                                ) {
                                    otherDropdown.classList.remove("active");
                                }
                            });
                            
                            // Toggle current dropdown
                            dropdown.classList.toggle("active");
                        }
                    });
                }
            });
            
            // Add shake animation for incorrect answers
            const style = document.createElement('style');
            style.textContent = `
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                    20%, 40%, 60%, 80% { transform: translateX(5px); }
                }
                
                .shake {
                    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>








