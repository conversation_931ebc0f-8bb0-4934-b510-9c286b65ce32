﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Greetings and Introductions - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="greetings-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="greetings-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="greetings-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Greetings and Introductions</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Greetings and Introductions in Finnish</h2>
            <p>Learning how to greet people and introduce yourself is an essential first step in any language. Finnish greetings vary depending on the time of day, the formality of the situation, and the relationship between speakers. This page covers the most common greetings and introductory phrases you'll need in everyday Finnish conversations.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Basic Greetings</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Usage</th>
                </tr>
                <tr>
                    <td>Hei</td>
                    <td class="pronunciation">hey</td>
                    <td>Hi, Hello</td>
                    <td>The most common, all-purpose greeting</td>
                </tr>
                <tr>
                    <td>Moi</td>
                    <td class="pronunciation">moy</td>
                    <td>Hi</td>
                    <td>Casual greeting, very common</td>
                </tr>
                <tr>
                    <td>Terve</td>
                    <td class="pronunciation">tehr-veh</td>
                    <td>Hello</td>
                    <td>Slightly more formal than "hei" or "moi"</td>
                </tr>
                <tr>
                    <td>Hyvää päivää</td>
                    <td class="pronunciation">hü-vaa päi-vaa</td>
                    <td>Good day</td>
                    <td>Formal greeting used during daytime</td>
                </tr>
                <tr>
                    <td>Hyvää huomenta</td>
                    <td class="pronunciation">hü-vaa huo-men-ta</td>
                    <td>Good morning</td>
                    <td>Used in the morning until around 10-11 AM</td>
                </tr>
                <tr>
                    <td>Hyvää iltaa</td>
                    <td class="pronunciation">hü-vaa il-taa</td>
                    <td>Good evening</td>
                    <td>Used in the evening, after 6 PM</td>
                </tr>
                <tr>
                    <td>Hyvää yötä</td>
                    <td class="pronunciation">hü-vaa üö-tä</td>
                    <td>Good night</td>
                    <td>Used when someone is going to sleep</td>
                </tr>
                <tr>
                    <td>Moikka</td>
                    <td class="pronunciation">moyk-ka</td>
                    <td>Hi, Bye</td>
                    <td>Very casual, can be used for both greeting and farewell</td>
                </tr>
                <tr>
                    <td>Morjens</td>
                    <td class="pronunciation">mor-yens</td>
                    <td>Hi there</td>
                    <td>Casual, slightly slangy greeting</td>
                </tr>
                <tr>
                    <td>Tervetuloa</td>
                    <td class="pronunciation">tehr-veh-tu-lo-a</td>
                    <td>Welcome</td>
                    <td>Used to welcome someone to a place</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>A:</strong> Hei! Mitä kuuluu?</p>
                <p><strong>B:</strong> Moi! Kiitos hyvää, entä sinulle?</p>
                <p><strong>A:</strong> Hyvää, kiitos.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Hi! How are you?</p>
                <p><strong>B:</strong> Hi! I'm good, thanks, and you?</p>
                <p><strong>A:</strong> Good, thank you.</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> In Finland, people tend to be quite direct and may not always use elaborate greetings. A simple "Hei" or "Moi" is often sufficient in most everyday situations.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Farewells</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Usage</th>
                </tr>
                <tr>
                    <td>Hei hei</td>
                    <td class="pronunciation">hey hey</td>
                    <td>Bye bye</td>
                    <td>Common, casual farewell</td>
                </tr>
                <tr>
                    <td>Nähdään</td>
                    <td class="pronunciation">näh-dään</td>
                    <td>See you</td>
                    <td>Casual, implies you'll meet again</td>
                </tr>
                <tr>
                    <td>Näkemiin</td>
                    <td class="pronunciation">nä-ke-miin</td>
                    <td>Goodbye</td>
                    <td>More formal farewell</td>
                </tr>
                <tr>
                    <td>Moikka</td>
                    <td class="pronunciation">moyk-ka</td>
                    <td>Bye</td>
                    <td>Casual, can be used for both greeting and farewell</td>
                </tr>
                <tr>
                    <td>Moi moi</td>
                    <td class="pronunciation">moy moy</td>
                    <td>Bye bye</td>
                    <td>Casual, friendly farewell</td>
                </tr>
                <tr>
                    <td>Hyvästi</td>
                    <td class="pronunciation">hü-väs-ti</td>
                    <td>Farewell</td>
                    <td>Formal, sometimes implies a longer separation</td>
                </tr>
                <tr>
                    <td>Nähdään pian</td>
                    <td class="pronunciation">näh-dään pi-an</td>
                    <td>See you soon</td>
                    <td>Casual, friendly</td>
                </tr>
                <tr>
                    <td>Nähdään huomenna</td>
                    <td class="pronunciation">näh-dään huo-men-na</td>
                    <td>See you tomorrow</td>
                    <td>Specific timeframe</td>
                </tr>
                <tr>
                    <td>Hyvää päivänjatkoa</td>
                    <td class="pronunciation">hü-vää päi-vän-yat-ko-a</td>
                    <td>Have a good day</td>
                    <td>Formal, polite farewell during daytime</td>
                </tr>
                <tr>
                    <td>Hyvää viikonloppua</td>
                    <td class="pronunciation">hü-vää vii-kon-lop-pu-a</td>
                    <td>Have a good weekend</td>
                    <td>Used on Fridays or before weekends</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>A:</strong> Minun täytyy nyt lähteä. Nähdään huomenna!</p>
                <p><strong>B:</strong> Hei hei! Hyvää illanjatkoa!</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> I have to go now. See you tomorrow!</p>
                <p><strong>B:</strong> Bye bye! Have a good evening!</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Introducing Yourself</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Minä olen [name]</td>
                    <td class="pronunciation">mi-nä o-len [name]</td>
                    <td>I am [name]</td>
                </tr>
                <tr>
                    <td>Nimeni on [name]</td>
                    <td class="pronunciation">ni-me-ni on [name]</td>
                    <td>My name is [name]</td>
                </tr>
                <tr>
                    <td>Hauska tutustua</td>
                    <td class="pronunciation">haus-ka tu-tus-tu-a</td>
                    <td>Nice to meet you</td>
                </tr>
                <tr>
                    <td>Olen [nationality]</td>
                    <td class="pronunciation">o-len [nationality]</td>
                    <td>I am [nationality]</td>
                </tr>
                <tr>
                    <td>Olen kotoisin [place]sta</td>
                    <td class="pronunciation">o-len ko-toi-sin [place]sta</td>
                    <td>I am from [place]</td>
                </tr>
                <tr>
                    <td>Puhun [language]a</td>
                    <td class="pronunciation">pu-hun [language]a</td>
                    <td>I speak [language]</td>
                </tr>
                <tr>
                    <td>Opiskelen suomea</td>
                    <td class="pronunciation">o-pis-ke-len suo-me-a</td>
                    <td>I'm studying Finnish</td>
                </tr>
                <tr>
                    <td>Olen [age] vuotta vanha</td>
                    <td class="pronunciation">o-len [age] vuot-ta van-ha</td>
                    <td>I am [age] years old</td>
                </tr>
                <tr>
                    <td>Olen [profession]</td>
                    <td class="pronunciation">o-len [profession]</td>
                    <td>I am a [profession]</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>A:</strong> Hei! Nimeni on Liisa. Entä sinä?</p>
                <p><strong>B:</strong> Hei Liisa! Minä olen Markku. Hauska tutustua.</p>
                <p><strong>A:</strong> Hauska tutustua. Oletko suomalainen?</p>
                <p><strong>B:</strong> Kyllä, olen kotoisin Tampereelta. Entä sinä?</p>
                <p><strong>A:</strong> Olen ruotsalainen, mutta opiskelen suomea.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Hi! My name is Liisa. And you?</p>
                <p><strong>B:</strong> Hi Liisa! I am Markku. Nice to meet you.</p>
                <p><strong>A:</strong> Nice to meet you. Are you Finnish?</p>
                <p><strong>B:</strong> Yes, I'm from Tampere. And you?</p>
                <p><strong>A:</strong> I'm Swedish, but I'm studying Finnish.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Asking How Someone Is</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Mitä kuuluu?</td>
                    <td class="pronunciation">mi-tä kuu-luu</td>
                    <td>How are you? (lit. "What is heard?")</td>
                </tr>
                <tr>
                    <td>Miten menee?</td>
                    <td class="pronunciation">mi-ten me-nee</td>
                    <td>How's it going?</td>
                </tr>
                <tr>
                    <td>Kuinka voit?</td>
                    <td class="pronunciation">kuin-ka voit</td>
                    <td>How are you? (more formal)</td>
                </tr>
                <tr>
                    <td>Miten päiväsi on mennyt?</td>
                    <td class="pronunciation">mi-ten päi-vä-si on men-nyt</td>
                    <td>How has your day been?</td>
                </tr>
            </table>
            
            <h4>Common Responses</h4>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Kiitos hyvää</td>
                    <td class="pronunciation">kii-tos hü-vää</td>
                    <td>Good, thank you</td>
                </tr>
                <tr>
                    <td>Ihan hyvin, kiitos</td>
                    <td class="pronunciation">i-han hü-vin, kii-tos</td>
                    <td>Quite well, thanks</td>
                </tr>
                <tr>
                    <td>Ei mitään erikoista</td>
                    <td class="pronunciation">ei mi-tään e-ri-kois-ta</td>
                    <td>Nothing special</td>
                </tr>
                <tr>
                    <td>Entä sinulle?</td>
                    <td class="pronunciation">en-tä si-nul-le</td>
                    <td>And you?</td>
                </tr>
                <tr>
                    <td>Menee hyvin</td>
                    <td class="pronunciation">me-nee hü-vin</td>
                    <td>Going well</td>
                </tr>
                <tr>
                    <td>Olen väsynyt</td>
                    <td class="pronunciation">o-len vä-sü-nüt</td>
                    <td>I'm tired</td>
                </tr>
                <tr>
                    <td>Olen kiireinen</td>
                    <td class="pronunciation">o-len kii-rei-nen</td>
                    <td>I'm busy</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>A:</strong> Hei Matti! Mitä kuuluu?</p>
                <p><strong>B:</strong> Hei Anna! Kiitos hyvää. Olen vähän kiireinen töissä. Entä sinulle?</p>
                <p><strong>A:</strong> Minulla menee hyvin. Olen juuri palannut lomalta.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Hi Matti! How are you?</p>
                <p><strong>B:</strong> Hi Anna! Good, thanks. I'm a bit busy at work. And you?</p>
                <p><strong>A:</strong> I'm doing well. I've just returned from vacation.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Polite Phrases</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Kiitos</td>
                    <td class="pronunciation">kii-tos</td>
                    <td>Thank you</td>
                </tr>
                <tr>
                    <td>Kiitos paljon</td>
                    <td class="pronunciation">kii-tos pal-yon</td>
                    <td>Thank you very much</td>
                </tr>
                <tr>
                    <td>Ole hyvä</td>
                    <td class="pronunciation">o-le hü-vä</td>
                    <td>You're welcome / Please (when offering something)</td>
                </tr>
                <tr>
                    <td>Anteeksi</td>
                    <td class="pronunciation">an-teek-si</td>
                    <td>Sorry / Excuse me</td>
                </tr>
                <tr>
                    <td>Ei se mitään</td>
                    <td class="pronunciation">ei se mi-tään</td>
                    <td>It's nothing / Don't worry about it</td>
                </tr>
                <tr>
                    <td>Onnea</td>
                    <td class="pronunciation">on-ne-a</td>
                    <td>Good luck / Congratulations</td>
                </tr>
                <tr>
                    <td>Kippis</td>
                    <td class="pronunciation">kip-pis</td>
                    <td>Cheers (when drinking)</td>
                </tr>
                <tr>
                    <td>Hyvää ruokahalua</td>
                    <td class="pronunciation">hü-vää ruo-ka-ha-lu-a</td>
                    <td>Enjoy your meal</td>
                </tr>
                <tr>
                    <td>Onneksi olkoon</td>
                    <td class="pronunciation">on-nek-si ol-koon</td>
                    <td>Congratulations</td>
                </tr>
                <tr>
                    <td>Pahoitteluni</td>
                    <td class="pronunciation">pa-hoit-te-lu-ni</td>
                    <td>My apologies (formal)</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>A:</strong> Anteeksi, missä on lähin pankki?</p>
                <p><strong>B:</strong> Se on tuolla kadun toisella puolella.</p>
                <p><strong>A:</strong> Kiitos paljon!</p>
                <p><strong>B:</strong> Ole hyvä.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Excuse me, where is the nearest bank?</p>
                <p><strong>B:</strong> It's over there on the other side of the street.</p>
                <p><strong>A:</strong> Thank you very much!</p>
                <p><strong>B:</strong> You're welcome.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Asking for Language Help</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>Puhutko englantia?</td>
                    <td class="pronunciation">pu-hut-ko eng-lan-ti-a</td>
                    <td>Do you speak English?</td>
                </tr>
                <tr>
                    <td>En puhu hyvin suomea</td>
                    <td class="pronunciation">en pu-hu hü-vin suo-me-a</td>
                    <td>I don't speak Finnish well</td>
                </tr>
                <tr>
                    <td>Voitko puhua hitaammin?</td>
                    <td class="pronunciation">voit-ko pu-hu-a hi-taam-min</td>
                    <td>Can you speak more slowly?</td>
                </tr>
                <tr>
                    <td>Anteeksi, en ymmärrä</td>
                    <td class="pronunciation">an-teek-si, en üm-mär-rä</td>
                    <td>Sorry, I don't understand</td>
                </tr>
                <tr>
                    <td>Miten sanotaan...?</td>
                    <td class="pronunciation">mi-ten sa-no-taan</td>
                    <td>How do you say...?</td>
                </tr>
                <tr>
                    <td>Mitä tämä tarkoittaa?</td>
                    <td class="pronunciation">mi-tä tä-mä tar-koit-taa</td>
                    <td>What does this mean?</td>
                </tr>
                <tr>
                    <td>Voitko toistaa?</td>
                    <td class="pronunciation">voit-ko tois-taa</td>
                    <td>Can you repeat that?</td>
                </tr>
                <tr>
                    <td>Voitko kirjoittaa sen?</td>
                    <td class="pronunciation">voit-ko kir-joit-taa sen</td>
                    <td>Can you write it down?</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>A:</strong> Anteeksi, en ymmärrä. Voitko puhua hitaammin?</p>
                <p><strong>B:</strong> Totta kai. Kysyin, mistä olet kotoisin.</p>
                <p><strong>A:</strong> Kiitos. Olen kotoisin Kanadasta.</p>
                <p><em>Translation:</em></p>
                <p><strong>A:</strong> Sorry, I don't understand. Can you speak more slowly?</p>
                <p><strong>B:</strong> Of course. I asked where you are from.</p>
                <p><strong>A:</strong> Thank you. I'm from Canada.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Cultural Notes on Finnish Greetings</h3>
            
            <div class="note-box">
                <p><strong>Personal Space:</strong> Finns typically maintain a larger personal space than people from many other cultures. When greeting, a handshake is common in formal situations, but hugging or kissing on the cheek is generally reserved for close friends and family.</p>
                <p><strong>Small Talk:</strong> Finns are not known for extensive small talk. Brief exchanges about the weather or well-being are common, but long conversations with strangers are less typical.</p>
                <p><strong>Directness:</strong> Finnish communication tends to be direct and to the point. This isn't rudeness but a cultural preference for clarity and efficiency.</p>
                <p><strong>Silence:</strong> Silence is not considered awkward in Finnish culture. It's perfectly acceptable to have quiet moments in conversation.</p>
                <p><strong>Formality:</strong> The Finnish language has formal and informal forms of address, but the informal "sinä" (you) is increasingly common in most situations. The formal "te" is mainly used when addressing elderly people, in very formal business contexts, or in customer service.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Practice Dialogues</h3>
            
            <h4>Dialogue 1: Meeting Someone New</h4>
            <div class="example-box">
                <p><strong>Matti:</strong> Hei! Minä olen Matti.</p>
                <p><strong>Laura:</strong> Hei Matti! Nimeni on Laura. Hauska tutustua.</p>
                <p><strong>Matti:</strong> Hauska tutustua, Laura. Oletko täältä Helsingistä?</p>
                <p><strong>Laura:</strong> En, olen kotoisin Turusta, mutta asun nyt Helsingissä. Entä sinä?</p>
                <p><strong>Matti:</strong> Olen Tampereelta. Olen täällä vain käymässä.</p>
                <p><strong>Laura:</strong> Ahaa, mukava kuulla. Mitä teet työksesi?</p>
                <p><strong>Matti:</strong> Olen opettaja. Entä sinä?</p>
                <p><strong>Laura:</strong> Minä olen insinööri.</p>
                <p><em>Translation:</em></p>
                <p><strong>Matti:</strong> Hi! I'm Matti.</p>
                <p><strong>Laura:</strong> Hi Matti! My name is Laura. Nice to meet you.</p>
                <p><strong>Matti:</strong> Nice to meet you, Laura. Are you from Helsinki?</p>
                <p><strong>Laura:</strong> No, I'm from Turku, but I live in Helsinki now. And you?</p>
                <p><strong>Matti:</strong> I'm from Tampere. I'm just visiting here.</p>
                <p><strong>Laura:</strong> Oh, nice to hear. What do you do for work?</p>
                <p><strong>Matti:</strong> I'm a teacher. And you?</p>
                <p><strong>Laura:</strong> I'm an engineer.</p>
            </div>
            
            <h4>Dialogue 2: At a Café</h4>
            <div class="example-box">
                <p><strong>Asiakas (Customer):</strong> Hei!</p>
                <p><strong>Myyjä (Seller):</strong> Hei hei! Mitä saisi olla?</p>
                <p><strong>Asiakas:</strong> Yksi kahvi, kiitos.</p>
                <p><strong>Myyjä:</strong> Haluatko maitoa tai sokeria?</p>
                <p><strong>Asiakas:</strong> Kyllä, vähän maitoa, kiitos.</p>
                <p><strong>Myyjä:</strong> Ole hyvä, tässä kahvisi. Se on 3,50 euroa.</p>
                <p><strong>Asiakas:</strong> Kiitos. Tässä on 5 euroa.</p>
                <p><strong>Myyjä:</strong> Kiitos. Tässä on vaihtorahasi. Hyvää päivänjatkoa!</p>
                <p><strong>Asiakas:</strong> Kiitos samoin!</p>
                <p><em>Translation:</em></p>
                <p><strong>Customer:</strong> Hi!</p>
                <p><strong>Seller:</strong> Hello! What would you like?</p>
                <p><strong>Customer:</strong> One coffee, please.</p>
                <p><strong>Seller:</strong> Would you like milk or sugar?</p>
                <p><strong>Customer:</strong> Yes, a little milk, please.</p>
                <p><strong>Seller:</strong> Here you are, here's your coffee. It's 3.50 euros.</p>
                <p><strong>Customer:</strong> Thank you. Here's 5 euros.</p>
                <p><strong>Seller:</strong> Thank you. Here's your change. Have a good day!</p>
                <p><strong>Customer:</strong> Thank you, same to you!</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















