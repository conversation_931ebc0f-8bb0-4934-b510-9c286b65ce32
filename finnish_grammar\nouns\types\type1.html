﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type 1 Nouns - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Nouns</a>
            <span class="separator">></span>
            <span>Type 1: Words ending in -i</span>
        </div>
        
        <section class="grammar-section">
            <h2>Type 1: Words ending in -i</h2>
            <p>Type 1 nouns in Finnish are words that end in -i. These nouns have specific inflection patterns that differ from other noun types. This page explains how to recognize and inflect Type 1 nouns.</p>
        </section>

        <section class="grammar-category">
            <h3>CHARACTERISTICS OF TYPE 1 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Type 1 nouns have the following characteristics:</p>
                <ul>
                    <li>They end in -i in the nominative singular form</li>
                    <li>The -i changes to -e- in most inflected forms</li>
                    <li>Common examples include: kieli (language), tuoli (chair), kivi (stone), järvi (lake)</li>
                </ul>
                
                <p>This type includes many common Finnish words, including many body parts, natural features, and everyday objects.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">kieli</span> <span class="english">language, tongue</span></p>
                    <p><span class="finnish">järvi</span> <span class="english">lake</span></p>
                    <p><span class="finnish">ovi</span> <span class="english">door</span></p>
                    <p><span class="finnish">lumi</span> <span class="english">snow</span></p>
                    <p><span class="finnish">nimi</span> <span class="english">name</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>INFLECTION PATTERNS</h3>
            
            <div class="grammar-content">
                <p>The key feature of Type 1 nouns is that the final -i changes to -e- in most inflected forms:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>kieli</td>
                        <td>kielet</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>kielen</td>
                        <td>kielien / kielten</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>kieltä</td>
                        <td>kieliä</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>kielessä</td>
                        <td>kielissä</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>kielestä</td>
                        <td>kielistä</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>kieleen</td>
                        <td>kieliin</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>kielellä</td>
                        <td>kielillä</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>kieleltä</td>
                        <td>kieliltä</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>kielelle</td>
                        <td>kielille</td>
                    </tr>
                </table>
                
                <p>Note how the -i changes to -e- in all singular forms except the nominative, and how the -i returns in the plural forms.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION</h3>
            
            <div class="grammar-content">
                <p>Many Type 1 nouns undergo consonant gradation. This means that certain consonants change in different forms of the word.</p>
                
                <p>For example, with the word "kieli" (language):</p>
                <ul>
                    <li>The strong grade is "kiel-" (as in "kieli")</li>
                    <li>The weak grade is "kiel-" (as in "kielen")</li>
                </ul>
                
                <p>Another example with "järvi" (lake):</p>
                <ul>
                    <li>The strong grade is "järv-" (as in "järvi")</li>
                    <li>The weak grade is "järv-" (as in "järven")</li>
                </ul>
                
                <p>Not all Type 1 nouns undergo consonant gradation. It depends on the specific consonants in the word.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">nimi (name) → nimen (of the name)</span> <span class="english">m → m (no gradation)</span></p>
                    <p><span class="finnish">joki (river) → joen (of the river)</span> <span class="english">k → Ø (k disappears)</span></p>
                    <p><span class="finnish">käsi (hand) → käden (of the hand)</span> <span class="english">s → d</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES OF TYPE 1 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Here are some common Type 1 nouns and their basic forms:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Nominative</th>
                        <th>Genitive</th>
                        <th>Partitive</th>
                        <th>Meaning</th>
                    </tr>
                    <tr>
                        <td>kieli</td>
                        <td>kielen</td>
                        <td>kieltä</td>
                        <td>language, tongue</td>
                    </tr>
                    <tr>
                        <td>järvi</td>
                        <td>järven</td>
                        <td>järveä</td>
                        <td>lake</td>
                    </tr>
                    <tr>
                        <td>ovi</td>
                        <td>oven</td>
                        <td>ovea</td>
                        <td>door</td>
                    </tr>
                    <tr>
                        <td>lumi</td>
                        <td>lumen</td>
                        <td>lunta</td>
                        <td>snow</td>
                    </tr>
                    <tr>
                        <td>nimi</td>
                        <td>nimen</td>
                        <td>nimeä</td>
                        <td>name</td>
                    </tr>
                    <tr>
                        <td>joki</td>
                        <td>joen</td>
                        <td>jokea</td>
                        <td>river</td>
                    </tr>
                    <tr>
                        <td>käsi</td>
                        <td>käden</td>
                        <td>kättä</td>
                        <td>hand</td>
                    </tr>
                    <tr>
                        <td>vesi</td>
                        <td>veden</td>
                        <td>vettä</td>
                        <td>water</td>
                    </tr>
                    <tr>
                        <td>tuoli</td>
                        <td>tuolin</td>
                        <td>tuolia</td>
                        <td>chair</td>
                    </tr>
                    <tr>
                        <td>kivi</td>
                        <td>kiven</td>
                        <td>kiveä</td>
                        <td>stone</td>
                    </tr>
                </table>
                
                <p>Note that some Type 1 nouns have irregular partitive forms, such as "lumi" → "lunta" and "käsi" → "kättä".</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE EXAMPLES</h3>
            
            <div class="grammar-content">
                <p>Here are some examples of Type 1 nouns used in sentences:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Puhun kolmea kieltä.</span> <span class="english">I speak three languages.</span></p>
                    <p><span class="finnish">Järven vesi on kylmää.</span> <span class="english">The water of the lake is cold.</span></p>
                    <p><span class="finnish">Mikä sinun nimesi on?</span> <span class="english">What is your name?</span></p>
                    <p><span class="finnish">Sulje ovi, kiitos.</span> <span class="english">Close the door, please.</span></p>
                    <p><span class="finnish">Ulkona on paljon lunta.</span> <span class="english">There is a lot of snow outside.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















