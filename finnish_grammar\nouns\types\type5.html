﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type 5 Nouns - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Nouns</a>
            <span class="separator">></span>
            <span>Type 5: Words ending in -o/-ö/-u/-y</span>
        </div>
        
        <section class="grammar-section">
            <h2>Type 5: Words ending in -o/-ö/-u/-y</h2>
            <p>Type 5 nouns in Finnish are words that end in -o, -ö, -u, or -y. These nouns have specific inflection patterns that differ from other noun types. This page explains how to recognize and inflect Type 5 nouns.</p>
        </section>

        <section class="grammar-category">
            <h3>CHARACTERISTICS OF TYPE 5 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Type 5 nouns have the following characteristics:</p>
                <ul>
                    <li>They end in -o, -ö, -u, or -y in the nominative singular form</li>
                    <li>The final vowel is retained in most inflected forms</li>
                    <li>Common examples include: auto (car), pöytä (table), luku (number), kylpy (bath)</li>
                </ul>
                
                <p>This type includes many common Finnish words, including many everyday objects, concepts, and activities.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">auto</span> <span class="english">car</span></p>
                    <p><span class="finnish">pöytä</span> <span class="english">table</span></p>
                    <p><span class="finnish">luku</span> <span class="english">number</span></p>
                    <p><span class="finnish">kylpy</span> <span class="english">bath</span></p>
                    <p><span class="finnish">radio</span> <span class="english">radio</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>INFLECTION PATTERNS</h3>
            
            <div class="grammar-content">
                <p>Type 5 nouns follow a relatively straightforward inflection pattern. The final vowel is retained in most forms.</p>
                
                <h4>Example: auto (car)</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>auto</td>
                        <td>autot</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>auton</td>
                        <td>autojen</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>autoa</td>
                        <td>autoja</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>autossa</td>
                        <td>autoissa</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>autosta</td>
                        <td>autoista</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>autoon</td>
                        <td>autoihin</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>autolla</td>
                        <td>autoilla</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>autolta</td>
                        <td>autoilta</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>autolle</td>
                        <td>autoille</td>
                    </tr>
                </table>
                
                <h4>Example: kylpy (bath)</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>kylpy</td>
                        <td>kylvyt</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>kylvyn</td>
                        <td>kylpyjen</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>kylpyä</td>
                        <td>kylpyjä</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>kylvyssä</td>
                        <td>kylvyissä</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>kylvystä</td>
                        <td>kylvyistä</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>kylpyyn</td>
                        <td>kylpyihin</td>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>kylvyllä</td>
                        <td>kylvyillä</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>kylvyltä</td>
                        <td>kylvyiltä</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>kylvylle</td>
                        <td>kylvyille</td>
                    </tr>
                </table>
                
                <p>Note that in the plural forms, an -i- is added before the case ending.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION</h3>
            
            <div class="grammar-content">
                <p>Some Type 5 nouns undergo consonant gradation. This means that certain consonants change in different forms of the word.</p>
                
                <p>For example, with the word "kylpy" (bath):</p>
                <ul>
                    <li>The strong grade is "kylpy" (as in "kylpy", "kylpyä")</li>
                    <li>The weak grade is "kylvy-" (as in "kylvyn", "kylvyssä")</li>
                </ul>
                
                <p>Another example with "luku" (number):</p>
                <ul>
                    <li>The strong grade is "luku" (as in "luku", "lukua")</li>
                    <li>The weak grade is "luvu-" (as in "luvun", "luvussa")</li>
                </ul>
                
                <p>Not all Type 5 nouns undergo consonant gradation. It depends on the specific consonants in the word.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">auto (car) → auton (of the car)</span> <span class="english">t → t (no gradation)</span></p>
                    <p><span class="finnish">kylpy (bath) → kylvyn (of the bath)</span> <span class="english">p → v</span></p>
                    <p><span class="finnish">luku (number) → luvun (of the number)</span> <span class="english">k → v</span></p>
                    <p><span class="finnish">teko (deed) → teon (of the deed)</span> <span class="english">k → Ø (k disappears)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES OF TYPE 5 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Here are some common Type 5 nouns and their basic forms:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Nominative</th>
                        <th>Genitive</th>
                        <th>Partitive</th>
                        <th>Meaning</th>
                    </tr>
                    <tr>
                        <td>auto</td>
                        <td>auton</td>
                        <td>autoa</td>
                        <td>car</td>
                    </tr>
                    <tr>
                        <td>radio</td>
                        <td>radion</td>
                        <td>radiota</td>
                        <td>radio</td>
                    </tr>
                    <tr>
                        <td>pöytä</td>
                        <td>pöydän</td>
                        <td>pöytää</td>
                        <td>table</td>
                    </tr>
                    <tr>
                        <td>luku</td>
                        <td>luvun</td>
                        <td>lukua</td>
                        <td>number</td>
                    </tr>
                    <tr>
                        <td>katu</td>
                        <td>kadun</td>
                        <td>katua</td>
                        <td>street</td>
                    </tr>
                    <tr>
                        <td>kylpy</td>
                        <td>kylvyn</td>
                        <td>kylpyä</td>
                        <td>bath</td>
                    </tr>
                    <tr>
                        <td>käsky</td>
                        <td>käskyn</td>
                        <td>käskyä</td>
                        <td>command</td>
                    </tr>
                    <tr>
                        <td>teko</td>
                        <td>teon</td>
                        <td>tekoa</td>
                        <td>deed</td>
                    </tr>
                    <tr>
                        <td>suku</td>
                        <td>suvun</td>
                        <td>sukua</td>
                        <td>family, kin</td>
                    </tr>
                    <tr>
                        <td>jälkö</td>
                        <td>jäljön</td>
                        <td>jälköä</td>
                        <td>trace</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PLURAL FORMATION</h3>
            
            <div class="grammar-content">
                <p>The plural forms of Type 5 nouns follow these patterns:</p>
                
                <h4>1. Nominative plural</h4>
                <p>Add -t to the nominative singular:</p>
                <div class="grammar-example">
                    <p><span class="finnish">auto → autot</span></p>
                    <p><span class="finnish">pöytä → pöydät</span></p>
                    <p><span class="finnish">luku → luvut</span></p>
                    <p><span class="finnish">kylpy → kylvyt</span></p>
                </div>
                
                <h4>2. Genitive plural</h4>
                <p>Add -jen to the stem:</p>
                <div class="grammar-example">
                    <p><span class="finnish">auto → autojen</span></p>
                    <p><span class="finnish">pöytä → pöytien</span></p>
                    <p><span class="finnish">luku → lukujen</span></p>
                    <p><span class="finnish">kylpy → kylpyjen</span></p>
                </div>
                
                <h4>3. Partitive plural</h4>
                <p>Add -ja/-jä to the stem:</p>
                <div class="grammar-example">
                    <p><span class="finnish">auto → autoja</span></p>
                    <p><span class="finnish">pöytä → pöytiä</span></p>
                    <p><span class="finnish">luku → lukuja</span></p>
                    <p><span class="finnish">kylpy → kylpyjä</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE EXAMPLES</h3>
            
            <div class="grammar-content">
                <p>Here are some examples of Type 5 nouns used in sentences:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Ajan autolla.</span> <span class="english">I drive a car.</span></p>
                    <p><span class="finnish">Kirja on pöydällä.</span> <span class="english">The book is on the table.</span></p>
                    <p><span class="finnish">Osaan lukea lukuja.</span> <span class="english">I can read numbers.</span></p>
                    <p><span class="finnish">Otan kylvyn.</span> <span class="english">I take a bath.</span></p>
                    <p><span class="finnish">Kävelen kadulla.</span> <span class="english">I walk on the street.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















