/* Main Styles for Opiskelen Suomea */

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Audio List Styles */
.audio-tab-container {
    margin-top: 30px;
    width: 100%;
}

.audio-tab-buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.audio-tab-button {
    padding: 10px 20px;
    margin: 0 5px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-weight: 500;
}

.audio-tab-button:hover {
    background-color: #1a5fb4; /* Darker blue instead of white */
}

.audio-tab-button.active {
    background-color: var(--accent-color);
}

.audio-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    gap: 10px;
}

.audio-control-button {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.audio-control-button:hover {
    background-color: var(--secondary-color);
}

.audio-control-button.active {
    background-color: var(--accent-color);
}

.audio-list-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: var(--bg-color-light);
}

.audio-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.audio-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    background-color: var(--bg-color);
    transition: background-color 0.3s;
}

.audio-item:hover {
    background-color: var(--hover-color);
}

.audio-item.playing {
    background-color: var(--highlight-color-light);
    border-left: 4px solid var(--accent-color);
}

.audio-title {
    flex-grow: 1;
    font-size: 14px;
}

.audio-play-single {
    background-color: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.audio-play-single:hover {
    background-color: var(--primary-color);
    color: white;
}

.audio-item.playing .audio-play-single {
    background-color: var(--accent-color);
    color: white;
}

/* Dark mode adjustments for audio list */
.dark-mode .audio-list-container {
    background-color: var(--dark-bg-color-light);
    border-color: #444;
}

.dark-mode .audio-item {
    background-color: var(--dark-bg-color);
}

.dark-mode .audio-item:hover {
    background-color: var(--dark-hover-color);
}

.dark-mode .audio-item.playing {
    background-color: var(--dark-highlight-color-light);
}

.dark-mode .audio-play-single {
    color: var(--dark-text-color);
}

.dark-mode .audio-play-single:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Tab content styles */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Highlight Mode Styles */
.highlight-mode {
    cursor: pointer;
}

/* Highlight mode for video page - only affects user-selected text */
.highlight-mode ::selection {
    background-color: var(--highlight-color) !important;
    color: black !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.3) !important;
}

/* Ensure selection works in all browsers */
.highlight-mode ::-moz-selection {
    background-color: var(--highlight-color) !important;
    color: black !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.3) !important;
}

/* Animation for highlight notification */
@keyframes fadeInOut {
    0% { opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; }
}

.user-highlight {
    background-color: var(--highlight-color);
    color: black !important;
    border-radius: 2px;
    padding: 0 2px;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    display: inline !important;
    white-space: normal !important;
    position: static !important;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
    line-height: inherit !important;
    height: auto !important;
    width: auto !important;
    margin: 0 !important;
    border: none !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    font-family: inherit !important;
    text-align: inherit !important;
    vertical-align: baseline !important;
}

/* Ensure highlights work in all contexts */
h1 .user-highlight,
h2 .user-highlight,
h3 .user-highlight,
h4 .user-highlight,
h5 .user-highlight,
h6 .user-highlight,
p .user-highlight,
li .user-highlight,
td .user-highlight,
th .user-highlight,
span .user-highlight,
div .user-highlight {
    display: inline !important;
    white-space: normal !important;
    position: static !important;
}

/* Ensure table layout isn't disrupted */
.vocabulary-table td .user-highlight,
.vocabulary-table th .user-highlight {
    display: inline !important;
    position: static !important;
    padding: 0 2px !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
    vertical-align: baseline !important;
}

/* Notification styling */
.highlight-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
    font-size: 14px;
    max-width: 300px;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.active-tool {
    background-color: var(--primary-color) !important;
    color: white !important;
    font-weight: bold !important;
    box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.5) !important;
    border-color: var(--primary-color) !important;
    transform: none !important;
}

/* Highlight disabled state */
.highlight-disabled .chapter-content {
    cursor: default !important;
}

/* Highlight toggle button */
#highlight-toggle {
    margin-left: 10px;
}

/* Global Styles */
:root {
    --primary-color: #003580; /* Finnish flag blue */
    --primary-color-rgb: 0, 53, 128; /* RGB values for primary color */
    --secondary-color: #ffffff;
    --accent-color: #f8f8f8;
    --text-color: #333333;
    --text-color-secondary: #666666;
    --heading-color: #222222;
    --border-color: #dddddd;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --success-color: #4CAF50;
    --highlight-color: #FFFF00; /* Yellow for highlights */
    --bg-color: #ffffff; /* Background color for light mode */
    --card-bg-color: #ffffff; /* Card background color for light mode */
    --bg-color-dark: #121212; /* Background color for dark mode */
    --dark-card-bg: #1e1e1e; /* Card background color for dark mode */
    --dark-text-color: #e0e0e0; /* Text color for dark mode */
    --dark-text-color-secondary: #a0a0a0; /* Secondary text color for dark mode */
    --dark-heading-color: #ffffff; /* Heading color for dark mode */
    --dark-border-color: #333333; /* Border color for dark mode */
    --category-daily: #4285F4;
    --category-web: #EA4335;
    --category-cleaner: #34A853;
    --category-kitchen: #FBBC05;
    --category-warehouse: #9C27B0;
    --category-technology: #FF5722;

    /* Section colors */
    --objectives-color: #EA4335; /* Red for objectives */
    --objectives-color-rgb: 234, 67, 53;
    --vocabulary-color: #4285F4; /* Blue for vocabulary */
    --vocabulary-color-rgb: 66, 133, 244;
    --grammar-color: #34A853;    /* Green for grammar */
    --grammar-color-rgb: 52, 168, 83;
    --script-color: #FBBC05;     /* Yellow for script */
    --script-color-rgb: 251, 188, 5;
    --cultural-color: #9C27B0;   /* Purple for cultural notes */
    --cultural-color-rgb: 156, 39, 176;
}

/* Dark Mode Styles */
.dark-mode {
    --secondary-color: #121212;
    --accent-color: #1e1e1e;
    --text-color: #e0e0e0;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --primary-color: #1a5fb4; /* Adjusted Finnish flag blue for dark mode */
    --footer-bg: #0a0a0a; /* Darker background for footer */
    --item-bg: #1a1a1a; /* Slightly lighter than secondary for items */
    --highlight-color: #665500; /* Darker yellow for highlights in dark mode */

    background-color: var(--secondary-color);
    color: var(--text-color);
}

.dark-mode .chapter-card,
.dark-mode .sidebar-section,
.dark-mode .chapter-main-full,
.dark-mode .tab-content,
.dark-mode .intro,
.dark-mode .structure-section,
.dark-mode .categories-section {
    background-color: var(--item-bg);
    border-color: var(--border-color);
}

.dark-mode .chapter-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

/* Dark mode category-specific card backgrounds */
.dark-mode .daily-life .chapter-card {
    background-color: var(--category-daily-bg);
    border-top: 3px solid var(--category-daily);
}

.dark-mode .web-development .chapter-card {
    background-color: var(--category-web-bg);
    border-top: 3px solid var(--category-web);
}

.dark-mode .cleaner .chapter-card {
    background-color: var(--category-cleaner-bg);
    border-top: 3px solid var(--category-cleaner);
}

.dark-mode .kitchen-assistant .chapter-card {
    background-color: var(--category-kitchen-bg);
    border-top: 3px solid var(--category-kitchen);
}

.dark-mode .warehouse .chapter-card {
    background-color: var(--category-warehouse-bg);
    border-top: 3px solid var(--category-warehouse);
}

.dark-mode .technology .chapter-card {
    background-color: var(--category-technology-bg);
    border-top: 3px solid var(--category-technology);
}

.dark-mode .chapter-footer {
    background-color: rgba(0, 0, 0, 0.3);
    color: #aaaaaa;
    border-top: 1px solid rgba(255, 255, 255, 0.03);
}

/* Dark mode category-specific card footers */
.dark-mode .daily-life .chapter-footer {
    background-color: rgba(120, 144, 156, 0.08);
}

.dark-mode .web-development .chapter-footer {
    background-color: rgba(234, 67, 53, 0.08);
}

.dark-mode .cleaner .chapter-footer {
    background-color: rgba(52, 168, 83, 0.08);
}

.dark-mode .kitchen-assistant .chapter-footer {
    background-color: rgba(251, 188, 5, 0.08);
}

.dark-mode .warehouse .chapter-footer {
    background-color: rgba(156, 39, 176, 0.08);
}

.dark-mode .technology .chapter-footer {
    background-color: rgba(255, 87, 34, 0.08);
}

.dark-mode .chapter-number {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Dark mode category-specific chapter numbers */
.dark-mode .daily-life .chapter-number {
    color: var(--category-daily);
}

.dark-mode .web-development .chapter-number {
    color: var(--category-web);
}

.dark-mode .cleaner .chapter-number {
    color: var(--category-cleaner);
}

.dark-mode .kitchen-assistant .chapter-number {
    color: var(--category-kitchen);
}

.dark-mode .warehouse .chapter-number {
    color: var(--category-warehouse);
}

.dark-mode .technology .chapter-number {
    color: var(--category-technology);
}

.dark-mode .nav-links a,
.dark-mode .footer-links a,
.dark-mode h1,
.dark-mode h2,
.dark-mode h3 {
    color: var(--text-color);
}

.dark-mode .compact-nav-button {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

/* Ensure the highlight button in dark mode shows correct styling when not active */
.dark-mode #compact-toggle-highlight:not(.active-tool) {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.dark-mode .compact-nav-button:hover {
    background-color: var(--primary-color);
    color: #ffffff;
    border-color: var(--primary-color);
}

/* Make dark mode toggle button more visible in dark mode */
.dark-mode #compact-toggle-dark {
    background-color: #444;
    color: #fff;
    border-color: #555;
}

.dark-mode #compact-toggle-dark.active-tool {
    background-color: #000000 !important;
    color: #ffffff !important; /* Dark text for contrast */
    border-color: #ffffff !important;
    text-shadow: none !important;
}

.dark-mode .dropdown-content {
    background-color: var(--accent-color);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.5);
}

.dark-mode .dropdown-content a:hover {
    background-color: #2a2a2a;
}

/* Dark mode submenu items */
.dark-mode .dropdown-submenu .dropdown-content a {
    color: var(--text-color) !important;
}

.dark-mode .dropdown-submenu .dropdown-content a:hover {
    background-color: #2a2a2a !important;
    color: var(--primary-color) !important;
}

/* Dark mode for nested dropdown */
.dark-mode .dropdown-submenu > .dropdown-content {
    background-color: var(--accent-color);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.5);
}

.dark-mode .submenu-header {
    background-color: rgba(var(--primary-color-rgb), 0.2);
    color: var(--primary-color);
}

.dark-mode .submenu-header:hover {
    background-color: rgba(var(--primary-color-rgb), 0.3) !important;
}

.dark-mode .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.7);
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .daily-life .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(120, 144, 156, 0.25);
}

.dark-mode .web-development .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(234, 67, 53, 0.2);
}

.dark-mode .cleaner .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(52, 168, 83, 0.2);
}

.dark-mode .kitchen-assistant .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(251, 188, 5, 0.2);
}

.dark-mode .warehouse .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(156, 39, 176, 0.2);
}

.dark-mode .technology .chapter-card:hover {
    box-shadow: 0 8px 20px rgba(255, 87, 34, 0.2);
}

/* Dark mode category colors */
.dark-mode {
    --category-daily: #a5a5a58f;    /* Material Blue Grey 500 - soft grey with slight blue tint */
    --category-web: #EA4335;      /* Google red - more vibrant */
    --category-cleaner: #34A853;  /* Google green - more vibrant */
    --category-kitchen: #FBBC05;  /* Google yellow - more vibrant */
    --category-warehouse: #9C27B0; /* Material purple - more vibrant */
    --category-technology: #FF5722; /* Material deep orange - more vibrant */
    --highlight-color: #FFFF00;   /* Yellow for highlights in dark mode */

    /* Category background colors for dark mode */
    --category-daily-bg: rgba(120, 144, 156, 0.15);
    --category-web-bg: rgba(234, 67, 53, 0.15);
    --category-cleaner-bg: rgba(52, 168, 83, 0.15);
    --category-kitchen-bg: rgba(251, 188, 5, 0.15);
    --category-warehouse-bg: rgba(156, 39, 176, 0.15);
    --category-technology-bg: rgba(255, 87, 34, 0.15);
}

/* Dark mode tab buttons */
.dark-mode .tab-button {
    color: #a0a0a0;
}

.dark-mode .tab-button:hover {
    color: #ffffff;
}

.dark-mode .tab-button.active {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
}

/* Dark mode load more button */
.dark-mode .load-more-btn {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border: 1px solid #444444;
}

.dark-mode .load-more-btn:hover {
    background-color: var(--primary-color);
    color: #ffffff;
    border-color: var(--primary-color);
}

/* Dark mode chapter navigation */
.dark-mode .nav-button,
.dark-mode .toc-button,
.dark-mode .back-to-home {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border: 1px solid #444444;
}

.dark-mode .nav-button:hover,
.dark-mode .toc-button:hover,
.dark-mode .back-to-home:hover {
    background-color: var(--primary-color);
    color: #ffffff;
    border-color: var(--primary-color);
}

.dark-mode .toc-dropdown-content {
    background-color: #252525;
    border: 1px solid #444444;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dark-mode .toc-item {
    color: #e0e0e0;
    border-bottom: 1px solid #333333;
}

.dark-mode .toc-item:hover {
    background-color: #333333;
}

/* Dark mode chapter content */
.dark-mode .chapter-content h1,
.dark-mode .chapter-content h2,
.dark-mode .chapter-content h3 {
    color: #ffffff;
}

.dark-mode .chapter-content .keyword {
    color: var(--primary-color);
    font-weight: bold;
}

.dark-mode .chapter-progress-container {
    background-color: #333333;
}

.dark-mode .chapter-progress-bar {
    background-color: var(--primary-color);
}

.dark-mode .translation-pair {
    background-color: #252525;
    border: 1px solid #444444;
}

.dark-mode .finnish-term {
    color: #ffffff;
    border-bottom: 1px solid #444444;
}

.dark-mode .english-term {
    color: #cccccc;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--secondary-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

/* Header Styles */
header {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    text-align: center;
    padding: 2rem 0;
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.subtitle {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.flag-colors {
    display: flex;
    justify-content: center;
    width: 100px;
    margin: 0 auto;
}

.flag-stripe {
    height: 4px;
    flex: 1;
}

.blue {
    background-color: var(--primary-color);
}

.white {
    background-color: var(--secondary-color);
}

/* Navigation Styles */
nav {
    background-color: var(--secondary-color);
    box-shadow: 0 2px 5px var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 10000 !important; /* Very high z-index to ensure it's above other elements */
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.8rem 1rem;
    position: relative;
}

/* Theme toggle and highlight toggle button positioning */
.theme-toggle-container, .highlight-toggle-container {
    display: flex;
    align-items: center;
}

/* Hide theme toggle and highlight toggle buttons when mobile menu is active */
.nav-links.show .theme-toggle-container,
.nav-links.show .highlight-toggle-container {
    display: none;
}

/* Hide mobile theme toggle on desktop */
.mobile-only-theme-toggle {
    display: none;
}

/* Mobile theme toggle button styling */
.mobile-only-theme-toggle .theme-toggle-button {
    /* margin-top: 8px; */
    /* Ensure it has the same styling as the hamburger menu */
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    width: 44px;
    height: 44px;
}

/* Dark mode styles for mobile theme toggle button */
.dark-mode .mobile-only-theme-toggle .theme-toggle-button {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Theme toggle and highlight toggle buttons */
.theme-toggle-button, .highlight-toggle-button {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px; /* Match mobile menu toggle size */
    height: 44px; /* Match mobile menu toggle size */
    outline: none !important;
}

.dark-mode .theme-toggle-button {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.logo a {
    font-weight: bold;
    font-size: 1.2rem;
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-links li {
    margin-left: 1.5rem;
    position: relative;
    text-align: left;
}

/* Mobile menu toggle button styles moved to a single location */

.nav-links a {
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
    text-align: left;
    display: block;
}

.nav-links a:hover {
    color: var(--primary-color);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    width: 220px; /* Fixed width to match dropdown items */
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 9999 !important;
    border-radius: 4px;
    top: 100%;
    left: 0;
    transform-origin: top left;
    animation: none; /* Remove animation to prevent the mirroring effect */
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: scaleX(0);
    }
    to {
        opacity: 1;
        transform: scaleX(1);
    }
}

.dropdown-content a {
    color: var(--text-color);
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-size: 0.9rem;
    text-align: left;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
    color: var(--primary-color);
}

/* Ensure submenu items are clickable and have proper hover effects */
.dropdown-submenu .dropdown-content a {
    pointer-events: auto !important;
    cursor: pointer !important;
    transition: background-color 0.3s ease, color 0.3s ease !important;
    position: relative !important;
    z-index: 10003 !important;
    display: block !important;
    padding: 12px 16px !important;
    text-decoration: none !important;
    color: #333 !important;
}

.dropdown-submenu .dropdown-content a:hover {
    background-color: #f1f1f1 !important;
    color: var(--primary-color) !important;
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* Ensure dropdown is always on top */
.dropdown {
    position: relative !important;
    z-index: 10000 !important;
}

.dropdown:hover {
    z-index: 10001 !important;
}

.dropdown.active {
    z-index: 10002 !important;
}

/* Ensure main content and game containers have lower z-index */
.main-content {
    position: relative !important;
    z-index: 1 !important;
}

.game-container {
    position: relative !important;
    z-index: 1 !important;
}

/* Ensure nav dropdown have highest z-index */
nav .dropdown {
    z-index: 10001 !important;
}

nav .dropdown-content {
    z-index: 10002 !important;
}

/* Nested dropdown styles */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu > .dropdown-content {
    display: none !important; /* Hidden by default with !important */
    position: absolute;
    top: 0;
    left: 100%;
    margin-top: 0;
    margin-left: 0;
    border-radius: 4px;
    background-color: #f9f9f9;
    width: 220px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 9999 !important; /* Much higher z-index to prevent overlap */
}

/* Show submenu ONLY when active class is added (click-based) */
.dropdown-submenu.active > .dropdown-content {
    display: block !important;
    position: absolute !important;
    left: 100% !important;
    top: 0 !important;
    z-index: 10004 !important;
    pointer-events: auto !important;
}

/* Force enable pointer events for all submenu elements */
.dropdown-submenu,
.dropdown-submenu *,
.dropdown-submenu .dropdown-content,
.dropdown-submenu .dropdown-content a {
    pointer-events: auto !important;
}

/* Completely disable hover behavior for submenus */
.dropdown-submenu:hover > .dropdown-content {
    display: none !important;
}

/* Hide all submenu content by default */
.dropdown-submenu .dropdown-content {
    display: none !important;
}

/* Only show when explicitly activated */
.dropdown-submenu.active .dropdown-content {
    display: block !important;
}

/* Override any other CSS that might show submenus */
.dropdown .dropdown-submenu .dropdown-content,
.nav-links .dropdown-submenu .dropdown-content,
ul .dropdown-submenu .dropdown-content {
    display: none !important;
}

.dropdown .dropdown-submenu.active .dropdown-content,
.nav-links .dropdown-submenu.active .dropdown-content,
ul .dropdown-submenu.active .dropdown-content {
    display: block !important;
}

.dropdown-submenu {
    position: relative !important;
    pointer-events: auto !important;
}

/* Ensure submenu content doesn't block clicks */
.dropdown-submenu > .dropdown-content {
    pointer-events: auto !important;
}

.dropdown-submenu > a:after {
    content: "\f105"; /* FontAwesome right arrow */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    float: right;
    margin-left: 10px;
}

/* Submenu header styling */
.submenu-header {
    font-weight: bold;
    color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.1);
    cursor: pointer;
    position: relative;
}

.submenu-header:hover {
    background-color: rgba(var(--primary-color-rgb), 0.15) !important;
    color: var(--primary-color) !important;
}

.dropbtn {
    display: flex;
    align-items: center;
    gap: 5px;
    justify-content: flex-start;
    text-align: left;
}

.dropbtn i {
    font-size: 0.8rem;
}

/* Compact Nav Buttons */
.compact-nav-button {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    box-shadow: none;
    border-radius: 4px;
    width: 44px; /* Match mobile menu toggle size */
    height: 44px; /* Match mobile menu toggle size */
}

/* Theme toggle button dark mode styles */
.dark-mode .theme-toggle-button {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.compact-nav-button:hover, .theme-toggle-button:hover, .highlight-toggle-button:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(var(--primary-color-rgb), 0.3);
}

.compact-nav-button i, .theme-toggle-button i, .highlight-toggle-button i, .mobile-menu-toggle i {
    font-size: 1.2rem;
    margin-right: 0;
}

/* Highlight button when active - using !important to override other styles */
#compact-toggle-highlight.active-tool {
    background-color: var(--highlight-color) !important;
    color: black !important;
    font-weight: bold !important;
    border-color: var(--highlight-color) !important;
    box-shadow: 0 0 5px var(--highlight-color) !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5) !important;
}

/* Ensure highlight button in dark mode also shows yellow background when active */
.dark-mode #compact-toggle-highlight.active-tool {
    background-color: var(--highlight-color) !important;
    color: black !important;
    font-weight: bold !important;
    border-color: var(--highlight-color) !important;
    box-shadow: 0 0 5px var(--highlight-color) !important;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5) !important;
}

/* Main Content Styles */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Video Page Styles */
.video-container {
    max-width: 95vw;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Audio Player Styles */
.audio-player-container {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: flex-start;
}

.audio-play-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.audio-play-button i {
    margin-right: 0.5rem;
}

.audio-play-button:hover {
    background-color: #002a66;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.audio-play-button.playing {
    background-color: #e74c3c;
}

.audio-play-button.playing:hover {
    background-color: #c0392b;
}

/* Dark mode styles for audio player */
.dark-mode .audio-play-button {
    background-color: var(--primary-color);
    color: white;
}

.dark-mode .audio-play-button:hover {
    background-color: #1a4a8f;
}

.dark-mode .audio-play-button.playing {
    background-color: #e74c3c;
}

.dark-mode .audio-play-button.playing:hover {
    background-color: #c0392b;
}

.video-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    text-align: center;
}

.video-description {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.video-playlist-container {
    background-color: var(--accent-color);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 12px var(--shadow-color);
    margin-bottom: 2rem;
}

.video-playlist-container h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.video-embed {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    box-shadow: 0 4px 8px var(--shadow-color);
    width: 100%; /* Ensure full width */
}

.video-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 6px;
    border: none; /* Remove border */
    z-index: 2; /* Ensure iframe is clickable */
    pointer-events: auto; /* Ensure clicks are registered */
}

.video-info {
    font-style: italic;
    color: var(--text-color);
    opacity: 0.8;
}

/* YouTube direct link for mobile users */
.mobile-youtube-link {
    margin-top: 1.5rem;
    text-align: center;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Hide mobile YouTube link on desktop */
@media (min-width: 769px) {
    .mobile-youtube-link {
        display: none;
    }
}

/* Show mobile YouTube link and hide iframe on small screens */
@media (max-width: 768px) {
    .video-embed {
        margin-bottom: 0.5rem;
    }
}

.youtube-direct-link {
    display: inline-block;
    background-color: #FF0000; /* YouTube red */
    color: white !important;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.youtube-direct-link:hover {
    background-color: #CC0000; /* Darker red on hover */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: white !important;
}

.youtube-direct-link i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.mobile-note {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Dark mode styles for YouTube link */
.dark-mode .youtube-direct-link {
    background-color: #FF0000; /* Keep YouTube red */
    color: white !important;
}

.dark-mode .youtube-direct-link:hover {
    background-color: #CC0000;
}

.dark-mode .mobile-note {
    color: #aaa;
}

.dark-mode .mobile-youtube-link {
    border-top: 1px solid var(--border-color);
}

/* Dark mode styles for video page */
.dark-mode .video-playlist-container {
    background-color: var(--item-bg);
    border: 1px solid var(--border-color);
}

.dark-mode .video-embed {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

section {
    margin-bottom: 3rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

p {
    margin-bottom: 1rem;
}

/* Structure Section */
.structure-list {
    background-color: var(--accent-color);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.structure-list li {
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
}

.structure-list li:before {
    content: "•";
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Categories Section */
.categories-section {
    margin-top: 2rem;
}

.category-tabs {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    background-color: transparent;
    border: none;
    padding: 0.8rem 1.5rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
}

.category-content {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.category-header h2 {
    margin-bottom: 0;
}

.category-description {
    margin-bottom: 2rem;
}

/* Chapter Grid */
.chapter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.chapter-card {
    background-color: var(--secondary-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
}

.chapter-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.chapter-header {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 1rem;
    font-weight: bold;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.chapter-number {
    color: var(--secondary-color);
    font-weight: bold;
    margin-right: 0.8rem;
    font-size: 1rem;
}

.chapter-body {
    flex-grow: 1;
}

.chapter-footer {
    padding: 0.8rem 1rem;
    background-color: var(--accent-color);
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

/* Category-specific colors */
.daily-life .chapter-header {
    background-color: var(--category-daily);
}

.web-development .chapter-header {
    background-color: var(--category-web);
}

.cleaner .chapter-header {
    background-color: var(--category-cleaner);
}

.kitchen-assistant .chapter-header {
    background-color: var(--category-kitchen);
}

.warehouse .chapter-header {
    background-color: var(--category-warehouse);
}

/* About Section */
.about-section {
    background-color: var(--accent-color);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

/* Footer Styles */
footer {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 3rem 0 2rem;
    margin-top: 3rem;
    position: relative;
}

.dark-mode footer {
    background-color: var(--footer-bg);
    color: #aaaaaa;
    border-top: 1px solid #222222;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        var(--category-daily) 0%,
        var(--category-web) 20%,
        var(--category-cleaner) 40%,
        var(--category-kitchen) 60%,
        var(--category-warehouse) 80%,
        var(--category-technology) 100%);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section {
    text-align: left;
}

.footer-section h3 {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin-bottom: 1.2rem;
    position: relative;
    padding-bottom: 0.8rem;
}

.dark-mode .footer-section h3 {
    color: #ffffff;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
    opacity: 0.5;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.7rem;
}

.footer-links a {
    color: var(--secondary-color);
    opacity: 0.8;
    transition: opacity 0.2s, transform 0.2s;
    display: inline-block;
    text-decoration: none;
}

.dark-mode .footer-links a {
    color: #888888;
}

.dark-mode .footer-links a:hover {
    color: #ffffff;
}

.footer-links a:hover {
    opacity: 1;
    transform: translateX(5px);
}

.footer-contact p {
    margin-bottom: 0.7rem;
    display: flex;
    align-items: center;
    opacity: 0.8;
}

.dark-mode .footer-contact p {
    color: #888888;
}

.footer-contact i {
    margin-right: 0.8rem;
    width: 16px;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--secondary-color);
    transition: all 0.3s;
}

.dark-mode .social-icon {
    background-color: rgba(255, 255, 255, 0.05);
    color: #aaaaaa;
}

.social-icon:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    grid-column: 1 / -1;
}

.dark-mode .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.copyright {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Load More Button */
.load-more-container {
    grid-column: 1 / -1;
    text-align: center;
    margin: 1.5rem 0;
}

.load-more-btn {
    background-color: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.load-more-btn:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.load-more-btn i {
    font-size: 0.8rem;
}

/* Chapter Page Styles */
/* Chapter Body */
.chapter-body {
    background-color: #f9f9f9;
}

/* Chapter Header Banner */
.chapter-header-banner {
    background-color: var(--primary-color);
    background-image: linear-gradient(135deg, var(--primary-color), var(--primary-color) 60%, rgba(0, 53, 128, 0.85));
    color: var(--secondary-color);
    padding: 0.8rem 0;
    margin-bottom: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.chapter-header-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="10" height="10" fill="rgba(255,255,255,0.03)"/></svg>');
    background-size: 20px 20px;
    opacity: 0.5;
}

.chapter-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.chapter-category-badge {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    padding: 0.2rem 0.7rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
}

.chapter-meta-compact {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
}

.header-buttons {
    display: flex;
    gap: 0.5rem;
}

.compact-header-button {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--secondary-color);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    cursor: pointer;
}

.compact-header-button:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

.compact-header-button i {
    font-size: 0.8rem;
}

/* Chapter Title Container */
.chapter-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1.5rem 0 2rem 0;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.chapter-title-container h1 {
    font-size: 1.8rem;
    margin: 0;
    font-family: 'Roboto Slab', serif;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    flex: 1;
    padding: 0 10px;
}

.chapter-number-badge {
    background-color: rgba(255, 255, 255, 0.25);
    padding: 0.2rem 0.6rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.75rem;
}

.chapter-reading-time, .chapter-vocabulary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-nav-button {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    margin: 0 12px;
}

.header-nav-button:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.header-nav-button i {
    font-size: 0.85rem;
}

/* Chapter Layout */
.chapter-layout-full {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.chapter-main-full {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
    padding: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.04);
}

/* Chapter Header Navigation */
.chapter-header-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: nowrap;
}

/* Table of Contents Dropdown */
#table-of-contents-dropdown {
    position: relative;
    display: inline-block;
}

.toc-button {
    background-color: var(--accent-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 30px;
    padding: 0.8rem 1.2rem;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.toc-button:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.toc-dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 250px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
    left: 50%;
    transform: translateX(-50%);
    top: 100%;
    margin-top: 10px;
}

#table-of-contents-dropdown:hover .toc-dropdown-content,
.toc-dropdown-content.show {
    display: block;
}

.chapter-navigation-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-top: 1.2rem;
}

.nav-button {
    background-color: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: 30px;
    padding: 0.8rem 1.2rem;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.nav-button:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.nav-button i {
    font-size: 0.85rem;
}

#prev-chapter i, #prev-chapter-bottom i {
    margin-right: 0.5rem;
}

#next-chapter i, #next-chapter-bottom i {
    margin-left: 0.5rem;
}

/* Table of Contents */
.toc {
    font-size: 0.9rem;
}

.toc-placeholder {
    color: #888;
    font-style: italic;
}

.toc-item {
    padding: 0.4rem 0;
    cursor: pointer;
    transition: color 0.2s;
    display: block;
    text-decoration: none;
    color: var(--text-color);
}

.toc-item:hover {
    color: var(--primary-color);
}

.toc-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.toc-h2 {
    padding-left: 1rem;
    font-size: 0.85rem;
}

.toc-h3 {
    padding-left: 2rem;
    font-size: 0.8rem;
    color: #666;
}

/* Learning Tools */
.tool-button {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    width: 100%;
    padding: 0.8rem 1.2rem;
    margin-bottom: 1rem;
    background-color: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.95rem;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.tool-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(0, 53, 128, 0.1);
    transition: width 0.3s ease;
    z-index: 0;
}

.tool-button:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.tool-button:hover::before {
    width: 100%;
}

.tool-button i {
    position: relative;
    z-index: 1;
    font-size: 1.1rem;
}

.tool-button span {
    position: relative;
    z-index: 1;
}

.tool-button:last-child {
    margin-bottom: 0;
}

/* Main Content */
.chapter-main {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    padding: 2.5rem;
    min-height: 70vh;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.chapter-progress-container {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: #eee;
    z-index: 10;
    margin: -2.5rem -2.5rem 2.5rem -2.5rem;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
}

.chapter-progress-bar {
    height: 100%;
    width: 0;
    background-color: var(--primary-color);
    background-image: linear-gradient(to right, var(--primary-color), #1a73e8);
    transition: width 0.3s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chapter-content {
    font-size: 1.05rem;
    line-height: 1.8;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
}

.chapter-content h1,
.chapter-content h2,
.chapter-content h3 {
    color: var(--primary-color);
    font-family: 'Roboto Slab', serif;
    margin-top: 2.5rem;
    margin-bottom: 1.2rem;
    position: relative;
    line-height: 1.4;
}

.chapter-content h1 {
    font-size: 2.2rem;
    border-bottom: 2px solid rgba(0, 53, 128, 0.1);
    padding-bottom: 0.8rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.chapter-content h2 {
    font-size: 1.6rem;
    padding-left: 1.2rem;
}

.chapter-content h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 5px;
    height: 1.6rem;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.chapter-content h3 {
    font-size: 1.3rem;
    color: #444;
}

/* Compact Vocabulary and Grammar Sections */
.compact-section {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    padding: 1.2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.compact-section h2[id="grammar-points-kielioppi"] {
    margin-top: 1.5rem;
}

.dark-mode .compact-section {
    background-color: rgba(255, 255, 255, 0.03);
}

/* Vocabulary section specific styling */
.compact-section h2[id="vocabulary-sanasto"] {
    border-bottom-color: var(--vocabulary-color);
}

.compact-section h2[id="vocabulary-sanasto"] i {
    color: var(--vocabulary-color);
}

/* Grammar section specific styling */
.compact-section h2[id="grammar-points-kielioppi"] {
    border-bottom-color: var(--grammar-color);
}

.compact-section h2[id="grammar-points-kielioppi"] i {
    color: var(--grammar-color);
}

.compact-section h2 {
    font-size: 1.4rem;
    margin-top: 0 !important;
    margin-bottom: 0.8rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.compact-section h2 i {
    margin-right: 0.5rem;
    opacity: 0.7;
}

.compact-section .translation-grid {
    margin-top: 0;
    padding-top: 0;
}

.compact-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.2rem;
    width: 100%;
    margin-bottom: 0;
    padding: 0.5rem 0;
    grid-auto-rows: minmax(min-content, max-content);
}

/* Medium screens and larger - 2 columns */
@media (min-width: 768px) {
    .compact-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.compact-item {
    margin-bottom: 0.5rem;
}

.translation-grid {
    display: grid;
    grid-template-columns: repeat(1, minmax(200px, 1fr));
    gap: 0.4rem;
    width: 100%;
    margin-bottom: 0;
    padding: 0;
    grid-auto-rows: minmax(min-content, max-content);
}

/* Medium screens - 2 columns */
@media (min-width: 768px) {
    .translation-grid {
        grid-template-columns: repeat(2, minmax(180px, 1fr));
    }
}

/* Large screens - 3 columns */
@media (min-width: 1200px) {
    .translation-grid {
        grid-template-columns: repeat(2, minmax(160px, 1fr));
    }
}

.translation-pair {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    padding: 0.4rem 0.6rem;
    margin-bottom: 0.3rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s;
    font-size: 0.95rem;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    width: 100%;
    box-sizing: border-box;
    min-height: 2rem;
    white-space: nowrap;
    min-width: 0; /* Ensure the grid item can shrink below its content size */
}

.dark-mode .translation-pair {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .translation-pair:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.translation-pair:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    z-index: 10;
    position: relative;
    background-color: rgba(255, 255, 255, 0.95);
}

.translation-pair:hover .finnish-term,
.translation-pair:hover .english-term {
    white-space: normal;
    word-break: break-word;
    display: inline-flex;
    align-items: center;
}

.finnish-term {
    font-weight: 600;
    color: var(--primary-color);
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    font-size: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.finnish-term::after {
    content: ": ";
    margin-right: 0.3rem;
}

.item-number {
    color: #888;
    font-weight: normal;
    margin-right: 0.3rem;
    font-size: 0.85rem;
}

.dark-mode .item-number {
    color: #aaa;
}

.english-term {
    color: var(--text-color);
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}

.grammar-markdown-list {
    padding: 0.5rem 0.5rem 1rem 0.5rem;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
}

.grammar-list-item {
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
}

.grammar-list-item:last-child {
    margin-bottom: 0;
}

.grammar-point-title {
    margin-bottom: 0.5rem;
    font-size: 1.05rem;
    color: var(--grammar-color);
}

.grammar-list-item ul {
    margin-top: 0.3rem;
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    list-style-type: disc;
}

.grammar-list-item li {
    margin-bottom: 0.4rem;
    line-height: 1.5;
}

.grammar-example {
    font-style: italic;
    margin: 0.5rem 0 0.7rem 1rem;
    padding-left: 1rem;
    border-left: 3px solid var(--grammar-color);
    color: var(--text-color);
    opacity: 0.9;
    font-size: 0.95rem;
}

.dark-mode .grammar-example {
    border-left-color: rgba(var(--grammar-color-rgb), 0.7);
}

.dark-mode .grammar-point-title {
    color: rgba(var(--grammar-color-rgb), 0.9);
}

@media (max-width: 576px) {
    .translation-grid {
        grid-template-columns: repeat(1, 1fr);
        gap: 0.3rem;
    }

    .compact-grid {
        grid-template-columns: 1fr;
    }

    .translation-pair {
        padding: 0.5rem 0.7rem;
        margin-bottom: 0.4rem;
        font-size: 0.9rem;
        min-height: 2.2rem;
        display: flex;
        align-items: center;
    }
    
    .finnish-term {
        font-size: 0.95rem;
        margin-right: 0.3rem;
        display: inline-flex;
        align-items: center;
    }
    
    .grammar-markdown-list {
        padding: 0.3rem 0.3rem 0.8rem 0.3rem;
        font-size: 0.95rem;
    }
    
    .grammar-list-item {
        margin-bottom: 1.2rem;
    }
    
    .grammar-point-title {
        font-size: 1rem;
    }

    .finnish-term::after {
        margin-right: 0.3rem;
    }

    .english-term {
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
        vertical-align: middle;
    }

    .grammar-example {
        margin: 0.4rem 0 0.6rem 0.5rem;
        padding-left: 0.8rem;
        font-size: 0.9rem;
    }
    
    .grammar-list-item ul {
        padding-left: 1.2rem;
    }
    
    .grammar-list-item li {
        margin-bottom: 0.3rem;
    }

    .item-number {
        font-size: 0.7rem;
    }

    .compact-section {
        padding: 0.8rem;
    }

    .compact-section h2 {
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }
}

.chapter-content p {
    margin-bottom: 1.4rem;
}

.chapter-content ul,
.chapter-content ol {
    margin-left: 1.8rem;
    margin-bottom: 1.8rem;
    padding-left: 0.5rem;
}

.chapter-content li {
    margin-bottom: 0.8rem;
    padding-left: 0.3rem;
}

.chapter-content strong {
    color: #222;
    font-weight: 600;
}

.chapter-content em {
    color: #555;
    font-style: italic;
}

/* Section styling */
.chapter-content h2[id*="objectives"] {
    color: var(--objectives-color);
}

.chapter-content h2[id*="objectives"]::before {
    background-color: var(--objectives-color);
}

.chapter-content h2[id*="vocabulary"] {
    color: var(--vocabulary-color);
}

.chapter-content h2[id*="vocabulary"]::before {
    background-color: var(--vocabulary-color);
}

.chapter-content h2[id*="grammar"] {
    color: var(--grammar-color);
}

.chapter-content h2[id*="grammar"]::before {
    background-color: var(--grammar-color);
}

.chapter-content h2[id*="script"] {
    color: var(--script-color);
}

.chapter-content h2[id*="script"]::before {
    background-color: var(--script-color);
}

.chapter-content h2[id*="cultural"] {
    color: var(--cultural-color);
}

.chapter-content h2[id*="cultural"]::before {
    background-color: var(--cultural-color);
}

/* Old Finnish-English Translation Pairs - Removed to avoid conflicts */

/* Highlighted Keywords */
.keyword {
    background-color: rgba(0, 53, 128, 0.08);
    padding: 0.1rem 0.4rem;
    border-radius: 4px;
    transition: all 0.2s;
    font-weight: 500;
    position: relative;
    display: inline-block;
    border-bottom: 1px dashed rgba(0, 53, 128, 0.3);
}

.keyword:hover {
    background-color: rgba(0, 53, 128, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Chapter Footer Navigation */
.chapter-footer-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    position: relative;
    flex-wrap: nowrap;
}

.chapter-footer-navigation::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.back-to-home {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    background-color: var(--accent-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    white-space: nowrap;
    text-align: center;
}

.back-to-home:hover {
    color: var(--primary-color);
    background-color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

/* Loader */
.loader {
    border: 5px solid var(--accent-color);
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error {
    color: #d32f2f;
    font-weight: bold;
    padding: 1rem;
    background-color: #ffebee;
    border-radius: 4px;
    margin-bottom: 1rem;
}

/* Dark Mode */
.dark-mode {
    --primary-color: #1a73e8;
    --secondary-color: #121212;
    --accent-color: #1e1e1e;
    --text-color: #e0e0e0;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

.dark-mode .chapter-body {
    background-color: #121212;
}

.dark-mode .chapter-main,
.dark-mode .sidebar-section {
    background-color: #1e1e1e;
}

.dark-mode .chapter-content {
    color: #e0e0e0;
}

.dark-mode .translation-pair {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .translation-pair:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-mode .finnish-term {
    color: var(--primary-color);
}

.dark-mode .english-term {
    color: #bbb;
}

.dark-mode .chapter-progress-container {
    background-color: #333;
}

.dark-mode .keyword {
    background-color: rgba(26, 115, 232, 0.2);
}

.dark-mode .keyword:hover {
    background-color: rgba(26, 115, 232, 0.3);
}

/* Responsive Styles */
/* Hide highlight button on tablet and mobile */
@media (max-width: 992px) {
    .highlight-button-container {
        display: none !important;
    }

    /* Adjust theme toggle button for tablet */
    .theme-toggle-container {
        position: absolute;
        right: 1rem;
        top: 0.8rem;
    }

    .theme-toggle-button {
        font-size: 1.2rem;
    }
    /* Chapter Layout */
    .chapter-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .chapter-sidebar {
        position: relative;
        top: 0;
        height: auto;
        margin-bottom: 1.5rem;
    }

    .sidebar-section {
        margin-bottom: 1rem;
    }

    .chapter-navigation-buttons {
        flex-direction: row;
    }

    .nav-button {
        width: auto;
    }

    /* Improve navigation buttons on tablets */
    .chapter-header-navigation,
    .chapter-footer-navigation {
        padding: 0.5rem 0;
    }

    .chapter-header-navigation .nav-button,
    .chapter-header-navigation .toc-button,
    .chapter-footer-navigation .nav-button,
    .chapter-footer-navigation .back-to-home {
        min-width: 36px;
        width: auto;
        padding: 0.5rem 0.75rem;
    }

    .chapter-header-navigation .nav-button .button-text,
    .chapter-header-navigation .toc-button .button-text,
    .chapter-footer-navigation .nav-button .button-text,
    .chapter-footer-navigation .back-to-home .button-text {
        font-size: 0.9rem;
    }
}

/* Mobile menu toggle button */
.mobile-menu-toggle {
    display: none; /* Hidden by default, shown in media query */
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0.5rem;
    margin-left: auto;
    position: relative;
    z-index: 1001; /* Higher z-index to ensure it's above the menu */
    transition: all 0.3s ease;
    width: 44px; /* Fixed width for better touch target */
    height: 44px; /* Fixed height for better touch target */
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.2); /* Add tap highlight color for mobile */
    touch-action: manipulation; /* Improve touch handling */
}

/* Add active state for touch */
.mobile-menu-toggle:active,
.mobile-menu-toggle.clicked {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.95);
}

/* Dark mode styles for mobile menu toggle */
.dark-mode .mobile-menu-toggle {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu-toggle:hover, .mobile-menu-toggle:focus {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(var(--primary-color-rgb), 0.3);
    outline: none;
}

.mobile-menu-toggle:active {
    transform: scale(0.95);
}

/* Dark mode styles for mobile menu toggle */
.dark-mode .mobile-menu-toggle {
    color: var(--text-color);
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex; /* Use flex to center the icon */
    }

    .nav-container {
        flex-direction: row;
        padding: 0.8rem 1rem;
        justify-content: space-between;
        align-items: center;
        position: relative; /* Ensure proper positioning of children */
        min-height: 60px; /* Fixed height for consistency */
    }

    .logo {
        margin-bottom: 0;
        width: auto;
        text-align: left;
    }

    /* Position theme toggle button for mobile */
    .mobile-only-theme-toggle {
        display: flex;
        z-index: 1001; /* Ensure it's above other elements */
        margin-left: auto; /* Push to the right */
        margin-right: 10px; /* Add some space between this and hamburger menu */
    }

    /* On mobile, hide the theme toggle and highlight toggle in the nav links */
    .nav-links .theme-toggle-container,
    .nav-links .highlight-toggle-container {
        display: none; /* Hide the ones in the nav links */
    }

    /* Ensure mobile menu toggle has proper spacing */
    .mobile-menu-toggle {
        display: flex !important; /* Force display as flex */
        z-index: 10000; /* Even higher z-index than the menu */
        background-color: rgba(0, 0, 0, 0.05); /* Light background for visibility */
        border: 1px solid rgba(0, 0, 0, 0.1); /* Light border for visibility */
        border-radius: 4px; /* Rounded corners */
        padding: 10px; /* Larger padding for better touch target */
        width: 44px; /* Fixed width for better touch target */
        height: 44px; /* Fixed height for better touch target */
        align-items: center; /* Center icon vertically */
        justify-content: center; /* Center icon horizontally */
        margin-left: 10px; /* Add margin to separate from other elements */
    }

    /* Dark mode styles for mobile menu toggle in media query */
    .dark-mode .mobile-menu-toggle {
        background-color: rgba(255, 255, 255, 0.05); /* Dark background for visibility */
        border: 1px solid rgba(255, 255, 255, 0.1); /* Dark border for visibility */
    }

    /* Mobile styles for h3 headings */
    .h3-container h3 {
        margin-bottom: 10px;
        width: 100%;
    }

    .nav-links {
        margin-top: 0;
        display: none; /* Hide by default */
        flex-direction: column;
        width: 100%;
        padding: 0;
        position: absolute;
        top: 60px; /* Fixed position from top of navbar */
        left: 0;
        right: 0;
        background-color: var(--secondary-color);
        z-index: 9999; /* Very high z-index to ensure it's above ALL other elements */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        border-top: 1px solid var(--border-color);
    }

    /* Dark mode styles for mobile menu */
    .dark-mode .nav-links {
        background-color: var(--secondary-color); /* Use secondary color for consistency */
        border-top: 1px solid var(--border-color);
    }

    .nav-links.show {
        display: flex !important; /* Show when toggled with !important to override any other styles */
        flex-direction: column; /* Ensure items stack vertically */
        align-items: flex-start; /* Align items to the left */
        width: 100%;
        padding: 1rem 0;
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .nav-links li {
        margin: 0.3rem 0;
        width: 100%;
        text-align: left;
        padding: 0 0.5rem;
    }

    /* Put highlight and dark mode buttons side by side */
    .nav-links li:nth-last-child(2),
    .nav-links li:last-child {
        width: 40%;
        display: inline-block;
        vertical-align: middle;
        text-align: center;
    }

    /* Make dropdown work better on mobile */
    .dropdown-content {
        position: static;
        display: none;
        width: 100%; /* Full width on mobile */
        box-shadow: none;
        margin: 0.5rem 0 0.5rem 0; /* Remove margin */
        padding-left: 20px; /* Add padding for indentation */
        text-align: left;
        left: 0;
        right: 0;
        transform: none;
        animation: none; /* Remove animation to prevent issues */
        border-left: 2px solid var(--primary-color); /* Add a left border for visual indication */
        background-color: transparent; /* Transparent background */
    }

    /* Show dropdown when active class is added */
    .dropdown.active .dropdown-content {
        display: block;
    }

    /* Dark mode styles for dropdown */
    .dark-mode .dropdown-content {
        border-left: 2px solid var(--primary-color);
        background-color: transparent;
    }

    @keyframes slideDownMobile {
        from {
            opacity: 0;
            max-height: 0;
            overflow: hidden;
        }
        to {
            opacity: 1;
            max-height: 500px;
            overflow: hidden;
        }
    }

    .dropdown-content a {
        text-align: left;
        padding: 10px 5px 10px 10px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        display: block;
        width: 100%; /* Full width on mobile */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 0.95rem; /* Slightly smaller font */
    }

    /* Dark mode styles for dropdown links */
    .dark-mode .dropdown-content a {
        border-bottom: 1px solid rgba(255,255,255,0.05);
    }

    /* Video page styles */
    .video-container {
        position: relative;
        z-index: 1; /* Lower z-index than navigation */
        padding-top: 1rem;
    }

    .video-playlist-container {
        margin: 2rem 0;
        background-color: var(--secondary-color);
        border-radius: 8px;
        box-shadow: 0 2px 10px var(--shadow-color);
        overflow: hidden;
        position: relative;
        z-index: 1; /* Lower z-index than navigation */
    }

    .video-embed {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        height: 0;
        overflow: hidden;
        z-index: 1; /* Lower z-index than navigation */
        width: 100%;
    }

    .video-embed iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        z-index: 2; /* Higher z-index to ensure clickability */
        pointer-events: auto; /* Ensure clicks are registered */
    }

    .video-info {
        padding: 1.5rem;
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--text-color);
    }

    .dropdown-content a:last-child {
        border-bottom: none;
    }

    .dropdown:hover .dropdown-content {
        display: none; /* Don't show on hover for mobile */
    }

    /* Show dropdown content when active class is added */
    .dropdown.active .dropdown-content {
        display: block !important;
        animation: slideDownMobile 0.3s ease forwards;
    }

    /* Make buttons more touch-friendly but not too large */
    .compact-nav-button {
        padding: 0.4rem 0.6rem;
        margin: 0.4rem auto;
        min-width: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        max-width: 150px;
        font-size: 0.85rem;
    }

    /* Adjust buttons when side by side */
    .nav-links li:nth-last-child(2) .compact-nav-button,
    .nav-links li:last-child .compact-nav-button {
        margin: 0.4rem 0.2rem;
        font-size: 0.75rem;
        padding: 0.4rem 0.3rem;
        white-space: nowrap;
        min-width: 60px;
    }

    .category-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .category-header p {
        margin-top: 0.5rem;
    }

    .chapter-grid {
        grid-template-columns: 1fr;
    }

    /* Chapter Header */
    .chapter-header-banner {
        padding: 0.6rem 0;
    }

    .chapter-header-content {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .chapter-meta-compact {
        order: 2;
    }

    .header-buttons {
        order: 3;
    }

    .chapter-category-badge {
        order: 1;
        width: 100%;
        justify-content: center;
        margin-bottom: 0.3rem;
    }

    .chapter-title-section {
        padding: 0.8rem 0;
    }

    .chapter-title-container h1 {
        font-size: 1.4rem;
    }

    /* Chapter Content */
    .chapter-main {
        padding: 1.5rem;
    }

    .chapter-content {
        font-size: 1rem;
    }

    .chapter-content h1 {
        font-size: 1.7rem;
    }

    .chapter-content h2 {
        font-size: 1.4rem;
    }

    .chapter-content h3 {
        font-size: 1.1rem;
    }

    /* Translation Pairs */
    .translation-pair {
        flex-direction: column;
    }

    .finnish-term {
        border-right: none;
        border-bottom: 1px solid #eee;
    }

    /* Footer Navigation */
    .chapter-footer-navigation {
        flex-direction: row;
        gap: 0.5rem;
        justify-content: center;
        align-items: center;
    }

    .back-to-home {
        order: 0;
        margin: 0;
    }
}

@media (max-width: 480px) {
    /* Improve h3-container layout on mobile */
    .h3-container h3 {
        font-size: 1.2rem;
    }

    .audio-play-button {
        padding: 6px 12px;
        min-width: 70px;
        height: 32px;
        font-size: 0.85rem;
    }

    .audio-play-button i {
        font-size: 0.9rem;
    }
    .header-content h1 {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .tab-button {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    /* Enhance YouTube direct link on mobile */
    .youtube-direct-link {
        display: block;
        width: 100%;
        padding: 1rem;
        font-size: 1.1rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .mobile-youtube-link {
        margin-top: 1rem;
        padding: 1rem 0.5rem;
    }

    .mobile-note {
        font-size: 0.85rem;
        padding: 0 0.5rem;
    }

    /* Hide text in navigation buttons on mobile */
    .chapter-header-navigation .nav-button .button-text,
    .chapter-footer-navigation .nav-button .button-text,
    .chapter-footer-navigation .back-to-home .button-text {
        display: none !important;
    }

    /* Make icons more visible */
    .chapter-header-navigation .nav-button i,
    .chapter-footer-navigation .nav-button i,
    .chapter-footer-navigation .back-to-home i,
    .chapter-header-navigation .toc-button i {
        font-size: 1.2rem;
        position: relative;
        z-index: 2;
        margin: 0 !important;
    }

    /* Only show icons in navigation buttons */
    .chapter-header-navigation .nav-button,
    .chapter-footer-navigation .nav-button,
    .chapter-footer-navigation .back-to-home {
        padding: 0.5rem;
        min-width: 40px;
        width: 40px;
        height: 40px;
        justify-content: center;
        position: relative;
        z-index: 5;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(0,0,0,0.2);
        touch-action: manipulation;
        overflow: hidden;
        white-space: nowrap;
    }

    .chapter-header-navigation .toc-button {
        padding: 0.5rem;
        min-width: 40px;
        width: 40px;
        height: 40px;
        position: relative;
        z-index: 5;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(0,0,0,0.2);
        touch-action: manipulation;
        overflow: hidden;
        white-space: nowrap;
    }

    .chapter-header-navigation .toc-button .button-text {
        display: none;
    }

    /* Improve mobile navigation */
    .nav-container {
        padding: 0.6rem 0.4rem;
    }

    .logo a {
        font-size: 1.1rem;
    }

    .nav-links {
        gap: 0.2rem;
    }

    .nav-links li {
        margin: 0.2rem 0.3rem;
    }

    .compact-nav-button {
        font-size: 0.75rem;
        padding: 0.35rem 0.5rem;
        min-width: 70px;
        max-width: 120px;
    }

    .dropdown-content {
        min-width: 180px;
        left: 30% !important;
    }

    /* Improve dropdown on mobile */
    .nav-links.show .dropdown-content {
        width: 90%;
        margin: 0 auto;
    }

    .dropdown-content a {
        padding: 10px;
        font-size: 0.85rem;
    }
}

    /* Chapter Header */
    .chapter-header-banner {
        padding: 0.5rem 0;
    }

    .chapter-category-badge {
        font-size: 0.7rem;
    }

    .chapter-meta-compact {
        font-size: 0.7rem;
        gap: 0.6rem;
    }

    .compact-header-button {
        width: 28px;
        height: 28px;
    }

    .chapter-title-section {
        padding: 0.7rem 0;
    }

    .chapter-title-container {
        margin: 1rem 0 1.5rem 0;
        padding-bottom: 0.8rem;
    }

    .chapter-title-container h1 {
        font-size: 1.2rem;
        padding: 0 5px;
    }

    .header-nav-button {
        width: 28px;
        height: 28px;
        margin: 0 5px;
    }

    .header-nav-button i {
        font-size: 0.75rem;
    }

    /* Chapter Content */
    .chapter-main {
        padding: 1rem;
    }

    .chapter-content {
        font-size: 0.95rem;
    }

    .chapter-content h1 {
        font-size: 1.5rem;
    }

    .chapter-content h2 {
        font-size: 1.3rem;
    }

    .chapter-content h2::before {
        left: -0.7rem;
        height: 1.2rem;
    }

    /* Learning Tools */
    .tool-button {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }

    /* Mobile audio button */
    .audio-play-button {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 80px;
    }

    .audio-player-container {
        padding: 0;
    }

    /* Mobile conversation */
    .conversation {
        padding: 1rem;
    }

    .h3-container {
        flex-direction: row;
        align-items: center;
        flex-wrap: nowrap;
    }

    .h3-container h3 {
        font-size: 1.3rem;
    }

    .h3-container .audio-player-container {
        margin-left: 1rem;
    }

    .audio-play-button {
        padding: 6px 14px;
        min-width: 80px;
        height: 32px;
        font-size: 0.9rem;
    }


/* Audio Player and Text-to-Speech Styles */
.audio-player-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 15px;
    padding: 0;
}

/* Conversation Styles */
.conversation {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--accent-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    position: relative;
}

/* Audio player in conversation */
.conversation .audio-player-container {
    margin-bottom: 0rem;
    display: flex;
    justify-content: flex-start;
}

.h3-container {
    display: block;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    min-height: 42px;
    transition: background-color 0.3s ease;
}

.h3-container.speaking {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-radius: 4px;
    padding: 0.5rem;
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.h3-container h3 {
    margin: 0;
    padding: 0;
    font-size: 1.5rem;
}

.audio-play-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 8px 18px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    width: auto;
    min-width: 90px;
    height: 36px;
    white-space: nowrap;
    flex-shrink: 0;
    letter-spacing: 0.5px;
}

.audio-play-button:hover {
    background-color: #0046c0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.audio-play-button.playing {
    background-color: #d32f2f;
    box-shadow: 0 2px 5px rgba(211, 47, 47, 0.4);
    animation: pulse 1.5s infinite;
}

.audio-play-button.playing:hover {
    background-color: #b71c1c;
    animation: none;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(211, 47, 47, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(211, 47, 47, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(211, 47, 47, 0);
    }
}

.audio-play-button i {
    font-size: 1rem;
    margin-right: 2px;
}

/* Dark mode styles for audio player */
.dark-mode .audio-player-container {
    background-color: #2a2a2a;
    border-color: #444;
}

.dark-mode .audio-play-button {
    background-color: #1a5fb4;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

.dark-mode .audio-play-button:hover {
    background-color: #0d47a1;
}

.dark-mode .audio-play-button.playing {
    background-color: #c62828;
}

.dark-mode .audio-play-button.playing:hover {
    background-color: #b71c1c;
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    /* Show mobile menu toggle button */
    .mobile-menu-toggle {
        display: block;
    }

    /* Hide navigation links by default on mobile */
    .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--secondary-color);
        box-shadow: 0 4px 8px var(--shadow-color);
        padding: 1rem;
        z-index: 100;
        align-items: flex-start;
    }

    /* Show navigation links when toggled */
    .nav-links.show {
        display: flex;
        align-items: flex-start;
    }

    /* Adjust navigation items for mobile */
    .nav-links li {
        margin: 0.5rem 0;
        width: 100%;
        text-align: left;
    }

    /* Adjust dropdown for mobile */
    .dropdown-content {
        position: static;
        display: none;
        box-shadow: none;
        width: 100%;
        padding-left: 1rem;
        text-align: left;
    }

    /* Show dropdown content when active */
    .dropdown.active .dropdown-content {
        display: block;
        float: left; /* Ensure it stays on the left */
        clear: both; /* Prevent other elements from floating beside it */
    }

    /* Adjust buttons for mobile */
    .compact-nav-button {
        width: 100%;
        text-align: left;
        justify-content: flex-start;
    }

    /* Ensure dropdown items are left-aligned */
    .dropdown-content a {
        text-align: left;
        padding-left: 1rem;
    }

    /* Style for active dropdown */
    .dropdown.active .dropbtn {
        background-color: rgba(var(--primary-color-rgb), 0.1);
        border-radius: 4px;
    }

    /* Improve mobile menu appearance */
    .nav-links.show {
        animation: slideDown 0.3s ease forwards;
        border-bottom: 1px solid var(--border-color);
    }

    /* Add animation for mobile menu */
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Style active page in mobile menu */
    .nav-links a.active {
        color: var(--primary-color);
        font-weight: bold;
    }

    /* Add dividers between mobile menu items */
    .nav-links li:not(:last-child) {
        border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);
        padding-bottom: 0.5rem;
    }
}

/* Highlight toggle button styles */
.highlight-toggle-button {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    outline: none !important;
}

.dark-mode .highlight-toggle-button {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.highlight-toggle-button:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(var(--primary-color-rgb), 0.3);
}

.highlight-toggle-button.active-tool {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(var(--primary-color-rgb), 0.5);
}

/* Unified Mobile Menu Styles for All Pages */
@media (max-width: 767px) {
    /* Common mobile menu toggle button styles */
    .mobile-menu-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
        position: absolute;
        right: 10px;
        width: 44px;
        height: 44px;
        background-color: rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        color: var(--text-color);
        font-size: 1.5rem;
        cursor: pointer;
        z-index: 10000;
        transition: all 0.3s ease;
    }
    
    .mobile-menu-toggle:hover,
    .mobile-menu-toggle:focus {
        background-color: rgba(0, 0, 0, 0.1);
        color: var(--primary-color);
    }
    
    .mobile-menu-toggle:active,
    .mobile-menu-toggle.active {
        transform: scale(0.95);
        background-color: var(--primary-color) !important;
        color: white !important;
        border-color: var(--primary-color) !important;
    }
    
    /* Dark mode mobile menu toggle */
    .dark-mode .mobile-menu-toggle {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: var(--text-color);
    }
    
    .dark-mode .mobile-menu-toggle:hover,
    .dark-mode .mobile-menu-toggle:focus {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--primary-color);
    }
    
    /* Mobile theme toggle positioning */
    .theme-toggle-container.mobile-only-theme-toggle {
        display: flex !important;
        position: absolute;
        right: 60px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10000;
    }
    
    /* Nav container positioning */
    .nav-container {
        position: relative !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1rem !important;
        width: 100% !important;
        min-height: 60px;
    }
    
    /* Unified nav-links styles */
    .nav-links {
        display: none !important;
        position: absolute !important;
        top: 60px !important;
        left: 0 !important;
        width: 100% !important;
        background-color: var(--bg-color, #fff) !important;
        z-index: 1000 !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        margin: 0 !important;
        padding: 0 !important;
        border-top: 1px solid var(--border-color, #ddd) !important;
    }
    
    /* Show nav-links when active */
    .nav-links.show {
        display: flex !important;
        flex-direction: column !important;
        animation: fadeIn 0.3s ease-in-out !important;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Nav links list items */
    .nav-links li {
        margin: 0 !important;
        width: 100% !important;
        text-align: left !important;
        border-bottom: 1px solid var(--border-color, #eee) !important;
    }
    
    .nav-links li:last-child {
        border-bottom: none !important;
    }
    
    /* Nav links anchors */
    .nav-links a {
        padding: 15px !important;
        width: 100% !important;
        display: block !important;
        color: var(--text-color, #333) !important;
        font-size: 16px !important;
        transition: all 0.2s ease !important;
    }
    
    .nav-links a:hover, 
    .nav-links a:focus {
        background-color: rgba(0, 0, 0, 0.05) !important;
        color: var(--primary-color, #003580) !important;
    }
    
    /* Dropdown content */
    .dropdown-content {
        position: static !important;
        display: none !important;
        box-shadow: none !important;
        width: 100% !important;
        padding: 0 !important;
        background-color: rgba(0, 0, 0, 0.02) !important;
        border-top: 1px solid var(--border-color, #eee) !important;
    }
    
    .dropdown-content a {
        padding: 12px 15px 12px 30px !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
    }
    
    .dropdown-content a:last-child {
        border-bottom: none !important;
    }
    
    /* Active dropdown */
    .dropdown.active .dropdown-content {
        display: block !important;
    }
    
    /* Dropdown button styling */
    .dropdown .dropbtn {
        position: relative !important;
    }
    
    .dropdown .dropbtn:after {
        content: '\f107' !important;
        font-family: 'Font Awesome 5 Free' !important;
        font-weight: 900 !important;
        position: absolute !important;
        right: 15px !important;
        transition: transform 0.3s ease !important;
    }
    
    .dropdown.active .dropbtn:after {
        transform: rotate(180deg) !important;
    }
    
    /* Dark mode adjustments */
    .dark-mode .nav-links {
        background-color: var(--secondary-color, #121212) !important;
        border-top: 1px solid var(--border-color, #333) !important;
    }
    
    .dark-mode .nav-links li {
        border-bottom: 1px solid var(--border-color, #333) !important;
    }
    
    .dark-mode .nav-links a:hover,
    .dark-mode .nav-links a:focus {
        background-color: rgba(255, 255, 255, 0.05) !important;
    }
    
    .dark-mode .dropdown-content {
        background-color: rgba(255, 255, 255, 0.03) !important;
        border-top: 1px solid var(--border-color, #333) !important;
    }
    
    .dark-mode .dropdown-content a {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
    }
}
/* Mobile dropdown scroll styles */
@media (max-width: 767px) {
    .dropdown.active .dropdown-content {
        max-height: 60vh !important; /* Limit height to 60% of viewport height */
        overflow-y: auto !important; /* Enable vertical scrolling */
    }

    /* Mobile nested dropdown styles */
    .dropdown-submenu > .dropdown-content {
        position: static !important;
        left: 0 !important;
        margin-left: 20px !important;
        padding-left: 15px !important;
        border-left: 2px solid var(--primary-color) !important;
        background-color: rgba(var(--primary-color-rgb), 0.05) !important;
        width: calc(100% - 20px) !important;
        box-shadow: none !important;
        z-index: auto !important;
    }

    .dropdown-submenu > a:after {
        content: "\f107" !important; /* FontAwesome down arrow for mobile */
        transform: rotate(0deg) !important;
        transition: transform 0.3s ease !important;
    }

    .dropdown-submenu.active > a:after {
        transform: rotate(180deg) !important;
    }

    .submenu-header {
        background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        border-left: 3px solid var(--primary-color) !important;
    }

    /* Show submenu when parent is active */
    .dropdown-submenu.active > .dropdown-content {
        display: block !important;
    }

    /* Submenu items styling */
    .dropdown-submenu .dropdown-content a {
        padding-left: 25px !important;
        font-size: 0.9rem !important;
        border-left: 1px solid rgba(var(--primary-color-rgb), 0.2) !important;
    }

    /* Completely disable hover on mobile */
    .dropdown-submenu:hover > .dropdown-content {
        display: none !important;
    }

    /* Force click-only behavior */
    .dropdown-submenu > .dropdown-content {
        display: none !important;
    }

    .dropdown-submenu.active > .dropdown-content {
        display: block !important;
    }

    /* Ensure mobile dropdown items are clickable */
    .nav-links.show .dropdown-content a {
        pointer-events: auto !important;
        touch-action: manipulation !important;
    }

    /* Improve touch targets for mobile */
    .nav-links.show .dropbtn,
    .nav-links.show .submenu-header {
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        padding: 12px 15px !important;
        touch-action: manipulation !important;
    }

    /* Make sure mobile dropdowns are visible */
    .nav-links.show .dropdown.active .dropdown-content {
        display: block !important;
        position: static !important;
        box-shadow: none !important;
        border: none !important;
        background-color: transparent !important;
    }

    /* Ensure submenu items work on mobile */
    .nav-links.show .dropdown-submenu {
        pointer-events: auto !important;
    }

    /* Hide all submenu content by default on mobile */
    .nav-links.show .dropdown-submenu .dropdown-content {
        display: none !important;
    }

    /* Only show submenu content when active */
    .nav-links.show .dropdown-submenu.active .dropdown-content {
        display: block !important;
        position: static !important;
        left: auto !important;
        top: auto !important;
        margin-left: 20px !important;
        pointer-events: auto !important;
        background-color: rgba(0, 0, 0, 0.05) !important;
        border-left: 3px solid var(--primary-color) !important;
        padding-left: 10px !important;
    }

    .nav-links.show .dropdown-submenu .dropdown-content a {
        pointer-events: auto !important;
        touch-action: manipulation !important;
        display: block !important;
        position: relative !important;
        z-index: 10005 !important;
    }
}
