﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indirect Questions in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa HyvÃ¤ltÃ¤</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Indirect Questions</span>
        </div>
        
        <section class="grammar-section">
            <h2>Indirect Questions in Finnish</h2>
            <p>Indirect questions (epäsuorat kysymykset) are questions embedded within statements or other questions. Unlike direct questions, they don't require an immediate answer and are often used to report what someone has asked or to express uncertainty. This page explains how to form and use indirect questions in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>WHAT ARE INDIRECT QUESTIONS?</h3>
            
            <div class="grammar-content">
                <p>Indirect questions are embedded questions that appear as part of a larger sentence. They differ from direct questions in several ways:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Direct Question</th>
                        <th>Indirect Question</th>
                    </tr>
                    <tr>
                        <td><span class="finnish">Missä sinä asut?</span><br><span class="english">Where do you live?</span></td>
                        <td><span class="finnish">Hän kysyi, missä sinä asut.</span><br><span class="english">He/she asked where you live.</span></td>
                    </tr>
                    <tr>
                        <td><span class="finnish">Mitä kello on?</span><br><span class="english">What time is it?</span></td>
                        <td><span class="finnish">En tiedä, mitä kello on.</span><br><span class="english">I don't know what time it is.</span></td>
                    </tr>
                    <tr>
                        <td><span class="finnish">Tuletko huomenna?</span><br><span class="english">Are you coming tomorrow?</span></td>
                        <td><span class="finnish">Hän kysyi, tuletko huomenna.</span><br><span class="english">He/she asked if you are coming tomorrow.</span></td>
                    </tr>
                </table>
                
                <p>In Finnish, indirect questions maintain much of the structure of direct questions, but they function as subordinate clauses within a larger sentence.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>FORMING INDIRECT QUESTIONS WITH QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <p>When a direct question contains a question word (kuka, mikä, missä, etc.), the indirect question keeps the same question word:</p>
                
                <h4>1. Direct question with a question word:</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p><span class="finnish">Kuka soitti?</span> <span class="english">Who called?</span></p>
                    <p><span class="finnish">Milloin elokuva alkaa?</span> <span class="english">When does the movie start?</span></p>
                </div>
                
                <h4>2. Corresponding indirect questions:</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Hän kysyi, missä sinä asut.</span> <span class="english">He/she asked where you live.</span></p>
                    <p><span class="finnish">En tiedä, kuka soitti.</span> <span class="english">I don't know who called.</span></p>
                    <p><span class="finnish">Voisitko kertoa, milloin elokuva alkaa?</span> <span class="english">Could you tell me when the movie starts?</span></p>
                </div>
                
                <p>Note that in Finnish, unlike in English, the word order in the indirect question part remains the same as in the direct question. There is no subject-verb inversion.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>FORMING INDIRECT YES/NO QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>When a direct question is a yes/no question (with -ko/-kö), the indirect question keeps the -ko/-kö particle:</p>
                
                <h4>1. Direct yes/no questions:</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Puhutko suomea?</span> <span class="english">Do you speak Finnish?</span></p>
                    <p><span class="finnish">Onko hän kotona?</span> <span class="english">Is he/she at home?</span></p>
                    <p><span class="finnish">Tuletko huomenna?</span> <span class="english">Are you coming tomorrow?</span></p>
                </div>
                
                <h4>2. Corresponding indirect questions:</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Hän kysyi, puhutko suomea.</span> <span class="english">He/she asked if you speak Finnish.</span></p>
                    <p><span class="finnish">En tiedä, onko hän kotona.</span> <span class="english">I don't know if he/she is at home.</span></p>
                    <p><span class="finnish">Hän haluaa tietää, tuletko huomenna.</span> <span class="english">He/she wants to know if you are coming tomorrow.</span></p>
                </div>
                
                <p>In Finnish, the -ko/-kö particle is retained in indirect yes/no questions, unlike in English where "if" or "whether" is used.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON VERBS USED WITH INDIRECT QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>Certain verbs are commonly used to introduce indirect questions:</p>
                
                <ul>
                    <li><span class="finnish">kysyä</span> - to ask</li>
                    <li><span class="finnish">tietää</span> - to know</li>
                    <li><span class="finnish">ihmetellä</span> - to wonder</li>
                    <li><span class="finnish">miettiä</span> - to think about, to consider</li>
                    <li><span class="finnish">pohtia</span> - to ponder</li>
                    <li><span class="finnish">kertoa</span> - to tell</li>
                    <li><span class="finnish">selittää</span> - to explain</li>
                    <li><span class="finnish">ymmärtää</span> - to understand</li>
                    <li><span class="finnish">muistaa</span> - to remember</li>
                    <li><span class="finnish">unohtaa</span> - to forget</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän kysyi, missä asun.</span> <span class="english">He/she asked where I live.</span></p>
                    <p><span class="finnish">En tiedä, mitä hän sanoi.</span> <span class="english">I don't know what he/she said.</span></p>
                    <p><span class="finnish">Mietin, tulisiko minun lähteä.</span> <span class="english">I'm wondering whether I should leave.</span></p>
                    <p><span class="finnish">Voisitko kertoa, miten pääsen asemalle?</span> <span class="english">Could you tell me how to get to the station?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PUNCTUATION IN INDIRECT QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>In Finnish, a comma is typically used to separate the main clause from the indirect question clause:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän kysyi, missä asun.</span> <span class="english">He/she asked where I live.</span></p>
                    <p><span class="finnish">En tiedä, onko hän tulossa.</span> <span class="english">I don't know if he/she is coming.</span></p>
                </div>
                
                <p>Unlike direct questions, indirect questions do not end with a question mark, even though they contain a question:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p>Indirect: <span class="finnish">Hän kysyi, missä sinä asut.</span> <span class="english">He/she asked where you live.</span></p>
                </div>
                
                <p>However, if the entire sentence is a question, then a question mark is used:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tiedätkö, missä hän asuu?</span> <span class="english">Do you know where he/she lives?</span></p>
                    <p><span class="finnish">Voisitko kertoa, milloin juna lähtee?</span> <span class="english">Could you tell me when the train leaves?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>TENSE CHANGES IN INDIRECT QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>Unlike in some languages, Finnish does not have a strict sequence of tenses rule for indirect questions. The tense in the indirect question generally remains the same as it would be in the direct question:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p>Indirect: <span class="finnish">Hän kysyi, missä sinä asut.</span> <span class="english">He/she asked where you live.</span></p>
                </div>
                
                <p>However, if the time reference changes, the tense may change accordingly:</p>
                
                <div class="grammar-example">
                    <p>Direct (present): <span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p>Indirect (past): <span class="finnish">Hän kysyi, missä sinä asuit.</span> <span class="english">He/she asked where you lived.</span></p>
                </div>
                
                <p>This change happens not because of a grammatical rule about indirect speech, but because the context has shifted to the past.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PRONOUN CHANGES IN INDIRECT QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>When reporting someone else's question, pronouns may change to reflect the new perspective:</p>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Missä sinä asut?</span> <span class="english">Where do you live?</span></p>
                    <p>Indirect: <span class="finnish">Hän kysyi minulta, missä minä asun.</span> <span class="english">He/she asked me where I live.</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Direct: <span class="finnish">Milloin tulet takaisin?</span> <span class="english">When are you coming back?</span></p>
                    <p>Indirect: <span class="finnish">Hän kysyi minulta, milloin tulen takaisin.</span> <span class="english">He/she asked me when I am coming back.</span></p>
                </div>
                
                <p>These changes are not grammatical requirements but reflect the logical shift in perspective when reporting someone else's words.</p>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















