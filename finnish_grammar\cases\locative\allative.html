﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Allative Case - Finnish Grammar - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Cases</a>
            <span class="separator">></span>
            <span>Allative Case</span>
        </div>
        
        <section class="grammar-section">
            <h2>Allative Case (Allatiivi)</h2>
            <p>The allative case in Finnish is used to express movement onto or to something. It answers the question "to where?" (mihin?) and is formed with the ending -lle.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMATION OF THE ALLATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The allative case is formed by adding -lle to the genitive stem of the word (without the final -n).</p>
                
                <p>Examples of words in the allative case:</p>
                <ul>
                    <li>pöytä (table) → pöydälle (onto the table)</li>
                    <li>katu (street) → kadulle (onto the street)</li>
                    <li>meri (sea) → merelle (to the sea)</li>
                    <li>ihminen (person) → ihmiselle (to the person)</li>
                    <li>tori (market) → torille (to the market)</li>
                </ul>
                
                <p>The allative plural is formed by adding -ille to the plural stem:</p>
                <ul>
                    <li>pöytä → pöydille (onto the tables)</li>
                    <li>katu → kaduille (onto the streets)</li>
                    <li>meri → merille (to the seas)</li>
                    <li>ihminen → ihmisille (to the people)</li>
                    <li>tori → toreille (to the markets)</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Laitan kirjan pöydälle.</span> <span class="english">I put the book onto the table.</span></p>
                    <p><span class="finnish">Lapset menevät kaduille.</span> <span class="english">The children go onto the streets.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF THE ALLATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The allative case is used in the following situations:</p>
                
                <h4>1. To express movement onto or to something</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Laitan kirjan pöydälle.</span> <span class="english">I put the book onto the table.</span></p>
                    <p><span class="finnish">Menen torille.</span> <span class="english">I go to the market square.</span></p>
                </div>
                
                <h4>2. To express giving to someone</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Annan kirjan ystävälleni.</span> <span class="english">I give the book to my friend.</span></p>
                    <p><span class="finnish">Soitan äidilleni.</span> <span class="english">I call my mother.</span></p>
                </div>
                
                <h4>3. To express feelings or sensations</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minulle tuli kylmä.</span> <span class="english">I got cold.</span></p>
                    <p><span class="finnish">Hänelle tuli paha olo.</span> <span class="english">He/she felt sick.</span></p>
                </div>
                
                <h4>4. To express opinions or impressions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minulle tämä on helppoa.</span> <span class="english">For me, this is easy.</span></p>
                    <p><span class="finnish">Hänelle matematiikka on vaikeaa.</span> <span class="english">For him/her, mathematics is difficult.</span></p>
                </div>
                
                <h4>5. To express selling to someone</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Myin auton hänelle.</span> <span class="english">I sold the car to him/her.</span></p>
                    <p><span class="finnish">Tarjosin kahvia vieraille.</span> <span class="english">I offered coffee to the guests.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ALLATIVE VS. OTHER LOCATIVE CASES</h3>
            
            <div class="grammar-content">
                <p>The allative case is part of the "external locative cases" in Finnish, which express relationships with the surface or vicinity of something:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Ending</th>
                        <th>Meaning</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Adessive</td>
                        <td>-lla/-llä</td>
                        <td>On, at (static)</td>
                        <td>pöydällä (on the table)</td>
                    </tr>
                    <tr>
                        <td>Ablative</td>
                        <td>-lta/-ltä</td>
                        <td>From on/at (movement away)</td>
                        <td>pöydältä (from the table)</td>
                    </tr>
                    <tr>
                        <td>Allative</td>
                        <td>-lle</td>
                        <td>Onto, to (movement to)</td>
                        <td>pöydälle (onto the table)</td>
                    </tr>
                </table>
                
                <p>Compare with the "internal locative cases" which express relationships with the inside of something:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Ending</th>
                        <th>Meaning</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>-ssa/-ssä</td>
                        <td>In, inside (static)</td>
                        <td>talossa (in the house)</td>
                    </tr>
                    <tr>
                        <td>Elative</td>
                        <td>-sta/-stä</td>
                        <td>From inside (movement out)</td>
                        <td>talosta (from the house)</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>-Vn, -hVn, -seen</td>
                        <td>Into (movement in)</td>
                        <td>taloon (into the house)</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Kirja on pöydällä.</span> <span class="english">The book is on the table. (Adessive - static)</span></p>
                    <p><span class="finnish">Otan kirjan pöydältä.</span> <span class="english">I take the book from the table. (Ablative - movement away)</span></p>
                    <p><span class="finnish">Laitan kirjan pöydälle.</span> <span class="english">I put the book onto the table. (Allative - movement to)</span></p>
                </div>
                
                <p>The difference between allative (-lle) and illative (-Vn, -hVn, -seen) is that allative refers to movement onto a surface or to a vicinity, while illative refers to movement into something:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Laitan kirjan pöydälle.</span> <span class="english">I put the book onto the table. (Allative)</span></p>
                    <p><span class="finnish">Laitan kirjan laatikkoon.</span> <span class="english">I put the book into the box. (Illative)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXPRESSIONS</h3>
            
            <div class="grammar-content">
                <p>Some common expressions using the allative case:</p>
                
                <h4>1. Giving and communication</h4>
                <div class="grammar-example">
                    <p><span class="finnish">antaa jollekin</span> <span class="english">to give to someone</span></p>
                    <p><span class="finnish">soittaa jollekin</span> <span class="english">to call someone</span></p>
                    <p><span class="finnish">kertoa jollekin</span> <span class="english">to tell someone</span></p>
                    <p><span class="finnish">lähettää jollekin</span> <span class="english">to send to someone</span></p>
                </div>
                
                <h4>2. Feelings and sensations</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minulle tuli kylmä.</span> <span class="english">I got cold.</span></p>
                    <p><span class="finnish">Minulle tuli nälkä.</span> <span class="english">I got hungry.</span></p>
                    <p><span class="finnish">Minulle tuli jano.</span> <span class="english">I got thirsty.</span></p>
                </div>
                
                <h4>3. Opinions and impressions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minulle tämä on tärkeää.</span> <span class="english">For me, this is important.</span></p>
                    <p><span class="finnish">Hänelle se on yhdentekevää.</span> <span class="english">For him/her, it doesn't matter.</span></p>
                </div>
                
                <h4>4. Fixed expressions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">mennä töille</span> <span class="english">to go to work</span></p>
                    <p><span class="finnish">jäädä eläkkeelle</span> <span class="english">to retire</span></p>
                    <p><span class="finnish">päästä vapaalle</span> <span class="english">to get off duty</span></p>
                    <p><span class="finnish">lähteä lomalle</span> <span class="english">to go on vacation</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












