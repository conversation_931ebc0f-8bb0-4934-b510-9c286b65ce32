<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accusative Case - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Cases</a>
            <span class="separator">></span>
            <span>Accusative Case</span>
        </div>
        
        <section class="grammar-section">
            <h2>Accusative Case (Akkusatiivi)</h2>
            <p>The accusative case in Finnish is used for the direct object of a sentence. Unlike other cases, the accusative doesn't have its own unique ending but instead takes the form of either the nominative or genitive case.</p>
        </section>

        <section class="grammar-category">
            <h3>UNDERSTANDING THE ACCUSATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The accusative case is somewhat special in Finnish because it doesn't have a unique form. Instead, it uses either:</p>
                <ul>
                    <li>The nominative form (no ending)</li>
                    <li>The genitive form (with -n ending)</li>
                </ul>
                
                <p>The choice between these forms depends on the sentence structure and the type of verb used.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Ostan kirjan.</span> <span class="english">I will buy the book. (Genitive form)</span></p>
                    <p><span class="finnish">Osta kirja!</span> <span class="english">Buy the book! (Nominative form)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>WHEN TO USE THE GENITIVE FORM</h3>
            
            <div class="grammar-content">
                <p>The genitive form of the accusative is used in the following situations:</p>
                
                <h4>1. With affirmative sentences that have a subject</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen kirjan.</span> <span class="english">I will read the book.</span></p>
                    <p><span class="finnish">Hän ostaa auton.</span> <span class="english">He/she will buy a car.</span></p>
                </div>
                
                <h4>2. With personal pronouns as objects</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Näen sinut.</span> <span class="english">I see you.</span></p>
                    <p><span class="finnish">Hän kutsuu meidät.</span> <span class="english">He/she invites us.</span></p>
                </div>
                
                <p>Note that personal pronouns have special accusative forms:</p>
                <ul>
                    <li>minut (me)</li>
                    <li>sinut (you - singular)</li>
                    <li>hänet (him/her)</li>
                    <li>meidät (us)</li>
                    <li>teidät (you - plural)</li>
                    <li>heidät (them)</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>WHEN TO USE THE NOMINATIVE FORM</h3>
            
            <div class="grammar-content">
                <p>The nominative form of the accusative is used in the following situations:</p>
                
                <h4>1. With imperative sentences (commands)</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Osta auto!</span> <span class="english">Buy a car!</span></p>
                    <p><span class="finnish">Lue kirja!</span> <span class="english">Read the book!</span></p>
                </div>
                
                <h4>2. With passive sentences</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Auto ostetaan huomenna.</span> <span class="english">The car will be bought tomorrow.</span></p>
                    <p><span class="finnish">Kirja luetaan koulussa.</span> <span class="english">The book is read at school.</span></p>
                </div>
                
                <h4>3. With necessive constructions (täytyy, pitää, etc.)</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Sinun täytyy ostaa auto.</span> <span class="english">You must buy a car.</span></p>
                    <p><span class="finnish">Minun pitää lukea kirja.</span> <span class="english">I have to read the book.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ACCUSATIVE VS. PARTITIVE</h3>
            
            <div class="grammar-content">
                <p>The choice between accusative and partitive for direct objects depends on several factors:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Accusative</th>
                        <th>Partitive</th>
                    </tr>
                    <tr>
                        <td>Complete, finished actions</td>
                        <td>Incomplete, ongoing actions</td>
                    </tr>
                    <tr>
                        <td>Definite, specific objects</td>
                        <td>Indefinite, non-specific objects</td>
                    </tr>
                    <tr>
                        <td>Affirmative sentences</td>
                        <td>Negative sentences</td>
                    </tr>
                    <tr>
                        <td>Countable objects (whole entities)</td>
                        <td>Mass nouns, uncountable objects</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Luen kirjan.</span> <span class="english">I will read the (whole) book. (Accusative - complete action)</span></p>
                    <p><span class="finnish">Luen kirjaa.</span> <span class="english">I am reading a/the book. (Partitive - ongoing action)</span></p>
                    <p><span class="finnish">Ostan auton.</span> <span class="english">I will buy the car. (Accusative - definite object)</span></p>
                    <p><span class="finnish">Ostan autoa.</span> <span class="english">I am buying a car. (Partitive - indefinite object)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXCEPTIONS</h3>
            
            <div class="grammar-content">
                <p>There are some special cases and exceptions to be aware of:</p>
                
                <h4>1. Plural objects</h4>
                <p>Plural objects in the accusative case take the nominative plural form (with -t ending):</p>
                <div class="grammar-example">
                    <p><span class="finnish">Ostan kirjat.</span> <span class="english">I will buy the books.</span></p>
                    <p><span class="finnish">Luen lehdet.</span> <span class="english">I will read the magazines.</span></p>
                </div>
                
                <h4>2. Certain verbs always take the partitive</h4>
                <p>Some verbs always take their objects in the partitive case, regardless of whether the action is complete:</p>
                <ul>
                    <li>rakastaa (to love)</li>
                    <li>vihata (to hate)</li>
                    <li>pelätä (to fear)</li>
                    <li>odottaa (to wait for)</li>
                    <li>ajatella (to think about)</li>
                </ul>
                <div class="grammar-example">
                    <p><span class="finnish">Rakastan sinua.</span> <span class="english">I love you. (Always partitive)</span></p>
                    <p><span class="finnish">Odotan bussia.</span> <span class="english">I'm waiting for the bus. (Always partitive)</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









