<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> (<PERSON>) - <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../styles.css">
    <style>
        /* Mobile menu specific styles */
        @media (max-width: 767px) {
            /* Ensure mobile menu toggle is visible and clickable */
            .mobile-menu-toggle {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 40px !important;
                height: 40px !important;
                background-color: transparent !important;
                border: none !important;
                cursor: pointer !important;
                z-index: 1001 !important; /* Higher than the menu */
                position: relative !important;
                padding: 8px !important;
            }
            
            .mobile-menu-toggle i {
                font-size: 1.5rem !important;
                color: var(--primary-color) !important;
            }
            
            .dark-mode .mobile-menu-toggle i {
                color: #fff !important;
            }
            
            .mobile-menu-toggle:hover, 
            .mobile-menu-toggle:focus {
                background-color: rgba(0, 0, 0, 0.05) !important;
                outline: none !important;
            }
            
            .dark-mode .mobile-menu-toggle:hover,
            .dark-mode .mobile-menu-toggle:focus {
                background-color: rgba(255, 255, 255, 0.1) !important;
            }
            
            .mobile-menu-toggle.active {
                background-color: rgba(0, 0, 0, 0.1) !important;
            }
            
            .dark-mode .mobile-menu-toggle.active {
                background-color: rgba(255, 255, 255, 0.15) !important;
            }
            
            /* Menu styles - using direct style manipulation for better control */
            .nav-links {
                display: flex; /* Show by default on larger screens */
            }
            
            /* Only hide on mobile */
            @media (max-width: 767px) {
                .nav-links {
                    display: none !important; /* Hide by default on mobile */
                    visibility: hidden !important;
                    opacity: 0 !important;
                    transition: none !important;
                }
            }
            
            /* Explicitly override any conflicting styles */
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
                visibility: visible !important;
                opacity: 1 !important;
                height: auto !important;
                overflow: visible !important;
                transform: none !important;
                pointer-events: auto !important;
            }
            
            .dark-mode .nav-links.show {
                background-color: #252525 !important;
            }
            
            /* Dropdown styles for mobile */
            .dropdown-content {
                display: none !important;
            }
            
            .dropdown.active .dropdown-content {
                display: block !important;
                position: static !important;
                width: 100% !important;
                background-color: rgba(0, 0, 0, 0.03) !important;
                box-shadow: none !important;
                border-top: 1px solid #eee !important;
            }
            
            .dark-mode .dropdown.active .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05) !important;
                border-top: 1px solid #333 !important;
            }
        }
        
        /* Game specific styles */
        .game-container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-wrap: wrap;
        }
        
        .dark-mode .game-container {
            background-color: #1e1e1e;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .game-container .intro {
            width: 100%;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eaeaea;
        }
        
        .dark-mode .game-container .intro {
            border-bottom: 1px solid #333;
        }
        
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 15px;
            position: relative;
        }
        
        .game-title {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .game-sidebar {
            flex: 0 0 250px;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        
        .game-sidebar.left {
            border-right: 1px solid #eaeaea;
        }
        
        .game-sidebar.right {
            border-left: 1px solid #eaeaea;
        }
        
        .dark-mode .game-sidebar.left {
            border-right: 1px solid #333;
        }
        
        .dark-mode .game-sidebar.right {
            border-left: 1px solid #333;
        }
        
        .game-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 0 20px;
            min-width: 0; /* Important for flex items */
            position: relative;
            width: 100%;
        }
        
        .fullscreen-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 9999; /* Higher z-index to ensure it's above everything */
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 20px;
            overflow: auto;
            box-sizing: border-box;
        }

        .fullscreen-container.active {
            display: flex;
        }

        .fullscreen-controls {
            display: flex;
            gap: 15px;
            width: 100%;
            max-width: 600px;
            justify-content: center;
            align-items: center;
        }
        
        .fullscreen-score {
            display: flex;
            gap: 15px;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px 15px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .score-white {
            color: #161616; /* Light gray color for better visibility */
        }
        
        .score-black {
            color: #161616;
        }
        
        /* Ensure consistent colors in fullscreen mode */
        .fullscreen-container .score-white {
            color: #000000; /* Light gray color for better visibility */
        }
        
        .fullscreen-container .score-black {
            color: #000000;
        }
        
        /* In dark mode, keep black pieces black */
        .dark-mode .score-black {
            color: #000000;
        }
        
        .dark-mode .fullscreen-container .score-black {
            color: #000000;
        }

        .fullscreen-container .game-button {
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .fullscreen-container .game-button:hover {
            background-color: rgba(255, 255, 255, 0.25);
            color: white; /* Ensure text remains white on hover */
            transform: translateY(-3px);
        }

        @media (max-width: 600px) {
            .fullscreen-header {
                margin-top: 5px;
            }
            
            .fullscreen-controls {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 5px;
                max-width: 100%;
                padding: 0 10px;
            }
            
            .fullscreen-container .game-button {
                flex: 1;
                min-width: 80px;
                padding: 8px 5px;
                font-size: 0.8rem;
                margin-bottom: 0;
            }
            
            .fullscreen-score {
                padding: 6px 10px;
                font-size: 1rem;
                gap: 10px;
            }
        }
        
        .fullscreen-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            font-size: 1.2rem;
        }
        
        .fullscreen-container .exit-button {
            background-color: #e74c3c; /* Red color for better visibility */
            color: white;
            border: none;
        }
        
        .fullscreen-container .exit-button:hover {
            background-color: #c0392b; /* Darker red on hover */
        }
        
        #fullscreen-chess-board {
            max-width: 80vh;
            width: 80vh;
            height: 80vh;
            margin: 0 auto;
        }
        
        @media (max-width: 768px) {
            #fullscreen-chess-board {
                max-width: 90vw;
                width: 90vw;
                height: 90vw;
            }
        }
        
        .game-board-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: visible;
            box-sizing: border-box;
            padding: 0;
            position: relative;
        }
        
        /* Chess specific styles */
        .chess-board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(8, 1fr);
            width: 100%;
            max-width: 600px;
            aspect-ratio: 1;
            margin: 0 auto;
            border: 2px solid #333;
            box-sizing: border-box;
        }
        
        .dark-mode .chess-board {
            border-color: #555;
        }
        
        .chess-square {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            cursor: pointer;
            user-select: none;
            position: relative;
            box-sizing: border-box;
            min-width: 30px;
            min-height: 30px;
        }
        
        .chess-square.white {
            background-color: #f0d9b5;
        }
        
        .chess-square.black {
            background-color: #b58863;
        }
        
        .dark-mode .chess-square.white {
            background-color: #d8c0a0;
        }
        
        .dark-mode .chess-square.black {
            background-color: #8b6b4c;
        }
        
        .chess-square.selected {
            background-color: rgba(173, 216, 230, 0.7);
        }
        
        .chess-square.valid-move {
            position: relative;
        }
        
        .chess-square.valid-move::before {
            content: "";
            position: absolute;
            width: 25%;
            height: 25%;
            background-color: rgba(0, 128, 0, 0.5);
            border-radius: 50%;
            z-index: 1;
        }
        
        .chess-square.check {
            background-color: rgba(255, 0, 0, 0.4);
        }
        
        .chess-piece {
            width: 80%;
            height: 80%;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 2;
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            min-width: 24px;
            min-height: 24px;
            transform: none !important;
            transition: none !important;
        }
        
        /* Chess piece colors remain consistent in both modes */
        .chess-piece[data-color="white"] {
            color: #161616; /* Light gray color for better visibility */
        }
        
        .chess-piece[data-color="black"] {
            color: #000000;
        }
        
        /* Ensure black pieces stay black in dark mode */
        .dark-mode .chess-piece[data-color="black"] {
            color: #000000;
        }
        
        /* Rank and file labels */
        .rank-label, .file-label {
            position: absolute;
            font-size: 0.8rem;
            color: #333;
            font-weight: bold;
        }
        
        .dark-mode .rank-label, .dark-mode .file-label {
            color: #333;
        }
        
        .rank-label {
            left: -2px;  /* Move 2px to the left */
            top: -5px;   /* Move 5px to the top */
            font-size: 0.7rem;  /* Smaller font size */
        }
        
        .file-label {
            right: 0px;  /* Keep at right edge */
            bottom: -5px; /* Move 5px to the bottom */
            font-size: 0.7rem; /* Smaller font size to match rank-label */
        }
        
        /* Promotion modal */
        .promotion-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            justify-content: center;
            align-items: center;
        }
        
        .promotion-modal.active {
            display: flex;
        }
        
        .promotion-options {
            display: flex;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .dark-mode .promotion-options {
            background-color: #333;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }
        
        .promotion-piece {
            width: 60px;
            height: 60px;
            margin: 0 10px;
            cursor: pointer;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transition: transform 0.2s;
        }
        
        .promotion-piece:hover {
            transform: scale(1.1);
        }
        
        /* Game status and controls */
        .game-status {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            min-height: 20px;
        }
        
        .dark-mode .game-status {
            background-color: #333;
        }
        
        .game-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .game-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }
        
        .game-button:hover {
            background-color: #002460; /* Darker blue that ensures text visibility */
            color: white; /* Ensure text remains white on hover */
            transform: translateY(-2px);
        }
        
        .score-board {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.2rem;
            width: 100%;
        }
        
        .score-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .dark-mode .score-item {
            background-color: #333;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .score-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .score-value {
            font-size: 1.5rem;
        }
        
        .captured-pieces {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
            justify-content: center;
        }
        
        .captured-piece {
            width: 25px;
            height: 25px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
        }
        
        .notification.show {
            opacity: 1;
        }
        
        /* Responsive styles */
        @media (max-width: 1100px) {
            .game-container {
                flex-direction: column;
            }
            
            .game-sidebar {
                flex: 0 0 auto;
                width: 100%;
                border: none !important;
                border-bottom: 1px solid #eaeaea !important;
                padding-bottom: 20px;
                margin-bottom: 20px;
            }
            
            .dark-mode .game-sidebar {
                border-bottom: 1px solid #333 !important;
            }
            
            .game-sidebar.right {
                order: 3;
                border-top: 1px solid #eaeaea !important;
                border-bottom: none !important;
                margin-bottom: 0;
                margin-top: 20px;
                padding-top: 20px;
                padding-bottom: 0;
            }
            
            .dark-mode .game-sidebar.right {
                border-top: 1px solid #333 !important;
                border-bottom: none !important;
            }
        }
        
        @media (max-width: 767px) {
            .game-container {
                padding: 15px;
            }
            
            .game-main {
                padding: 0;
            }
            
            .game-sidebar {
                padding: 15px 0;
                width: 100%;
                box-sizing: border-box;
            }
            
            .game-sidebar.right {
                order: 3;
            }
            
            .score-board {
                flex-direction: row;
                justify-content: center;
            }
            
            .score-item {
                width: auto;
                flex: 1;
                max-width: 150px;
            }
            
            .game-controls {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .game-options {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .game-options h3, .game-options h4 {
                margin-bottom: 10px;
                width: 100%;
            }
            
            .difficulty-options {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 15px;
            }
            
            .option-label {
                margin: 5px 0;
                display: inline-flex;
                justify-content: center;
                width: auto;
            }
            
            .game-options, .difficulty-options {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .game-button {
                width: auto;
                flex: 1;
                min-width: 150px;
            }
            
            .chess-board {
                max-width: 100%;
            }
        }
        
        @media (max-width: 480px) {
            .game-container {
                padding: 10px;
            }
            
            .chess-square {
                font-size: 1.8rem;
            }
            
            /* Ensure chess pieces maintain their size on mobile */
            .chess-piece {
                font-size: 2.5rem !important; /* Keep the same font size as desktop */
                width: 80% !important;
                height: 80% !important;
                min-width: 24px !important;
                min-height: 24px !important;
            }
            
            .game-title {
                font-size: 1.2rem;
            }
            
            .game-button {
                padding: 8px 12px;
                font-size: 0.9rem;
            }
            
            /* Improve mobile layout for game options */
            .game-options {
                padding: 0 5px;
            }
            
            .difficulty-options {
                padding-top: 15px;
                margin-top: 15px;
                border-top: 1px solid #eaeaea;
            }
            
            .dark-mode .difficulty-options {
                border-top: 1px solid #333;
            }
            
            .option-label {
                margin: 3px 0;
                font-size: 0.9rem;
                display: inline-flex;
                justify-content: center;
                width: auto;
            }
            
            .game-options h3, .game-options h4 {
                margin-bottom: 8px;
            }
        }
        
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        /* Finnish vocabulary section */
        .game-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 8px;
        }
        
        .dark-mode .game-info {
            background-color: #252525;
        }
        
        .game-info h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .game-info p {
            margin: 5px 0;
            font-size: 0.9rem;
        }
        
        .vocabulary-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f8f8;
            border-radius: 8px;
        }
        
        .dark-mode .vocabulary-section {
            background-color: #252525;
        }
        
        .vocabulary-title {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .vocabulary-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .vocabulary-item {
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .dark-mode .vocabulary-item {
            background-color: #333;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .finnish-word {
            font-weight: bold;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="game-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../index.html">Home</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content">
                        <a href="../video.html?channel=kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../video.html?channel=finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=yleareena">Yle Areena 1</a>
                        <a href="../video.html?channel=yleareena2">Yle Areena 2</a>
                        <a href="../video.html?channel=yleareena3">Yle Areena 3</a>
                        <a href="../video.html?channel=yleareena4">Yle Areena 4</a>
                        <a href="../video.html?channel=yleareena5">Yle Areena 5</a>
                        <a href="../video.html?channel=pipsapossu">Pipsa Possu</a>
                        <a href="../video.html?channel=katchatsfinnish">KatChats Finnish</a>
                        <a href="../video.html?channel=kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../video.html?channel=kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../video.html?channel=kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../video.html?channel=kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../audio.html">Audio</a></li>
                <li><a href="../finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../index.html#daily-life">Daily Life</a>
                        <a href="../index.html#web-development">Web Development</a>
                        <a href="../index.html#cleaner">Cleaner</a>
                        <a href="../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../games.html">Games</a>
                    </div>
                </li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="game-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <div class="game-container">
            <!-- Left Sidebar -->
            <div class="game-sidebar left">
                <div class="game-options">
                    <h3>Game Mode</h3>
                    <label class="option-label">
                        <input type="radio" name="game-mode" value="computer" checked>Computer
                    </label>
                    <label class="option-label">
                        <input type="radio" name="game-mode" value="friend">Friend
                    </label>
                    
                    <div id="difficulty-options" class="difficulty-options">
                        <h4>Difficulty Level</h4>
                        <label class="option-label">
                            <input type="radio" name="difficulty" value="easy" checked> Easy
                        </label>
                        <label class="option-label">
                            <input type="radio" name="difficulty" value="normal"> Normal
                        </label>
                        <label class="option-label">
                            <input type="radio" name="difficulty" value="hard"> Hard
                        </label>
                    </div>
                </div>
                
                <div class="score-board">
                    <div class="score-item player-white">
                        <div class="score-label">White</div>
                        <div class="score-value" id="score-white">0</div>
                        <div class="captured-pieces" id="white-captured"></div>
                    </div>
                    <div class="score-item player-black">
                        <div class="score-label">Black</div>
                        <div class="score-value" id="score-black">0</div>
                        <div class="captured-pieces" id="black-captured"></div>
                    </div>
                </div>
                
                <a href="../games.html" class="back-link">
                    <i class="fas fa-arrow-left"></i> Takaisin peleihin (Back to Games)
                </a>
            </div>
            
            <!-- Main Game Area -->
            <div class="game-main">
                <div class="game-header">
                    <h2 class="game-title">Shakki (Chess)</h2>
                    <button class="fullscreen-button" id="fullscreen-button">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
                
                <div class="game-board-container">
                    <div class="chess-board" id="chess-board">
                        <!-- Chess squares will be generated by JavaScript -->
                    </div>
                </div>
                
                <div class="fullscreen-container" id="fullscreen-container">
                    <div class="fullscreen-header">
                        <div class="fullscreen-controls">
                            <button class="game-button" id="fullscreen-reset-button">
                                <i class="fas fa-redo"></i> Aloita uudelleen
                            </button>
                            <button class="game-button" id="fullscreen-reset-scores-button">
                                <i class="fas fa-eraser"></i> Nollaa pisteet
                            </button>
                            <div class="fullscreen-score">
                                <span class="score-white">W:<span id="fullscreen-score-white">0</span></span>
                                <span class="score-black">B:<span id="fullscreen-score-black">0</span></span>
                            </div>
                            <button class="game-button exit-button" id="exit-fullscreen-button">
                                <i class="fas fa-compress"></i> Sulje
                            </button>
                        </div>
                    </div>
                    
                    <div class="chess-board" id="fullscreen-chess-board">
                        <!-- Chess squares will be generated by JavaScript -->
                    </div>
                </div>
                
                <div id="notification" class="notification"></div>
            </div>
            
            <!-- Right Sidebar -->
            <div class="game-sidebar right">
                <h3>Game Controls</h3>
                <div class="game-controls">
                    <button class="game-button" id="reset-button">Aloita uudelleen (Restart)</button>
                    <button class="game-button" id="reset-scores-button">Nollaa pisteet (Reset Scores)</button>
                    <button class="game-button" id="undo-button">Kumoa siirto (Undo Move)</button>
                </div>
                
                <div class="game-info">
                    <h3>How to Play</h3>
                    <p>Classic chess game, known as "Shakki" in Finnish. Move your pieces strategically to checkmate your opponent's king.</p>
                    <p>Play against the computer with different difficulty levels or challenge a friend. White goes first.</p>
                </div>
            </div>
        </div>
        
        <div class="vocabulary-section">
            <h3 class="vocabulary-title">Finnish Vocabulary for Chess</h3>
            <div class="vocabulary-list">
                <div class="vocabulary-item">
                    <span class="finnish-word">Shakki</span> - Chess
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Kuningas</span> - King
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Kuningatar</span> - Queen
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Torni</span> - Rook
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Lähetti</span> - Bishop
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Ratsu</span> - Knight
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Sotilas</span> - Pawn
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Shakkimatti</span> - Checkmate
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Shakki</span> - Check
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Patti</span> - Stalemate
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Siirto</span> - Move
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Voittaa</span> - To win
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Hävitä</span> - To lose
                </div>
                <div class="vocabulary-item">
                    <span class="finnish-word">Tasapeli</span> - Draw
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Opiskelen Suomea. All rights reserved.</p>
        </div>
    </footer>

    <div class="promotion-modal" id="promotion-modal">
        <div class="promotion-options" id="promotion-options">
            <!-- Promotion pieces will be added by JavaScript -->
        </div>
    </div>

    <script src="../script.js"></script>
    <script>
        // Initialize dark mode and mobile menu
        document.addEventListener("DOMContentLoaded", function() {
            // Mobile menu toggle functionality - explicitly defined for chess.html
            const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            const navLinks = document.getElementById("nav-links");

            if (mobileMenuToggle && navLinks) {
                // Remove any existing event listeners (to avoid duplicates)
                mobileMenuToggle.removeEventListener("click", toggleMenu);
                
                // Define the toggle function - using direct style manipulation
                function toggleMenu() {
                    // Only toggle on mobile screens
                    if (window.innerWidth <= 767) {
                        // Check if menu is currently visible
                        const isVisible = navLinks.classList.contains("show");
                        
                        if (isVisible) {
                            // Hide the menu
                            navLinks.classList.remove("show");
                            mobileMenuToggle.classList.remove("active");
                            
                            // Direct style manipulation as backup
                            navLinks.style.display = "none";
                            navLinks.style.visibility = "hidden";
                            navLinks.style.opacity = "0";
                        } else {
                            // Show the menu
                            navLinks.classList.add("show");
                            mobileMenuToggle.classList.add("active");
                            
                            // Direct style manipulation as backup
                            navLinks.style.display = "flex";
                            navLinks.style.flexDirection = "column";
                            navLinks.style.visibility = "visible";
                            navLinks.style.opacity = "1";
                            navLinks.style.position = "absolute";
                            navLinks.style.top = "60px";
                            navLinks.style.left = "0";
                            navLinks.style.width = "100%";
                            navLinks.style.zIndex = "1000";
                        }
                        
                        console.log("Menu toggled, visible:", !isVisible);
                        console.log("Nav links classes:", navLinks.className);
                        console.log("Menu display style:", navLinks.style.display);
                    } else {
                        console.log("Toggle menu ignored on desktop view");
                    }
                }
                
                // Add the event listener with capture and prevent default
                mobileMenuToggle.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleMenu();
                }, true); // Use capture phase
                
                // Add a debug click handler to verify the element is clickable
                mobileMenuToggle.addEventListener("mousedown", function() {
                    console.log("Menu button clicked");
                });
                
                // Add window resize handler to adjust menu visibility based on screen size
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 767) {
                        // Desktop view - ensure menu is visible
                        navLinks.style.display = "flex";
                        navLinks.style.visibility = "visible";
                        navLinks.style.opacity = "1";
                        navLinks.style.position = "static";
                        navLinks.style.flexDirection = "row";
                    } else {
                        // Mobile view - hide menu unless it's explicitly shown
                        if (!navLinks.classList.contains("show")) {
                            navLinks.style.display = "none";
                            navLinks.style.visibility = "hidden";
                            navLinks.style.opacity = "0";
                        }
                    }
                });
                
                // Ensure menu is properly initialized
                console.log("Menu initialized");
                console.log("Mobile menu toggle exists:", !!mobileMenuToggle);
                console.log("Nav links exists:", !!navLinks);
                
                // Set initial state based on screen size
                if (window.innerWidth <= 767) {
                    // Mobile view - hide menu
                    navLinks.classList.remove("show");
                    navLinks.style.display = "none";
                    navLinks.style.visibility = "hidden";
                    navLinks.style.opacity = "0";
                } else {
                    // Desktop view - show menu
                    navLinks.style.display = "flex";
                    navLinks.style.visibility = "visible";
                    navLinks.style.opacity = "1";
                }
                
                // Create a fallback menu in case the original one doesn't work
                const fallbackMenu = document.createElement('div');
                fallbackMenu.id = 'fallback-menu';
                fallbackMenu.style.display = 'none';
                fallbackMenu.style.position = 'absolute';
                fallbackMenu.style.top = '60px';
                fallbackMenu.style.left = '0';
                fallbackMenu.style.width = '100%';
                fallbackMenu.style.backgroundColor = '#fff';
                fallbackMenu.style.zIndex = '2000';
                fallbackMenu.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                fallbackMenu.style.padding = '10px 0';
                fallbackMenu.style.borderTop = '1px solid #ddd';
                
                // Add menu items
                fallbackMenu.innerHTML = `
                    <a href="../index.html" style="display:block; padding:10px 20px; color:#004aad; text-decoration:none; border-bottom:1px solid #eee;">Home</a>
                    <a href="../video.html" style="display:block; padding:10px 20px; color:#004aad; text-decoration:none; border-bottom:1px solid #eee;">Videos</a>
                    <a href="../grammar.html" style="display:block; padding:10px 20px; color:#004aad; text-decoration:none; border-bottom:1px solid #eee;">Grammar</a>
                    <a href="../vocabulary.html" style="display:block; padding:10px 20px; color:#004aad; text-decoration:none; border-bottom:1px solid #eee;">Vocabulary</a>
                    <a href="../games.html" style="display:block; padding:10px 20px; color:#004aad; text-decoration:none; font-weight:bold;">Games</a>
                `;
                
                document.body.appendChild(fallbackMenu);
                
                // Add fallback toggle functionality
                let fallbackMenuVisible = false;
                mobileMenuToggle.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    fallbackMenuVisible = !fallbackMenuVisible;
                    fallbackMenu.style.display = fallbackMenuVisible ? 'block' : 'none';
                    console.log("Fallback menu toggled:", fallbackMenuVisible);
                });
            }

            // Check for saved dark mode preference
            const darkMode = localStorage.getItem("darkMode");
            
            // Apply dark mode if previously enabled
            if (darkMode === "enabled") {
                document.body.classList.add("dark-mode");
                
                // Update icon to sun
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Update mobile icon too
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
                // No need to update piece colors
            }
            
            // Set up dark mode toggle
            const darkModeToggle = document.getElementById("game-toggle-dark");
            if (darkModeToggle) {
                darkModeToggle.addEventListener("click", toggleDarkMode);
            }
            
            // Set up mobile dark mode toggle
            const mobileDarkModeToggle = document.getElementById("game-toggle-dark-mobile");
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.addEventListener("click", toggleDarkMode);
            }

            // Handle dropdown menus in mobile view
            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach((dropdown) => {
                const dropbtn = dropdown.querySelector(".dropbtn");
                if (dropbtn) {
                    dropbtn.addEventListener("click", function (e) {
                        // Only in mobile view
                        if (window.innerWidth <= 767) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Close other active dropdowns
                            dropdowns.forEach((otherDropdown) => {
                                if (
                                    otherDropdown !== dropdown &&
                                    otherDropdown.classList.contains("active")
                                ) {
                                    otherDropdown.classList.remove("active");
                                }
                            });

                            // Toggle current dropdown
                            dropdown.classList.toggle("active");
                        }
                    });
                }
            });
        });
        
        // Toggle dark mode function
        function toggleDarkMode() {
            document.body.classList.toggle("dark-mode");
            
            // Store preference in localStorage
            if (document.body.classList.contains("dark-mode")) {
                localStorage.setItem("darkMode", "enabled");
                
                // Change icon to sun
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Change mobile icon to sun
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            } else {
                localStorage.setItem("darkMode", "disabled");
                
                // Change icon back to moon
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-sun");
                        icon.classList.add("fa-moon");
                    }
                }
                
                // Change mobile icon back to moon
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-sun");
                        mobileIcon.classList.add("fa-moon");
                    }
                }
            }
        }

        // Chess Game Implementation
        document.addEventListener('DOMContentLoaded', function() {
            // Game elements
            const chessBoard = document.getElementById('chess-board');
            const fullscreenChessBoard = document.getElementById('fullscreen-chess-board');
            const resetButton = document.getElementById('reset-button');
            const resetScoresButton = document.getElementById('reset-scores-button');
            const undoButton = document.getElementById('undo-button');
            const scoreWhite = document.getElementById('score-white');
            const scoreBlack = document.getElementById('score-black');
            const whiteCaptured = document.getElementById('white-captured');
            const blackCaptured = document.getElementById('black-captured');
            const notification = document.getElementById('notification');
            const promotionModal = document.getElementById('promotion-modal');
            const promotionOptions = document.getElementById('promotion-options');
            
            // Game mode and difficulty
            const gameModeRadios = document.querySelectorAll('input[name="game-mode"]');
            let gameMode = 'computer'; // Default: play against computer
            
            const difficultyRadios = document.querySelectorAll('input[name="difficulty"]');
            let difficulty = 'easy'; // Default: easy difficulty
            
            // Game state
            let board = [];
            let currentPlayer = 'white';
            let selectedPiece = null;
            let validMoves = [];
            let gameActive = true;
            let scores = { white: 0, black: 0 };
            let capturedPieces = { white: [], black: [] };
            let moveHistory = [];
            let kings = { white: null, black: null };
            let inCheck = { white: false, black: false };
            let promotionPending = null;
            
            // Chess piece Unicode symbols
            const pieces = {
                'white': {
                    'king': '♔',
                    'queen': '♕',
                    'rook': '♖',
                    'bishop': '♗',
                    'knight': '♘',
                    'pawn': '♙'
                },
                'black': {
                    'king': '♚',
                    'queen': '♛',
                    'rook': '♜',
                    'bishop': '♝',
                    'knight': '♞',
                    'pawn': '♟'
                }
            };
            
            // No need to change piece colors between modes
            
            // Messages for notifications
            const messages = {
                check: "Check!",
                checkmate: "Checkmate!",
                stalemate: "Stalemate! The game is a draw.",
                whiteTurn: "White's turn",
                blackTurn: "Black's turn",
                whiteWin: "White wins!",
                blackWin: "Black wins!",
                promotion: "Choose a piece for promotion",
                gameStart: "Game started. White moves first."
            };
            
            // Initialize the game
            function initGame() {
                createBoard();
                setupPieces();
                const { updateFullscreenBoard, updateMainBoard } = initFullscreenMode();
                
                // No need to update piece colors
                
                // Add event listeners for game controls
                resetButton.addEventListener('click', resetGame);
                resetScoresButton.addEventListener('click', resetScores);
                undoButton.addEventListener('click', undoMove);
                
                // Add event listeners for fullscreen buttons
                const fullscreenResetButton = document.getElementById('fullscreen-reset-button');
                fullscreenResetButton.addEventListener('click', resetGame);
                
                const fullscreenResetScoresButton = document.getElementById('fullscreen-reset-scores-button');
                fullscreenResetScoresButton.addEventListener('click', resetScores);
                
                // Game mode change handler
                gameModeRadios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        gameMode = e.target.value;
                        
                        // Show/hide difficulty options based on game mode
                        const difficultyOptions = document.getElementById('difficulty-options');
                        if (gameMode === 'computer') {
                            difficultyOptions.style.display = 'block';
                        } else {
                            difficultyOptions.style.display = 'none';
                        }
                        
                        resetGame();
                    });
                });
                
                // Difficulty change handler
                difficultyRadios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        difficulty = e.target.value;
                        resetGame();
                    });
                });
                
                // Initialize difficulty options visibility
                const difficultyOptions = document.getElementById('difficulty-options');
                if (gameMode === 'computer') {
                    difficultyOptions.style.display = 'block';
                } else {
                    difficultyOptions.style.display = 'none';
                }
                
                updateScores();
                showNotification(messages.gameStart, 1500);
            }
            
            // Create the chess board
            function createBoard() {
                // Clear existing board
                chessBoard.innerHTML = '';
                
                // Create 8x8 grid
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const square = document.createElement('div');
                        square.classList.add('chess-square');
                        
                        // Add white or black class for square color
                        if ((row + col) % 2 === 0) {
                            square.classList.add('white');
                        } else {
                            square.classList.add('black');
                        }
                        
                        // Set data attributes for position
                        square.setAttribute('data-row', row);
                        square.setAttribute('data-col', col);
                        
                        // Add rank and file labels (a-h, 1-8)
                        if (col === 0) {
                            const rankLabel = document.createElement('span');
                            rankLabel.classList.add('rank-label');
                            rankLabel.textContent = 8 - row;
                            square.appendChild(rankLabel);
                        }
                        
                        if (row === 7) {
                            const fileLabel = document.createElement('span');
                            fileLabel.classList.add('file-label');
                            fileLabel.textContent = String.fromCharCode(97 + col); // 'a' to 'h'
                            square.appendChild(fileLabel);
                        }
                        
                        // Add click event listener
                        square.addEventListener('click', handleSquareClick);
                        
                        chessBoard.appendChild(square);
                    }
                }
            }
            
            // Initialize fullscreen mode
            function initFullscreenMode() {
                const fullscreenButton = document.getElementById('fullscreen-button');
                const exitFullscreenButton = document.getElementById('exit-fullscreen-button');
                const fullscreenContainer = document.getElementById('fullscreen-container');
                
                // Enter fullscreen mode
                fullscreenButton.addEventListener('click', () => {
                    fullscreenContainer.classList.add('active');
                    
                    // Copy the current game state to the fullscreen board
                    updateFullscreenBoard();
                    
                    // Update the fullscreen scores
                    updateScores();
                    
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                });
                
                // Exit fullscreen mode
                exitFullscreenButton.addEventListener('click', () => {
                    fullscreenContainer.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                    
                    // Update the main board to match the fullscreen board state
                    updateMainBoard();
                });
                
                // Create the fullscreen board
                function createFullscreenBoard() {
                    fullscreenChessBoard.innerHTML = '';
                    
                    // Create 8x8 grid
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const square = document.createElement('div');
                            square.classList.add('chess-square');
                            
                            // Add white or black class for square color
                            if ((row + col) % 2 === 0) {
                                square.classList.add('white');
                            } else {
                                square.classList.add('black');
                            }
                            
                            // Set data attributes for position
                            square.setAttribute('data-row', row);
                            square.setAttribute('data-col', col);
                            
                            // Add rank and file labels (a-h, 1-8)
                            if (col === 0) {
                                const rankLabel = document.createElement('span');
                                rankLabel.classList.add('rank-label');
                                rankLabel.textContent = 8 - row;
                                square.appendChild(rankLabel);
                            }
                            
                            if (row === 7) {
                                const fileLabel = document.createElement('span');
                                fileLabel.classList.add('file-label');
                                fileLabel.textContent = String.fromCharCode(97 + col); // 'a' to 'h'
                                square.appendChild(fileLabel);
                            }
                            
                            // Add click event listener
                            square.addEventListener('click', handleSquareClick);
                            
                            fullscreenChessBoard.appendChild(square);
                        }
                    }
                }
                
                // Update the fullscreen board with the current game state
                function updateFullscreenBoard() {
                    createFullscreenBoard();
                    
                    // Update pieces on the fullscreen board
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const piece = board[row][col];
                            if (piece) {
                                const square = fullscreenChessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                                addPieceToSquare(square, piece.color, piece.type);
                            }
                        }
                    }
                    
                    // Update check indicators
                    updateCheckIndicators(fullscreenChessBoard);
                }
                
                // Update the main board with the current game state
                function updateMainBoard() {
                    // Update pieces on the main board
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const piece = board[row][col];
                            const square = chessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                            
                            // Clear existing piece
                            const existingPiece = square.querySelector('.chess-piece');
                            if (existingPiece) {
                                square.removeChild(existingPiece);
                            }
                            
                            // Add piece if present
                            if (piece) {
                                addPieceToSquare(square, piece.color, piece.type);
                            }
                        }
                    }
                    
                    // Update check indicators
                    updateCheckIndicators(chessBoard);
                }
                
                // Create the fullscreen board initially
                createFullscreenBoard();
                
                // Return both update functions for use in other functions
                return { updateFullscreenBoard, updateMainBoard };
            }
            
            // Setup chess pieces in their initial positions
            function setupPieces() {
                // Initialize empty board
                board = Array(8).fill().map(() => Array(8).fill(null));
                
                // Setup pawns
                for (let col = 0; col < 8; col++) {
                    board[1][col] = { type: 'pawn', color: 'black', hasMoved: false };
                    board[6][col] = { type: 'pawn', color: 'white', hasMoved: false };
                }
                
                // Setup other pieces
                const backRowPieces = ['rook', 'knight', 'bishop', 'queen', 'king', 'bishop', 'knight', 'rook'];
                
                for (let col = 0; col < 8; col++) {
                    board[0][col] = { type: backRowPieces[col], color: 'black', hasMoved: false };
                    board[7][col] = { type: backRowPieces[col], color: 'white', hasMoved: false };
                    
                    // Store king positions
                    if (backRowPieces[col] === 'king') {
                        kings.black = { row: 0, col: col };
                        kings.white = { row: 7, col: col };
                    }
                }
                
                // Add pieces to the board visually
                updateBoardDisplay();
            }
            
            // Update the visual display of the board
            function updateBoardDisplay() {
                // Clear all pieces
                const allSquares = chessBoard.querySelectorAll('.chess-square');
                allSquares.forEach(square => {
                    const pieceElement = square.querySelector('.chess-piece');
                    if (pieceElement) {
                        square.removeChild(pieceElement);
                    }
                });
                
                // Add pieces based on board state
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece) {
                            const square = chessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                            addPieceToSquare(square, piece.color, piece.type);
                        }
                    }
                }
                
                // Update check indicators
                updateCheckIndicators(chessBoard);
                
                // Update fullscreen board if active
                const fullscreenContainer = document.getElementById('fullscreen-container');
                if (fullscreenContainer.classList.contains('active')) {
                    const fullscreenSquares = fullscreenChessBoard.querySelectorAll('.chess-square');
                    fullscreenSquares.forEach(square => {
                        const pieceElement = square.querySelector('.chess-piece');
                        if (pieceElement) {
                            square.removeChild(pieceElement);
                        }
                    });
                    
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const piece = board[row][col];
                            if (piece) {
                                const square = fullscreenChessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                                addPieceToSquare(square, piece.color, piece.type);
                            }
                        }
                    }
                    
                    // Update check indicators
                    updateCheckIndicators(fullscreenChessBoard);
                }
                
                // No need to update piece colors
            }
            
            // Add a piece to a square
            function addPieceToSquare(square, color, type) {
                const pieceElement = document.createElement('div');
                pieceElement.classList.add('chess-piece');
                pieceElement.textContent = pieces[color][type];
                pieceElement.setAttribute('data-color', color);
                pieceElement.setAttribute('data-type', type);
                
                // Prevent any layout shifts
                pieceElement.style.transform = 'none';
                pieceElement.style.transition = 'none';
                pieceElement.style.width = '80%';
                pieceElement.style.height = '80%';
                pieceElement.style.boxSizing = 'border-box';
                pieceElement.style.margin = '0';
                pieceElement.style.padding = '0';
                
                // No need to change piece colors in dark mode
                
                square.appendChild(pieceElement);
            }
            
            // Handle square click
            function handleSquareClick(event) {
                if (!gameActive) return;
                
                // If it's computer's turn and playing against computer, don't allow clicks
                if (gameMode === 'computer' && currentPlayer === 'black') {
                    return;
                }
                
                let square = event.target;
                
                // If clicked on a piece, get its parent square
                if (square.classList.contains('chess-piece')) {
                    square = square.parentElement;
                }
                
                // Get row and column from data attributes
                const row = parseInt(square.getAttribute('data-row'));
                const col = parseInt(square.getAttribute('data-col'));
                
                // Check if there's a promotion pending
                if (promotionPending) {
                    return; // Don't allow moves while promotion is pending
                }
                
                // If a piece is already selected
                if (selectedPiece) {
                    // Check if the clicked square is a valid move
                    const isValidMove = validMoves.some(move => move.row === row && move.col === col);
                    
                    if (isValidMove) {
                        // Make the move
                        makeMove(selectedPiece.row, selectedPiece.col, row, col);
                        
                        // Clear selection and valid moves
                        clearSelection();
                    } else {
                        // Check if clicking on another piece of the same color
                        const piece = board[row][col];
                        if (piece && piece.color === currentPlayer) {
                            // Select the new piece
                            clearSelection();
                            selectPiece(row, col);
                        } else {
                            // Clear selection if clicking elsewhere
                            clearSelection();
                        }
                    }
                } else {
                    // Check if there's a piece on the clicked square
                    const piece = board[row][col];
                    if (piece && piece.color === currentPlayer) {
                        selectPiece(row, col);
                    }
                }
            }
            
            // Select a piece and show valid moves
            function selectPiece(row, col) {
                selectedPiece = { row, col };
                
                // Highlight the selected piece
                const square = getSquareElement(row, col);
                square.classList.add('selected');
                
                // Get valid moves for the selected piece
                validMoves = getValidMoves(row, col);
                
                // Highlight valid moves
                validMoves.forEach(move => {
                    const moveSquare = getSquareElement(move.row, move.col);
                    moveSquare.classList.add('valid-move');
                });
            }
            
            // Clear selection and valid move highlights
            function clearSelection() {
                if (selectedPiece) {
                    const square = getSquareElement(selectedPiece.row, selectedPiece.col);
                    square.classList.remove('selected');
                }
                
                // Remove valid move highlights
                const allSquares = document.querySelectorAll('.chess-square');
                allSquares.forEach(square => {
                    square.classList.remove('valid-move');
                });
                
                selectedPiece = null;
                validMoves = [];
            }
            
            // Get valid moves for a piece
            function getValidMoves(row, col) {
                const piece = board[row][col];
                if (!piece) return [];
                
                let moves = [];
                
                switch (piece.type) {
                    case 'pawn':
                        moves = getPawnMoves(row, col, piece.color);
                        break;
                    case 'rook':
                        moves = getRookMoves(row, col, piece.color);
                        break;
                    case 'knight':
                        moves = getKnightMoves(row, col, piece.color);
                        break;
                    case 'bishop':
                        moves = getBishopMoves(row, col, piece.color);
                        break;
                    case 'queen':
                        moves = getQueenMoves(row, col, piece.color);
                        break;
                    case 'king':
                        moves = getKingMoves(row, col, piece.color);
                        break;
                }
                
                // Filter out moves that would put or leave the king in check
                return moves.filter(move => {
                    // Simulate the move
                    const originalPiece = board[move.row][move.col];
                    const movingPiece = board[row][col];
                    
                    // Temporarily make the move
                    board[move.row][move.col] = movingPiece;
                    board[row][col] = null;
                    
                    // Update king position if moving the king
                    let originalKingPos = null;
                    if (movingPiece.type === 'king') {
                        originalKingPos = { ...kings[movingPiece.color] };
                        kings[movingPiece.color] = { row: move.row, col: move.col };
                    }
                    
                    // Check if the king is in check after the move
                    const kingPos = kings[movingPiece.color];
                    const inCheckAfterMove = isSquareAttacked(kingPos.row, kingPos.col, movingPiece.color === 'white' ? 'black' : 'white');
                    
                    // Undo the move
                    board[row][col] = movingPiece;
                    board[move.row][move.col] = originalPiece;
                    
                    // Restore king position if it was moved
                    if (originalKingPos) {
                        kings[movingPiece.color] = originalKingPos;
                    }
                    
                    // Return true if the move doesn't leave the king in check
                    return !inCheckAfterMove;
                });
            }
            
            // Get valid moves for a pawn
            function getPawnMoves(row, col, color) {
                const moves = [];
                const direction = color === 'white' ? -1 : 1; // White moves up, black moves down
                const startingRow = color === 'white' ? 6 : 1;
                
                // Forward move
                if (isValidPosition(row + direction, col) && !board[row + direction][col]) {
                    moves.push({ row: row + direction, col: col });
                    
                    // Double move from starting position
                    if (row === startingRow && !board[row + 2 * direction][col]) {
                        moves.push({ row: row + 2 * direction, col: col });
                    }
                }
                
                // Capture moves (diagonally)
                const captureCols = [col - 1, col + 1];
                captureCols.forEach(captureCol => {
                    if (isValidPosition(row + direction, captureCol)) {
                        const targetPiece = board[row + direction][captureCol];
                        if (targetPiece && targetPiece.color !== color) {
                            moves.push({ row: row + direction, col: captureCol });
                        }
                        
                        // En passant capture
                        // This would require tracking the last move, which we'll implement later
                    }
                });
                
                return moves;
            }
            
            // Get valid moves for a rook
            function getRookMoves(row, col, color) {
                return getStraightMoves(row, col, color);
            }
            
            // Get valid moves for a knight
            function getKnightMoves(row, col, color) {
                const moves = [];
                const knightMoves = [
                    { row: row - 2, col: col - 1 },
                    { row: row - 2, col: col + 1 },
                    { row: row - 1, col: col - 2 },
                    { row: row - 1, col: col + 2 },
                    { row: row + 1, col: col - 2 },
                    { row: row + 1, col: col + 2 },
                    { row: row + 2, col: col - 1 },
                    { row: row + 2, col: col + 1 }
                ];
                
                knightMoves.forEach(move => {
                    if (isValidPosition(move.row, move.col)) {
                        const targetPiece = board[move.row][move.col];
                        if (!targetPiece || targetPiece.color !== color) {
                            moves.push(move);
                        }
                    }
                });
                
                return moves;
            }
            
            // Get valid moves for a bishop
            function getBishopMoves(row, col, color) {
                return getDiagonalMoves(row, col, color);
            }
            
            // Get valid moves for a queen
            function getQueenMoves(row, col, color) {
                return [...getStraightMoves(row, col, color), ...getDiagonalMoves(row, col, color)];
            }
            
            // Get valid moves for a king
            function getKingMoves(row, col, color) {
                const moves = [];
                const directions = [
                    { row: -1, col: -1 }, { row: -1, col: 0 }, { row: -1, col: 1 },
                    { row: 0, col: -1 }, { row: 0, col: 1 },
                    { row: 1, col: -1 }, { row: 1, col: 0 }, { row: 1, col: 1 }
                ];
                
                directions.forEach(dir => {
                    const newRow = row + dir.row;
                    const newCol = col + dir.col;
                    
                    if (isValidPosition(newRow, newCol)) {
                        const targetPiece = board[newRow][newCol];
                        if (!targetPiece || targetPiece.color !== color) {
                            // Check if the square is not under attack
                            if (!isSquareAttacked(newRow, newCol, color === 'white' ? 'black' : 'white')) {
                                moves.push({ row: newRow, col: newCol });
                            }
                        }
                    }
                });
                
                // Castling
                if (!board[row][col].hasMoved && !inCheck[color]) {
                    // Kingside castling
                    if (board[row][7] && board[row][7].type === 'rook' && !board[row][7].hasMoved) {
                        if (!board[row][5] && !board[row][6]) {
                            if (!isSquareAttacked(row, 5, color === 'white' ? 'black' : 'white') &&
                                !isSquareAttacked(row, 6, color === 'white' ? 'black' : 'white')) {
                                moves.push({ row: row, col: 6, castling: 'kingside' });
                            }
                        }
                    }
                    
                    // Queenside castling
                    if (board[row][0] && board[row][0].type === 'rook' && !board[row][0].hasMoved) {
                        if (!board[row][1] && !board[row][2] && !board[row][3]) {
                            if (!isSquareAttacked(row, 2, color === 'white' ? 'black' : 'white') &&
                                !isSquareAttacked(row, 3, color === 'white' ? 'black' : 'white')) {
                                moves.push({ row: row, col: 2, castling: 'queenside' });
                            }
                        }
                    }
                }
                
                return moves;
            }
            
            // Get moves along straight lines (for rook and queen)
            function getStraightMoves(row, col, color) {
                const moves = [];
                const directions = [
                    { row: -1, col: 0 }, // Up
                    { row: 1, col: 0 },  // Down
                    { row: 0, col: -1 }, // Left
                    { row: 0, col: 1 }   // Right
                ];
                
                directions.forEach(dir => {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    
                    while (isValidPosition(newRow, newCol)) {
                        const targetPiece = board[newRow][newCol];
                        
                        if (!targetPiece) {
                            // Empty square, can move here
                            moves.push({ row: newRow, col: newCol });
                        } else {
                            // Square has a piece
                            if (targetPiece.color !== color) {
                                // Can capture opponent's piece
                                moves.push({ row: newRow, col: newCol });
                            }
                            break; // Stop in this direction
                        }
                        
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                });
                
                return moves;
            }
            
            // Get moves along diagonals (for bishop and queen)
            function getDiagonalMoves(row, col, color) {
                const moves = [];
                const directions = [
                    { row: -1, col: -1 }, // Up-left
                    { row: -1, col: 1 },  // Up-right
                    { row: 1, col: -1 },  // Down-left
                    { row: 1, col: 1 }    // Down-right
                ];
                
                directions.forEach(dir => {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    
                    while (isValidPosition(newRow, newCol)) {
                        const targetPiece = board[newRow][newCol];
                        
                        if (!targetPiece) {
                            // Empty square, can move here
                            moves.push({ row: newRow, col: newCol });
                        } else {
                            // Square has a piece
                            if (targetPiece.color !== color) {
                                // Can capture opponent's piece
                                moves.push({ row: newRow, col: newCol });
                            }
                            break; // Stop in this direction
                        }
                        
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                });
                
                return moves;
            }
            
            // Check if a position is valid (within the board)
            function isValidPosition(row, col) {
                return row >= 0 && row < 8 && col >= 0 && col < 8;
            }
            
            // Check if a square is under attack by the opponent
            function isSquareAttacked(row, col, attackerColor) {
                // Check for pawn attacks
                const pawnDirection = attackerColor === 'white' ? -1 : 1;
                const pawnAttackCols = [col - 1, col + 1];
                
                for (const attackCol of pawnAttackCols) {
                    const attackRow = row + pawnDirection;
                    if (isValidPosition(attackRow, attackCol)) {
                        const piece = board[attackRow][attackCol];
                        if (piece && piece.type === 'pawn' && piece.color === attackerColor) {
                            return true;
                        }
                    }
                }
                
                // Check for knight attacks
                const knightMoves = [
                    { row: row - 2, col: col - 1 },
                    { row: row - 2, col: col + 1 },
                    { row: row - 1, col: col - 2 },
                    { row: row - 1, col: col + 2 },
                    { row: row + 1, col: col - 2 },
                    { row: row + 1, col: col + 2 },
                    { row: row + 2, col: col - 1 },
                    { row: row + 2, col: col + 1 }
                ];
                
                for (const move of knightMoves) {
                    if (isValidPosition(move.row, move.col)) {
                        const piece = board[move.row][move.col];
                        if (piece && piece.type === 'knight' && piece.color === attackerColor) {
                            return true;
                        }
                    }
                }
                
                // Check for attacks along straight lines (rook, queen)
                const straightDirections = [
                    { row: -1, col: 0 }, // Up
                    { row: 1, col: 0 },  // Down
                    { row: 0, col: -1 }, // Left
                    { row: 0, col: 1 }   // Right
                ];
                
                for (const dir of straightDirections) {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    
                    while (isValidPosition(newRow, newCol)) {
                        const piece = board[newRow][newCol];
                        
                        if (piece) {
                            if (piece.color === attackerColor && 
                                (piece.type === 'rook' || piece.type === 'queen')) {
                                return true;
                            }
                            break; // Stop in this direction
                        }
                        
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                }
                
                // Check for attacks along diagonals (bishop, queen)
                const diagonalDirections = [
                    { row: -1, col: -1 }, // Up-left
                    { row: -1, col: 1 },  // Up-right
                    { row: 1, col: -1 },  // Down-left
                    { row: 1, col: 1 }    // Down-right
                ];
                
                for (const dir of diagonalDirections) {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    
                    while (isValidPosition(newRow, newCol)) {
                        const piece = board[newRow][newCol];
                        
                        if (piece) {
                            if (piece.color === attackerColor && 
                                (piece.type === 'bishop' || piece.type === 'queen')) {
                                return true;
                            }
                            break; // Stop in this direction
                        }
                        
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                }
                
                // Check for king attacks (adjacent squares)
                const kingMoves = [
                    { row: row - 1, col: col - 1 }, { row: row - 1, col: col }, { row: row - 1, col: col + 1 },
                    { row: row, col: col - 1 }, { row: row, col: col + 1 },
                    { row: row + 1, col: col - 1 }, { row: row + 1, col: col }, { row: row + 1, col: col + 1 }
                ];
                
                for (const move of kingMoves) {
                    if (isValidPosition(move.row, move.col)) {
                        const piece = board[move.row][move.col];
                        if (piece && piece.type === 'king' && piece.color === attackerColor) {
                            return true;
                        }
                    }
                }
                
                return false;
            }
            
            // Make a move
            function makeMove(fromRow, fromCol, toRow, toCol) {
                const piece = board[fromRow][fromCol];
                const targetPiece = board[toRow][toCol];
                
                // Save move for undo
                moveHistory.push({
                    from: { row: fromRow, col: fromCol },
                    to: { row: toRow, col: toCol },
                    piece: { ...piece },
                    captured: targetPiece ? { ...targetPiece } : null,
                    kings: { ...kings },
                    inCheck: { ...inCheck }
                });
                
                // Handle castling
                if (piece.type === 'king' && Math.abs(fromCol - toCol) === 2) {
                    // Kingside castling
                    if (toCol === 6) {
                        board[fromRow][5] = board[fromRow][7]; // Move rook
                        board[fromRow][7] = null;
                        board[fromRow][5].hasMoved = true;
                    }
                    // Queenside castling
                    else if (toCol === 2) {
                        board[fromRow][3] = board[fromRow][0]; // Move rook
                        board[fromRow][0] = null;
                        board[fromRow][3].hasMoved = true;
                    }
                }
                
                // Handle captures
                if (targetPiece) {
                    capturedPieces[piece.color].push(targetPiece);
                    updateCapturedPieces();
                    
                    // Check if a king was captured (this shouldn't happen in standard chess, but we'll handle it)
                    if (targetPiece.type === 'king') {
                        // End the game immediately
                        gameActive = false;
                        
                        // Update score
                        if (targetPiece.color === 'white') {
                            scores.black++;
                            showNotification(messages.blackWin + " King captured!", 3000);
                        } else {
                            scores.white++;
                            showNotification(messages.whiteWin + " King captured!", 3000);
                        }
                        
                        updateScores();
                        
                        // Update the board to show the final position
                        board[toRow][toCol] = piece;
                        board[fromRow][fromCol] = null;
                        updateBoardDisplay();
                        
                        return; // Skip the rest of the move processing
                    }
                }
                
                // Move the piece
                board[toRow][toCol] = piece;
                board[fromRow][fromCol] = null;
                
                // Mark the piece as moved
                board[toRow][toCol].hasMoved = true;
                
                // Update king position if king was moved
                if (piece.type === 'king') {
                    kings[piece.color] = { row: toRow, col: toCol };
                }
                
                // Check for pawn promotion
                if (piece.type === 'pawn' && (toRow === 0 || toRow === 7)) {
                    promotionPending = { row: toRow, col: toCol, color: piece.color };
                    showPromotionOptions(piece.color);
                    return; // Wait for promotion choice
                }
                
                // Complete the move
                completeMove();
            }
            
            // Complete the move after any pending actions (like promotion)
            function completeMove() {
                // Switch player
                currentPlayer = currentPlayer === 'white' ? 'black' : 'white';
                
                // Update the board display
                updateBoardDisplay();
                
                // Check for check, checkmate, or stalemate
                checkGameStatus();
                
                // If it's computer's turn and playing against computer, make computer move
                if (gameActive && gameMode === 'computer' && currentPlayer === 'black') {
                    // Add a slight delay before computer moves
                    setTimeout(makeComputerMove, 500);
                }
            }
            
            // Show promotion options
            function showPromotionOptions(color) {
                promotionModal.classList.add('active');
                promotionOptions.innerHTML = '';
                
                const pieceTypes = ['queen', 'rook', 'bishop', 'knight'];
                
                pieceTypes.forEach(type => {
                    const pieceElement = document.createElement('div');
                    pieceElement.classList.add('promotion-piece');
                    pieceElement.textContent = pieces[color][type];
                    pieceElement.setAttribute('data-type', type);
                    
                    pieceElement.addEventListener('click', () => {
                        promotePawn(type);
                    });
                    
                    promotionOptions.appendChild(pieceElement);
                });
            }
            
            // Promote a pawn
            function promotePawn(pieceType) {
                if (!promotionPending) return;
                
                // Replace the pawn with the chosen piece
                board[promotionPending.row][promotionPending.col].type = pieceType;
                
                // Close the promotion modal
                promotionModal.classList.remove('active');
                
                // Clear promotion pending
                promotionPending = null;
                
                // Complete the move
                completeMove();
            }
            
            // Check game status (check, checkmate, stalemate)
            function checkGameStatus() {
                // Reset check status
                inCheck.white = false;
                inCheck.black = false;
                
                // Check if kings are in check
                if (isSquareAttacked(kings.white.row, kings.white.col, 'black')) {
                    inCheck.white = true;
                }
                
                if (isSquareAttacked(kings.black.row, kings.black.col, 'white')) {
                    inCheck.black = true;
                }
                
                // Update check indicators
                updateCheckIndicators(chessBoard);
                
                // Check if current player has any valid moves
                const hasValidMoves = checkForValidMoves(currentPlayer);
                
                // Update game status
                if (inCheck[currentPlayer]) {
                    if (!hasValidMoves) {
                        // Checkmate
                        gameActive = false;
                        
                        // Update score
                        if (currentPlayer === 'white') {
                            scores.black++;
                            showNotification(messages.blackWin, 2000);
                        } else {
                            scores.white++;
                            showNotification(messages.whiteWin, 2000);
                        }
                        
                        updateScores();
                    } else {
                        // Check
                        showNotification(messages.check, 1500);
                    }
                } else if (!hasValidMoves) {
                    // Stalemate
                    gameActive = false;
                    showNotification(messages.stalemate, 2000);
                } else {
                    // Normal play
                    // Current player's turn
                }
            }
            
            // Check if a player has any valid moves
            function checkForValidMoves(player) {
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece && piece.color === player) {
                            const moves = getValidMoves(row, col);
                            if (moves.length > 0) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }
            
            // Update check indicators
            function updateCheckIndicators(boardElement) {
                // Remove all check indicators
                const allSquares = boardElement.querySelectorAll('.chess-square');
                allSquares.forEach(square => {
                    square.classList.remove('check');
                });
                
                // Add check indicator to king's square if in check
                if (inCheck.white) {
                    const kingSquare = boardElement.querySelector(`[data-row="${kings.white.row}"][data-col="${kings.white.col}"]`);
                    if (kingSquare) kingSquare.classList.add('check');
                }
                
                if (inCheck.black) {
                    const kingSquare = boardElement.querySelector(`[data-row="${kings.black.row}"][data-col="${kings.black.col}"]`);
                    if (kingSquare) kingSquare.classList.add('check');
                }
            }
            
            // Make a computer move
            function makeComputerMove() {
                if (!gameActive || currentPlayer !== 'black') return;
                
                let bestMove = null;
                let bestScore = -Infinity;
                
                // Collect all possible moves for black pieces
                const possibleMoves = [];
                
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece && piece.color === 'black') {
                            const moves = getValidMoves(row, col);
                            moves.forEach(move => {
                                possibleMoves.push({
                                    fromRow: row,
                                    fromCol: col,
                                    toRow: move.row,
                                    toCol: move.col
                                });
                            });
                        }
                    }
                }
                
                // Evaluate each move
                possibleMoves.forEach(move => {
                    // Simulate the move
                    const piece = board[move.fromRow][move.fromCol];
                    const targetPiece = board[move.toRow][move.toCol];
                    
                    // Temporarily make the move
                    board[move.toRow][move.toCol] = piece;
                    board[move.fromRow][move.fromCol] = null;
                    
                    // Update king position if king was moved
                    let originalKingPos = null;
                    if (piece.type === 'king') {
                        originalKingPos = { ...kings.black };
                        kings.black = { row: move.toRow, col: move.toCol };
                    }
                    
                    // Evaluate the position
                    let score;
                    
                    if (difficulty === 'easy') {
                        // Easy: Random with slight preference for captures
                        score = Math.random() * 10 + (targetPiece ? getPieceValue(targetPiece.type) : 0);
                    } else if (difficulty === 'normal') {
                        // Normal: Basic material evaluation
                        score = evaluateBoard();
                    } else {
                        // Hard: Look ahead one move
                        score = evaluateBoard();
                        
                        // Check if this move puts white in check
                        if (isSquareAttacked(kings.white.row, kings.white.col, 'black')) {
                            score += 50; // Bonus for check
                        }
                        
                        // Check if this move defends against check
                        if (inCheck.black && !isSquareAttacked(kings.black.row, kings.black.col, 'white')) {
                            score += 30; // Bonus for defending against check
                        }
                    }
                    
                    // Undo the move
                    board[move.fromRow][move.fromCol] = piece;
                    board[move.toRow][move.toCol] = targetPiece;
                    
                    // Restore king position if it was moved
                    if (originalKingPos) {
                        kings.black = originalKingPos;
                    }
                    
                    // Update best move
                    if (score > bestScore) {
                        bestScore = score;
                        bestMove = move;
                    }
                });
                
                // Make the best move
                if (bestMove) {
                    makeMove(bestMove.fromRow, bestMove.fromCol, bestMove.toRow, bestMove.toCol);
                }
            }
            
            // Evaluate the board position
            function evaluateBoard() {
                let score = 0;
                
                // Material value
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece) {
                            const pieceValue = getPieceValue(piece.type);
                            if (piece.color === 'black') {
                                score += pieceValue;
                            } else {
                                score -= pieceValue;
                            }
                        }
                    }
                }
                
                return score;
            }
            
            // Get the value of a piece
            function getPieceValue(pieceType) {
                switch (pieceType) {
                    case 'pawn': return 1;
                    case 'knight': return 3;
                    case 'bishop': return 3;
                    case 'rook': return 5;
                    case 'queen': return 9;
                    case 'king': return 100;
                    default: return 0;
                }
            }
            
            // Undo the last move
            function undoMove() {
                if (moveHistory.length === 0) return;
                
                const lastMove = moveHistory.pop();
                
                // Restore the piece to its original position
                board[lastMove.from.row][lastMove.from.col] = lastMove.piece;
                
                // Restore captured piece if any
                board[lastMove.to.row][lastMove.to.col] = lastMove.captured;
                
                // Restore king positions
                kings = { ...lastMove.kings };
                
                // Restore check status
                inCheck = { ...lastMove.inCheck };
                
                // Handle castling undo
                if (lastMove.piece.type === 'king' && Math.abs(lastMove.from.col - lastMove.to.col) === 2) {
                    // Kingside castling
                    if (lastMove.to.col === 6) {
                        board[lastMove.from.row][7] = board[lastMove.from.row][5]; // Move rook back
                        board[lastMove.from.row][5] = null;
                        board[lastMove.from.row][7].hasMoved = false;
                    }
                    // Queenside castling
                    else if (lastMove.to.col === 2) {
                        board[lastMove.from.row][0] = board[lastMove.from.row][3]; // Move rook back
                        board[lastMove.from.row][3] = null;
                        board[lastMove.from.row][0].hasMoved = false;
                    }
                }
                
                // Remove the last captured piece from the list
                if (lastMove.captured) {
                    const color = lastMove.piece.color;
                    capturedPieces[color].pop();
                    updateCapturedPieces();
                }
                
                // Switch back to the previous player
                currentPlayer = currentPlayer === 'white' ? 'black' : 'white';
                
                // Update the board display
                updateBoardDisplay();
                
                // Update game status
                gameActive = true;
            }
            
            // Update captured pieces display
            function updateCapturedPieces() {
                // Clear captured pieces display
                whiteCaptured.innerHTML = '';
                blackCaptured.innerHTML = '';
                
                // Add white's captured pieces
                capturedPieces.white.forEach(piece => {
                    const pieceElement = document.createElement('div');
                    pieceElement.classList.add('captured-piece');
                    pieceElement.textContent = pieces.black[piece.type];
                    whiteCaptured.appendChild(pieceElement);
                });
                
                // Add black's captured pieces
                capturedPieces.black.forEach(piece => {
                    const pieceElement = document.createElement('div');
                    pieceElement.classList.add('captured-piece');
                    pieceElement.textContent = pieces.white[piece.type];
                    blackCaptured.appendChild(pieceElement);
                });
            }
            
            // Update scores
            function updateScores() {
                scoreWhite.textContent = scores.white;
                scoreBlack.textContent = scores.black;
                
                // Update fullscreen scores if they exist
                const fullscreenScoreWhite = document.getElementById('fullscreen-score-white');
                const fullscreenScoreBlack = document.getElementById('fullscreen-score-black');
                
                if (fullscreenScoreWhite) fullscreenScoreWhite.textContent = scores.white;
                if (fullscreenScoreBlack) fullscreenScoreBlack.textContent = scores.black;
            }
            
            // Reset the game
            function resetGame() {
                currentPlayer = 'white';
                gameActive = true;
                selectedPiece = null;
                validMoves = [];
                moveHistory = [];
                capturedPieces = { white: [], black: [] };
                inCheck = { white: false, black: false };
                promotionPending = null;
                
                // Close promotion modal if open
                promotionModal.classList.remove('active');
                
                // Setup pieces
                setupPieces();
                
                // Update captured pieces display
                updateCapturedPieces();
                
                // Show notification
                showNotification(messages.gameStart, 1500);
                
                // Update fullscreen board if active
                const fullscreenContainer = document.getElementById('fullscreen-container');
                if (fullscreenContainer.classList.contains('active')) {
                    const { updateFullscreenBoard } = initFullscreenMode();
                    updateFullscreenBoard();
                }
            }
            
            // Reset scores
            function resetScores() {
                scores.white = 0;
                scores.black = 0;
                updateScores();
            }
            
            // Show notification
            function showNotification(message, duration = 2000) {
                // Clear any existing timeout
                if (window.notificationTimeout) {
                    clearTimeout(window.notificationTimeout);
                }
                
                // Remove existing show class if present
                notification.classList.remove('show');
                
                // Set the message
                notification.textContent = message;
                
                // Small delay before showing to ensure animation works properly
                setTimeout(() => {
                    notification.classList.add('show');
                    
                    // Set timeout to hide notification
                    window.notificationTimeout = setTimeout(() => {
                        notification.classList.remove('show');
                    }, duration);
                }, 50);
            }
            
            // Helper function to get a square element by row and column
            function getSquareElement(row, col) {
                // Check if we're in fullscreen mode
                const fullscreenContainer = document.getElementById('fullscreen-container');
                const isFullscreen = fullscreenContainer.classList.contains('active');
                
                // Get the appropriate board
                const board = isFullscreen ? fullscreenChessBoard : chessBoard;
                
                return board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            }
            
            // Initialize the game
            initGame();
        });
    </script>
</body>
</html>