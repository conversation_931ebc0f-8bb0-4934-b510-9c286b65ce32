<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opiskelen Suomea - Finnish Language Learning</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="#">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="index-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="#">Home</a></li>
                <li><a href="audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="#daily-life">Daily Life</a>
                        <a href="#web-development">Web Development</a>
                        <a href="#cleaner">Cleaner</a>
                        <a href="#kitchen-assistant">Kitchen Assistant</a>
                        <a href="#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="index-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="index-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <section class="intro">
            <h2>Welcome to Opiskelen Suomea</h2>
            <p>This resource is designed to help professionals learn Finnish language skills specific to their work environments. Our structured approach focuses on practical vocabulary, common phrases, and cultural context needed in Finnish workplaces.</p>
            <p>Each chapter includes audio recordings, vocabulary lists, grammar points, and realistic dialogues to help you develop confidence in using Finnish in professional settings.</p>
        </section>

        <section id="structure" class="structure-section">
            <h2>Chapter Structure</h2>
            <p>Each chapter follows this consistent structure:</p>
            <ul class="structure-list">
                <li><strong>Title</strong>: Chapter number and title in both Finnish and English</li>
                <li><strong>Objectives</strong>: Detailed learning goals for the chapter</li>
                <li><strong>Audio Duration</strong>: Planned length of the audio recording (5 minutes)</li>
                <li><strong>Vocabulary</strong>: Key words or phrases with translations</li>
                <li><strong>Grammar Points</strong>: Important grammar concepts explained with examples</li>
                <li><strong>Script & Notes</strong>: Dialogues with pronunciation guides in parentheses</li>
                <li><strong>Cultural Notes</strong>: Insights into Finnish workplace practices</li>
            </ul>
        </section>

        <section id="categories" class="categories-section">
            <h2>Professional Categories</h2>
            <p>Our chapters are organized by professional categories to help you focus on the specific vocabulary and situations relevant to your field.</p>

            <div class="category-tabs">
                <button class="tab-button active" onclick="openCategory(event, 'daily-life')">Daily Life</button>
                <button class="tab-button" onclick="openCategory(event, 'web-development')">Web Development IT</button>
                <button class="tab-button" onclick="openCategory(event, 'cleaner')">Cleaner</button>
                <button class="tab-button" onclick="openCategory(event, 'kitchen-assistant')">Kitchen Assistant</button>
                <button class="tab-button" onclick="openCategory(event, 'warehouse')">Warehouse</button>
            </div>

            <!-- Daily Life Category -->
            <div id="daily-life" class="category-content tab-content">
                <div class="category-header">
                    <h2>Daily Life / Arkielämä</h2>
                    <p>Chapters 1-30</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on common everyday situations and conversations, helping you navigate daily life in Finland with confidence.</p>
                </div>
                <div class="chapter-grid" id="daily-life-chapters">
                    <!-- Chapters will be dynamically loaded here -->
                    <!-- Chapter 1 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="1">
                        <div class="chapter-header">
                            <span class="chapter-number">1</span>
                            Aamutervehdykset / Morning Greetings
                        </div>
                        <div class="chapter-body">
                            <p>Learn essential morning greetings and basic conversation starters for everyday interactions.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 2 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="2">
                        <div class="chapter-header">
                            <span class="chapter-number">2</span>
                            Perhe ja ystävät / Family and Friends
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to family members, relationships, and social connections.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 3 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="3">
                        <div class="chapter-header">
                            <span class="chapter-number">3</span>
                            Koti ja asuminen / Home and Living
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to housing, furniture, and daily activities at home.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 4 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="4">
                        <div class="chapter-header">
                            <span class="chapter-number">4</span>
                            Päivittäinen ostokset / Daily Shopping
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary for shopping, prices, and common items you need to purchase regularly.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 5 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="5">
                        <div class="chapter-header">
                            <span class="chapter-number">5</span>
                            Liikenne ja ajoneuvot / Transport and Vehicles
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to public transportation, directions, and getting around in Finland.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 6 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="6">
                        <div class="chapter-header">
                            <span class="chapter-number">6</span>
                            Terveys ja hyvinvointi / Health and Wellness
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to health, doctor visits, and describing how you feel.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 7 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="7">
                        <div class="chapter-header">
                            <span class="chapter-number">7</span>
                            Sää ja vuodenajat / Weather and Seasons
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to weather conditions, seasons, and climate in Finland.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 8 -->
                    <div class="chapter-card daily-life" data-category="daily-life" data-chapter="8">
                        <div class="chapter-header">
                            <span class="chapter-number">8</span>
                            Vapaa-aika ja harrastukset / Leisure and Hobbies
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to free time activities, hobbies, and recreation in Finland.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="load-more-btn" onclick="loadMoreChapters('daily-life', 9, 30)">
                            Load More Chapters (9-30)
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Web Development IT Category -->
            <div id="web-development" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Web Development IT / Verkkokehitys IT</h2>
                    <p>Chapters 31-50</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on technical vocabulary for IT professionals, covering web development concepts, programming, and digital technologies in Finnish.</p>
                </div>
                <div class="chapter-grid" id="web-development-chapters">
                    <!-- Chapter 31 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="31">
                        <div class="chapter-header">
                            <span class="chapter-number">31</span>
                            HTML perusteet / HTML Basics
                        </div>
                        <div class="chapter-body">
                            <p>Learn fundamental HTML terminology and concepts for web development in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 32 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="32">
                        <div class="chapter-header">
                            <span class="chapter-number">32</span>
                            CSS tyylittely / CSS Styling
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to CSS styling, layout, and design concepts in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 33 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="33">
                        <div class="chapter-header">
                            <span class="chapter-number">33</span>
                            Responsiivinen suunnittelu / Responsive Design
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to responsive web design and mobile-friendly development.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 34 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="34">
                        <div class="chapter-header">
                            <span class="chapter-number">34</span>
                            JavaScript perusteet / JavaScript Basics
                        </div>
                        <div class="chapter-body">
                            <p>Master essential JavaScript terminology and programming concepts in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 35 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="35">
                        <div class="chapter-header">
                            <span class="chapter-number">35</span>
                            DOM-manipulointi / DOM Manipulation
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to Document Object Model and dynamic web content.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 36 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="36">
                        <div class="chapter-header">
                            <span class="chapter-number">36</span>
                            Lomakkeet ja validointi / Forms and Validation
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to web forms, user input, and data validation.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 37 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="37">
                        <div class="chapter-header">
                            <span class="chapter-number">37</span>
                            AJAX ja API-kutsut / AJAX and API Calls
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to asynchronous requests, APIs, and data fetching in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 38 -->
                    <div class="chapter-card web-development" data-category="web-development" data-chapter="38">
                        <div class="chapter-header">
                            <span class="chapter-number">38</span>
                            Tietokannat ja SQL / Databases and SQL
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to databases, SQL queries, and data management in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="load-more-btn" onclick="loadMoreChapters('web-development', 39, 50)">
                            Load More Chapters (39-50)
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Cleaner Category -->
            <div id="cleaner" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Cleaner / Siivooja</h2>
                    <p>Chapters 51-60</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on vocabulary for cleaning professionals, covering cleaning techniques, equipment, and workplace communication in Finnish.</p>
                </div>
                <div class="chapter-grid" id="cleaner-chapters">
                    <!-- Chapter 51 -->
                    <div class="chapter-card cleaner" data-category="cleaner" data-chapter="51">
                        <div class="chapter-header">
                            <span class="chapter-number">51</span>
                            Siivousvälineet / Cleaning Equipment
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to cleaning tools, equipment, and supplies used in professional cleaning.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 52 -->
                    <div class="chapter-card cleaner" data-category="cleaner" data-chapter="52">
                        <div class="chapter-header">
                            <span class="chapter-number">52</span>
                            Puhdistusaineet / Cleaning Agents
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to cleaning chemicals, solutions, and their proper usage.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 53 -->
                    <div class="chapter-card cleaner" data-category="cleaner" data-chapter="53">
                        <div class="chapter-header">
                            <span class="chapter-number">53</span>
                            Siivoustekniikat / Cleaning Techniques
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to professional cleaning methods and procedures.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 54 -->
                    <div class="chapter-card cleaner" data-category="cleaner" data-chapter="54">
                        <div class="chapter-header">
                            <span class="chapter-number">54</span>
                            Työturvallisuus / Work Safety
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to safety procedures and precautions in cleaning work.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 55 -->
                    <div class="chapter-card cleaner" data-category="cleaner" data-chapter="55">
                        <div class="chapter-header">
                            <span class="chapter-number">55</span>
                            Eri tilojen siivous / Cleaning Different Spaces
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary specific to cleaning various environments like offices, hotels, and public spaces.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 56 -->
                    <div class="chapter-card cleaner" data-category="cleaner" data-chapter="56">
                        <div class="chapter-header">
                            <span class="chapter-number">56</span>
                            Asiakaspalvelu / Customer Service
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary for interacting with clients and responding to cleaning requests.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="load-more-btn" onclick="loadMoreChapters('cleaner', 57, 60)">
                            Load More Chapters (57-60)
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Kitchen Assistant Category -->
            <div id="kitchen-assistant" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Kitchen Assistant / Keittiöapulainen</h2>
                    <p>Chapters 61-70</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on terms for working in food service, covering kitchen equipment, food preparation, and restaurant operations in Finnish.</p>
                </div>
                <div class="chapter-grid" id="kitchen-assistant-chapters">
                    <!-- Chapter 61 -->
                    <div class="chapter-card kitchen-assistant" data-category="kitchen-assistant" data-chapter="61">
                        <div class="chapter-header">
                            <span class="chapter-number">61</span>
                            Keittiön perusteet / Kitchen Basics
                        </div>
                        <div class="chapter-body">
                            <p>Learn fundamental vocabulary for kitchen environments, including areas, roles, and basic operations.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 62 -->
                    <div class="chapter-card kitchen-assistant" data-category="kitchen-assistant" data-chapter="62">
                        <div class="chapter-header">
                            <span class="chapter-number">62</span>
                            Ruoanvalmistusmenetelmät / Cooking Methods
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to different cooking techniques and food preparation methods.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 63 -->
                    <div class="chapter-card kitchen-assistant" data-category="kitchen-assistant" data-chapter="63">
                        <div class="chapter-header">
                            <span class="chapter-number">63</span>
                            Keittiövälineet ja -laitteet / Kitchen Utensils and Equipment
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary for kitchen tools, appliances, and equipment used in professional kitchens.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 64 -->
                    <div class="chapter-card kitchen-assistant" data-category="kitchen-assistant" data-chapter="64">
                        <div class="chapter-header">
                            <span class="chapter-number">64</span>
                            Ruoan käsittely ja säilytys / Food Handling and Storage
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to food safety, storage methods, and proper handling procedures.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 65 -->
                    <div class="chapter-card kitchen-assistant" data-category="kitchen-assistant" data-chapter="65">
                        <div class="chapter-header">
                            <span class="chapter-number">65</span>
                            Keittiön puhtaus ja hygienia / Kitchen Cleanliness and Hygiene
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to maintaining cleanliness and hygiene standards in professional kitchens.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 66 -->
                    <div class="chapter-card kitchen-assistant" data-category="kitchen-assistant" data-chapter="66">
                        <div class="chapter-header">
                            <span class="chapter-number">66</span>
                            Erityisruokavaliot ja allergiat / Special Diets and Allergies
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary for different dietary requirements, allergies, and food restrictions.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="load-more-btn" onclick="loadMoreChapters('kitchen-assistant', 67, 70)">
                            Load More Chapters (67-70)
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Warehouse Category -->
            <div id="warehouse" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Warehouse / Varasto</h2>
                    <p>Chapters 71-80</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on language for logistics and warehouse operations, covering inventory management, shipping, and warehouse procedures in Finnish.</p>
                </div>
                <div class="chapter-grid" id="warehouse-chapters">
                    <!-- Chapter 71 -->
                    <div class="chapter-card warehouse" data-category="warehouse" data-chapter="71">
                        <div class="chapter-header">
                            <span class="chapter-number">71</span>
                            Vastaanotto ja tarkastus / Receiving and Inspection
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to receiving shipments, checking goods, and processing incoming inventory.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 72 -->
                    <div class="chapter-card warehouse" data-category="warehouse" data-chapter="72">
                        <div class="chapter-header">
                            <span class="chapter-number">72</span>
                            Varastointitekniikat / Storage Techniques
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to different storage methods, organization systems, and warehouse layout.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 73 -->
                    <div class="chapter-card warehouse" data-category="warehouse" data-chapter="73">
                        <div class="chapter-header">
                            <span class="chapter-number">73</span>
                            Tilausten keräily / Order Picking
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary for picking orders, fulfillment processes, and inventory selection.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 74 -->
                    <div class="chapter-card warehouse" data-category="warehouse" data-chapter="74">
                        <div class="chapter-header">
                            <span class="chapter-number">74</span>
                            Pakkaaminen ja lähettäminen / Packing and Shipping
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to packaging materials, shipping procedures, and order fulfillment.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 75 -->
                    <div class="chapter-card warehouse" data-category="warehouse" data-chapter="75">
                        <div class="chapter-header">
                            <span class="chapter-number">75</span>
                            Varaston turvallisuus / Warehouse Safety
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary related to safety procedures, hazard prevention, and emergency protocols in warehouses.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 76 -->
                    <div class="chapter-card warehouse" data-category="warehouse" data-chapter="76">
                        <div class="chapter-header">
                            <span class="chapter-number">76</span>
                            Trukit ja laitteet / Forklifts and Equipment
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary for warehouse equipment, machinery operation, and maintenance procedures.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="load-more-btn" onclick="loadMoreChapters('warehouse', 77, 80)">
                            Load More Chapters (77-80)
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Technology Category -->
            <div id="technology" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Technology / Teknologia</h2>
                    <p>Chapters 21-30</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on IT, software development, technical support, and digital technology vocabulary in Finnish professional contexts.</p>
                </div>
                <div class="chapter-grid">
                    <!-- Chapter 21 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">21</span>
                            IT-perusteet / IT Basics
                        </div>
                        <div class="chapter-body">
                            <p>Learn fundamental vocabulary for IT environments, including hardware, software, and basic computing concepts.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapters 22-30 would be added here -->
                </div>
            </div>

            <!-- Hospitality Category -->
            <div id="hospitality" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Hospitality / Vieraanvaraisuus</h2>
                    <p>Chapters 31-40</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on hotel operations, restaurant service, tourism, and customer service in the hospitality industry.</p>
                </div>
                <div class="chapter-grid">
                    <!-- Chapter 31 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">31</span>
                            Hotellin vastaanotto / Hotel Reception
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary for check-in, check-out, and handling guest inquiries at a hotel front desk in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Add more hospitality chapters (32-40) -->
                    <!-- Chapters 32-40 would be added here -->
                </div>
            </div>

            <!-- Education Category -->
            <div id="education" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Education / Koulutus</h2>
                    <p>Chapters 41-50</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on teaching, classroom management, educational administration, and academic vocabulary in Finnish.</p>
                </div>
                <div class="chapter-grid">
                    <!-- Chapter 41 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">41</span>
                            Luokkahuone / Classroom
                        </div>
                        <div class="chapter-body">
                            <p>Learn vocabulary for classroom management, teaching activities, and interacting with students in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Add more education chapters (42-50) -->
                    <!-- Chapters 42-50 would be added here -->
                </div>
            </div>

            <!-- Construction Category -->
            <div id="construction" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Construction / Rakentaminen</h2>
                    <p>Chapters 51-60</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on construction site vocabulary, building materials, safety procedures, and project management in Finnish.</p>
                </div>
                <div class="chapter-grid">
                    <!-- Chapter 51 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">51</span>
                            Rakennustyömaan perusteet / Construction Site Basics
                        </div>
                        <div class="chapter-body">
                            <p>Learn fundamental vocabulary for construction sites, including areas, equipment, and basic safety procedures.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Add more construction chapters (52-60) -->
                    <!-- Chapters 52-60 would be added here -->
                </div>
            </div>

            <!-- Kitchen Assistant Category -->
            <div id="kitchen" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Kitchen Assistant / Keittiöapulainen</h2>
                    <p>Chapters 61-70</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on kitchen operations, food preparation, culinary vocabulary, and restaurant kitchen procedures in Finnish.</p>
                </div>
                <div class="chapter-grid">
                    <!-- Chapter 61 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">61</span>
                            Keittiön perusteet / Kitchen Basics
                        </div>
                        <div class="chapter-body">
                            <p>Learn fundamental vocabulary and concepts for working in a Finnish kitchen environment. This chapter covers kitchen hierarchy, basic food preparation terminology, and essential phrases for your first day in a Finnish kitchen.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 30 terms</span>
                        </div>
                    </div>

                    <!-- Chapter 62 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">62</span>
                            Ruoanvalmistusmenetelmät / Cooking Methods
                        </div>
                        <div class="chapter-body">
                            <p>Understand different cooking techniques and methods used in Finnish kitchens. Learn vocabulary for boiling, frying, baking, steaming, and other preparation methods, along with temperature terminology and cooking instructions.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 40 terms</span>
                        </div>
                    </div>

                    <!-- Chapter 63 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">63</span>
                            Keittiövälineet ja -laitteet / Kitchen Utensils & Equipment
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to tools, utensils, and equipment used in professional kitchens. From knives and cutting boards to industrial ovens and specialized appliances, this chapter covers all essential equipment terminology.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 64 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">64</span>
                            Ruoan käsittely ja säilytys / Food Handling & Storage
                        </div>
                        <div class="chapter-body">
                            <p>Learn terms and procedures for properly handling and storing food items safely. This chapter covers temperature control, contamination prevention, proper storage techniques, and food safety regulations in Finland.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 65 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">65</span>
                            Keittiön puhtaus ja hygienia / Kitchen Cleanliness & Hygiene
                        </div>
                        <div class="chapter-body">
                            <p>Understand cleaning protocols, hygiene standards, and sanitation procedures in a kitchen. Learn vocabulary for cleaning products, sanitizing methods, personal hygiene requirements, and Finnish health regulations for food establishments.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 66 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">66</span>
                            Erityisruokavaliot ja allergiat / Special Diets & Allergies
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary related to dietary restrictions, allergies, and special meal preparation. This chapter covers common allergens, religious dietary restrictions, vegetarian/vegan options, and how to communicate about food sensitivities in Finnish.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 67 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">67</span>
                            Asiakaspalvelu keittiössä / Customer Service in Kitchen
                        </div>
                        <div class="chapter-body">
                            <p>Learn terms related to customer interactions, handling requests, and food service. This chapter focuses on taking orders, addressing complaints, explaining dishes, and providing excellent service in the Finnish context.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 68 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">68</span>
                            Työturvallisuus keittiössä / Work Safety in Kitchen
                        </div>
                        <div class="chapter-body">
                            <p>Understand vocabulary and procedures for maintaining safety in kitchen operations. Learn terms for accident prevention, emergency procedures, proper handling of dangerous equipment, and Finnish workplace safety regulations.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 69 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">69</span>
                            Tilaukset ja varastonhallinta / Orders and Inventory Management
                        </div>
                        <div class="chapter-body">
                            <p>Learn terminology related to ordering supplies and managing kitchen inventory. This chapter covers stock rotation, inventory tracking, placing orders with suppliers, and efficient management of kitchen resources.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Chapter 70 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">70</span>
                            Tiimityö ja viestintä keittiössä / Teamwork and Communication in Kitchen
                        </div>
                        <div class="chapter-body">
                            <p>Master vocabulary for effective communication and collaboration in a kitchen environment. Learn how to coordinate with colleagues, report issues, request assistance, and function as part of a cohesive kitchen team in Finland.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 20+ terms</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Retail Category -->
            <div id="retail" class="category-content tab-content" style="display:none">
                <div class="category-header">
                    <h2>Retail / Vähittäiskauppa</h2>
                    <p>Chapters 71-80</p>
                </div>
                <div class="category-description">
                    <p>These chapters focus on retail operations, sales, customer service, and inventory management in Finnish retail environments.</p>
                </div>
                <div class="chapter-grid">
                    <!-- Chapter 71 -->
                    <div class="chapter-card">
                        <div class="chapter-header">
                            <span class="chapter-number">71</span>
                            Myymälän perusteet / Retail Store Basics
                        </div>
                        <div class="chapter-body">
                            <p>Learn fundamental vocabulary for retail environments, including store layout, departments, and basic retail operations.</p>
                        </div>
                        <div class="chapter-footer">
                            <span><i class="fas fa-clock"></i> 5 min</span>
                            <span><i class="fas fa-list"></i> 15+ terms</span>
                        </div>
                    </div>

                    <!-- Add more retail chapters (72-80) -->
                    <!-- Chapters 72-80 would be added here -->
                </div>
            </div>
        </section>

        <section id="about" class="intro">
            <h2>Using These Materials</h2>
            <p>These comprehensive chapters are designed to help professionals develop practical Finnish language skills specific to their work environments. Each chapter builds on previous knowledge while introducing new vocabulary and grammar concepts relevant to specific professional contexts.</p>

            <h3 style="color: var(--primary-color); margin-top: 1rem;">Study Recommendations:</h3>
            <ul class="structure-list">
                <li><strong>Sequential Learning</strong>: Work through chapters in order within your professional category, as later chapters build on vocabulary introduced earlier.</li>
                <li><strong>Pronunciation Practice</strong>: Use the syllable-by-syllable pronunciation guides (in parentheses) to practice authentic Finnish pronunciation.</li>
                <li><strong>Dialogue Practice</strong>: Role-play the conversations with a study partner to build conversational fluency.</li>
                <li><strong>Vocabulary Reinforcement</strong>: Create flashcards with the key terms from each chapter, reviewing them regularly.</li>
                <li><strong>Grammar Application</strong>: Complete the example sentences for each grammar point, then try creating your own sentences using the same structures.</li>
                <li><strong>Cultural Integration</strong>: Pay special attention to the cultural notes, as understanding Finnish workplace culture is as important as learning the language.</li>
                <li><strong>Audio Resources</strong>: Use text-to-speech tools to create audio versions of the dialogues for listening practice.</li>
            </ul>
        </section>
    </div>



    <!-- Highlight mode notification -->
    <div id="highlight-notification" class="highlight-notification">
        <i class="fas fa-highlighter"></i> Highlight mode enabled. Select text to highlight it.
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Opiskelen Suomea provides Finnish language learning resources specifically designed for professional environments and workplace integration in Finland.</p>
                <div class="footer-social">
                    <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Categories</h3>
                <ul class="footer-links">
                    <li><a href="#daily-life"><i class="fas fa-angle-right"></i> Daily Life</a></li>
                    <li><a href="#web-development"><i class="fas fa-angle-right"></i> Web Development</a></li>
                    <li><a href="#cleaner"><i class="fas fa-angle-right"></i> Cleaner</a></li>
                    <li><a href="#kitchen-assistant"><i class="fas fa-angle-right"></i> Kitchen Assistant</a></li>
                    <li><a href="#warehouse"><i class="fas fa-angle-right"></i> Warehouse</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="#"><i class="fas fa-angle-right"></i> Home</a></li>
                    <li><a href="audio.html"><i class="fas fa-angle-right"></i> Audio</a></li>
                    <li><a href="video.html"><i class="fas fa-angle-right"></i> Videos</a></li>
                    <li><a href="#about"><i class="fas fa-angle-right"></i> About</a></li>
                    <li><a href="#structure"><i class="fas fa-angle-right"></i> Learning Structure</a></li>
                </ul>
            </div>

            <div class="footer-section footer-contact">
                <h3>Contact Us</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> +358 40 123 4567</p>
                <p><i class="fas fa-map-marker-alt"></i> Helsinki, Finland</p>
            </div>

            <div class="footer-bottom">
                <p class="copyright">&copy; 2024 Opiskelen Suomea. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="highlight.js"></script>
    <script>
        // Override the toggleDarkMode function from script.js
        function toggleDarkMode() {
            console.log("Custom toggleDarkMode called");

            // Toggle dark mode class
            document.body.classList.toggle('dark-mode');

            // Save preference to localStorage
            const isDarkMode = document.body.classList.contains('dark-mode');
            console.log("Setting darkMode to:", isDarkMode ? 'enabled' : 'disabled');
            localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');

            // Update desktop icon
            const desktopIcon = document.querySelector('#index-toggle-dark i');
            if (desktopIcon) {
                if (isDarkMode) {
                    desktopIcon.classList.remove('fa-moon');
                    desktopIcon.classList.add('fa-sun');
                } else {
                    desktopIcon.classList.remove('fa-sun');
                    desktopIcon.classList.add('fa-moon');
                }
            }

            // Update mobile icon
            const mobileIcon = document.querySelector('#index-toggle-dark-mobile i');
            if (mobileIcon) {
                if (isDarkMode) {
                    mobileIcon.classList.remove('fa-moon');
                    mobileIcon.classList.add('fa-sun');
                } else {
                    mobileIcon.classList.remove('fa-sun');
                    mobileIcon.classList.add('fa-moon');
                }
            }

            console.log("Dark mode is now:", document.body.classList.contains('dark-mode') ? 'enabled' : 'disabled');
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM Content Loaded");

            // Apply dark mode from localStorage
            const savedDarkMode = localStorage.getItem('darkMode');
            console.log("Saved dark mode:", savedDarkMode);

            if (savedDarkMode === 'enabled') {
                console.log("Applying dark mode from localStorage");
                document.body.classList.add('dark-mode');

                // Update icons to sun
                const desktopIcon = document.querySelector('#index-toggle-dark i');
                if (desktopIcon) {
                    desktopIcon.classList.remove('fa-moon');
                    desktopIcon.classList.add('fa-sun');
                }

                const mobileIcon = document.querySelector('#index-toggle-dark-mobile i');
                if (mobileIcon) {
                    mobileIcon.classList.remove('fa-moon');
                    mobileIcon.classList.add('fa-sun');
                }
            }

            // Check if highlight mode was previously enabled
            if (localStorage.getItem('highlightMode') === 'enabled') {
                document.body.classList.add('highlight-mode');
                // Set cursor to indicate highlight mode
                document.body.style.cursor = 'text';
                // Add active-tool class to the highlight button
                const highlightBtn = document.getElementById('index-toggle-highlight');
                if (highlightBtn) {
                    highlightBtn.classList.add('active-tool');
                }
                // Add event listener for text selection
                document.addEventListener('mouseup', handleTextSelection);
            }

            // Dark mode toggle for index page - desktop version
            const darkModeToggle = document.getElementById('index-toggle-dark');
            if (darkModeToggle) {
                console.log("Adding click event to desktop toggle");
                darkModeToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log("Desktop toggle clicked");
                    toggleDarkMode();
                });
            }

            // Dark mode toggle for index page - mobile version
            const darkModeToggleMobile = document.getElementById('index-toggle-dark-mobile');
            if (darkModeToggleMobile) {
                console.log("Adding click event to mobile toggle");
                darkModeToggleMobile.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log("Mobile toggle clicked");
                    toggleDarkMode();
                });
            }

            // Highlight toggle
            const highlightToggle = document.getElementById('index-toggle-highlight');
            if (highlightToggle) {
                highlightToggle.addEventListener('click', function() {
                    document.body.classList.toggle('highlight-mode');
                    // Toggle active-tool class on the button
                    this.classList.toggle('active-tool');

                    // Change cursor to indicate highlight mode
                    if (document.body.classList.contains('highlight-mode')) {
                        document.body.style.cursor = 'text';

                        // Show notification
                        const notification = document.getElementById('highlight-notification');
                        if (notification) {
                            notification.style.display = 'block';
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    notification.style.display = 'none';
                                }, 500);
                            }, 3000);
                        }
                    } else {
                        document.body.style.cursor = '';

                        // Remove all highlights
                        removeAllHighlights();
                    }

                    localStorage.setItem('highlightMode', document.body.classList.contains('highlight-mode') ? 'enabled' : 'disabled');
                });
            }

            // Function to handle text selection
            function handleTextSelection() {
                if (!document.body.classList.contains('highlight-mode')) return;

                const selection = window.getSelection();
                if (selection.toString().length > 0) {
                    // Create a span element to wrap the selected text
                    const range = selection.getRangeAt(0);
                    const selectedText = range.extractContents();
                    const span = document.createElement('span');
                    span.className = 'user-highlight';
                    span.appendChild(selectedText);
                    range.insertNode(span);

                    // Clear the selection
                    selection.removeAllRanges();
                }
            }

            // Function to remove all highlights
            function removeAllHighlights() {
                const highlights = document.querySelectorAll('.user-highlight');
                highlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    while (highlight.firstChild) {
                        parent.insertBefore(highlight.firstChild, highlight);
                    }
                    parent.removeChild(highlight);
                });
            }
        });
    </script>
    <script src="script.js">        
        // Migrate highlight settings from other localStorage variables
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we need to migrate from other highlight variables
            if (!localStorage.getItem('highlightMode')) {
                // Check if we have settings in other variables
                if (localStorage.getItem('highlightMode') === 'enabled') { localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') { localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
        });        // Check for highlight settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Migrate old settings if needed
            if (!localStorage.getItem('highlightMode')) {
                const oldHighlightState = localStorage.getItem('highlight');
                if (oldHighlightState === 'enabled') {
                    localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') {
                    localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
            
            // Apply highlight state on page load
            const isHighlighted = localStorage.getItem('highlightMode') === 'enabled';
            if (isHighlighted) {
                const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                document.body.classList.add(document.body.classList.contains('highlight-mode') ? 'highlight-mode' : 'highlight-enabled');
                
                // Add background color to Finnish words if they exist
                const finnishWords = document.querySelectorAll('.finnish');
                if (finnishWords.length > 0) {
                    finnishWords.forEach(function(element) {
                        element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                    });
                }
            }
        });        // Function to toggle text highlighting
        function toggleHighlight() {
            const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
            
            if (document.body.classList.contains('highlight-mode') || document.body.classList.contains('highlight-enabled')) {
                // Disable highlighting
                document.body.classList.remove('highlight-mode');
                document.body.classList.remove('highlight-enabled');
                localStorage.setItem('highlightMode', 'disabled');
                
                if (highlightButton) {
                    highlightButton.classList.remove('active');
                    highlightButton.classList.remove('active-tool');
                }
                
                // Remove background color from Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = '';
                });
            } else {
                // Enable highlighting
                document.body.classList.add(document.querySelector('.highlight-mode') !== null ? 'highlight-mode' : 'highlight-enabled');
                localStorage.setItem('highlightMode', 'enabled');
                
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                
                // Add background color to Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                });
            }
        }
        
        // Check for highlight settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Migrate old settings if needed
            if (!localStorage.getItem('highlightMode')) {
                const oldHighlightState = localStorage.getItem('highlight');
                if (oldHighlightState === 'enabled') {
                    localStorage.setItem('highlightMode', 'enabled');
                } else if (localStorage.getItem('highlightEnabled') === 'true') {
                    localStorage.setItem('highlightMode', 'enabled');
                }
                
                // Clean up old variables
                localStorage.removeItem('highlight');
                localStorage.removeItem('highlightEnabled');
            }
            
            // Apply highlight state on page load
            const isHighlighted = localStorage.getItem('highlightMode') === 'enabled';
            if (isHighlighted) {
                const highlightButton = document.querySelector('[id$="-toggle-highlight"]');
                if (highlightButton) {
                    highlightButton.classList.add(highlightButton.classList.contains('compact-nav-button') ? 'active-tool' : 'active');
                }
                
                document.body.classList.add(document.querySelector('.highlight-mode') !== null ? 'highlight-mode' : 'highlight-enabled');
                
                // Add background color to Finnish words if they exist
                const finnishWords = document.querySelectorAll('.finnish');
                if (finnishWords.length > 0) {
                    finnishWords.forEach(function(element) {
                        element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                    });
                }
            }
        });</script>
</body>

<script>
// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // Function to handle hash changes and switch tabs
    function handleHashChange() {
        if (window.location.hash) {
            const categoryId = window.location.hash.substring(1); // Remove the # character
            const tabButton = document.querySelector(`.tab-button[onclick*="${categoryId}"]`);
            if (tabButton) {
                console.log("Switching to tab:", categoryId);
                tabButton.click(); // Simulate a click on the tab button
            }
        }
    }
    
    // Check hash on page load
    handleHashChange();
    
    // Also listen for hash changes while the page is loaded
    window.addEventListener('hashchange', handleHashChange);

    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        console.log("Setting up mobile menu toggle");
        mobileMenuToggle.addEventListener('click', function(e) {
            console.log("Mobile menu toggle clicked");
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Handle dropdown menus in mobile view
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const dropbtn = dropdown.querySelector('.dropbtn');
        if (dropbtn) {
            dropbtn.addEventListener('click', function(e) {
                // Only in mobile view
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('active');
                }
            });
        }
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (navLinks && navLinks.classList.contains('show')) {
            // Check if click is outside the nav menu
            if (!navLinks.contains(e.target) && e.target !== mobileMenuToggle) {
                navLinks.classList.remove('show');
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                    
                    // Change icon back to bars
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-bars';
                    }
                }
            }
        }
    });
});
</script>
</html>




