<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> (Memory Game) - <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../styles.css">
    <style>
        * {
            box-sizing: border-box; /* Apply to all elements */
        }
        
        html, body {
            overflow-x: hidden; /* Prevent horizontal scrolling */
            max-width: 100%;
        }
        
        /* Mobile menu specific styles */
        @media (max-width: 767px) {
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            .dark-mode .nav-links.show {
                background-color: #252525 !important;
            }
            
            /* Dropdown styles for mobile */
            .dropdown-content {
                display: none !important;
            }
            
            .dropdown.active .dropdown-content {
                display: block !important;
                position: static !important;
                width: 100% !important;
                background-color: rgba(0, 0, 0, 0.03) !important;
                box-shadow: none !important;
                border-top: 1px solid #eee !important;
            }
            
            .dark-mode .dropdown.active .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05) !important;
                border-top: 1px solid #333 !important;
            }
        }
        
        /* Game specific styles */
        .game-container {
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow-x: hidden; /* Prevent horizontal scrolling */
            box-sizing: border-box; /* Include padding in width calculation */
            overflow-y: auto; /* Allow vertical scrolling if needed */
        }
        
        .main-content {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            box-sizing: border-box;
            width: 100%;
            overflow-x: hidden;
        }
        
        .dark-mode .game-container {
            background-color: #1e1e1e;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .game-top-row {
            width: 100%;
            margin-bottom: 10px; /* Reduced margin */
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 8px; /* Reduced padding */
            display: flex;
            flex-direction: column;
            gap: 8px; /* Reduced gap */
        }
        
        @media (max-width: 767px) {
            .game-top-row {
                padding: 10px;
                gap: 0;
            }
        }
        
        .dark-mode .game-top-row {
            background-color: #252525;
        }
        
        .control-row {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        /* Mobile layout for control row - 4 rows */
        @media (max-width: 767px) {
            .control-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .game-title, .game-stats, .game-options, .game-actions {
                width: 100%;
            }
            
            .game-stats {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                margin: 8px 0;
                border-top: 1px solid #eee;
                border-bottom: 1px solid #eee;
            }
            
            .dark-mode .game-stats {
                border-top: 1px solid #333;
                border-bottom: 1px solid #333;
            }
            
            .game-options {
                display: flex;
                justify-content: space-between;
            }
            
            .game-actions {
                display: flex;
                justify-content: space-between;
            }
        }
        
        .game-title {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.2rem;
            white-space: nowrap;
        }
        
        @media (max-width: 767px) {
            .game-title {
                text-align: center;
                width: 100%;
                font-size: 1.4rem;
                margin-bottom: 5px;
            }
        }
        
        .game-stats {
            display: flex;
            gap: 12px;
            flex: 1;
            min-width: 0;
            flex-wrap: wrap;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: nowrap;
        }
        
        @media (max-width: 767px) {
            .stat-item {
                flex-direction: column;
                align-items: center;
                gap: 2px;
            }
        }
        
        .stat-label {
            font-weight: bold;
            font-size: 0.8rem;
            color: #666;
        }
        
        .dark-mode .stat-label {
            color: #aaa;
        }
        
        .stat-value {
            font-size: 0.95rem;
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .game-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        @media (max-width: 767px) {
            .game-actions {
                padding-top: 5px;
                justify-content: space-between;
                gap: 5px;
            }
            
            .game-button, .back-button {
                flex: 1;
                max-width: 32%;
                padding: 8px 5px;
                font-size: 0.9rem;
            }
        }
        
        .game-button, .back-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            cursor: pointer;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            white-space: nowrap;
            height: 30px; /* Fixed height for all buttons */
            line-height: 18px; /* Center text vertically */
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .game-button:hover, .back-button:hover {
            background-color: #002a66;
            transform: translateY(-2px);
        }
        
        .game-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .dark-mode .game-button:disabled {
            background-color: #555555;
        }
        
        .game-options {
            display: flex;
            gap: 10px;
            min-width: 0;
            flex-wrap: wrap; /* Allow wrapping on small screens */
        }
        
        @media (max-width: 767px) {
            .game-options {
                padding: 5px 0;
                justify-content: space-between;
                gap: 5px;
            }
            
            .option-group {
                flex: 1;
                max-width: 48%;
            }
            
            .option-group select {
                width: 100%;
            }
        }
        
        .option-group {
            display: flex;
            align-items: center;
            gap: 5px;
            min-width: 0;
        }
        
        .option-group label {
            font-weight: bold;
            font-size: 0.8rem;
            white-space: nowrap;
        }
        
        .game-select {
            padding: 4px 6px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #fff;
            font-size: 0.8rem;
            cursor: pointer;
            min-width: 0;
            width: 110px; /* Increased width to show full content */
        }
        
        .dark-mode .game-select {
            background-color: #333;
            border-color: #444;
            color: #fff;
        }
        
        .memory-board {
            display: grid;
            grid-template-columns: repeat(4, minmax(0, 1fr)); /* Default 4 columns as a safe option */
            gap: 10px 10px; /* Increased gap between cards */
            margin: 0 auto 15px auto;
            width: 100%;
            max-width: 100%;
            max-height: calc(100vh - 220px); /* Increased max-height to accommodate more cards */
            overflow-y: auto; /* Allow vertical scrolling if needed */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            justify-content: center;
            align-items: center;
            align-content: start; /* Start from the top */
            padding-bottom: 20px; /* Add padding at bottom for scrolling */
            padding-left: 0;
            padding-right: 0;
            box-sizing: border-box;
        }
        
        .memory-card {
            aspect-ratio: 2.5; /* Much wider than tall - about 1/3 of previous height */
            background-color: var(--primary-color);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 0.85rem; /* Smaller font */
            font-weight: bold;
            color: white;
            perspective: 1000px;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.5s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            min-height: 65px; /* Minimum height of 65px */
            margin: 0; /* Reset margin */
            z-index: 1; /* Base z-index */
            width: 100%; /* Ensure full width */
            box-sizing: border-box; /* Include padding in width calculation */
        }
        
        .memory-card.flipped {
            transform: rotateY(180deg);
            z-index: 2; /* Higher z-index when flipped */
        }
        
        .memory-card.matched {
            cursor: default;
            box-shadow: 0 0 5px rgba(0, 102, 204, 0.5);
        }
        
        .memory-card-front, 
        .memory-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            top: 0;
            left: 0;
            -webkit-backface-visibility: hidden; /* Safari support */
        }
        
        .memory-card-front {
            background-color: var(--primary-color);
            transform: rotateY(0deg);
            z-index: 1;
        }
        
        .memory-card-back {
            background-color: white;
            color: var(--text-color);
            transform: rotateY(180deg);
            padding: 5px 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #eaeaea;
            word-break: break-word;
            hyphens: auto;
            font-size: 0.85rem; /* Slightly larger font size */
            line-height: 1.2; /* Slightly increased line height */
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 0;
            overflow: hidden; /* Prevent text overflow */
            text-overflow: ellipsis; /* Add ellipsis for overflowing text */
        }
        
        /* Fix for 3D transform issues */
        .memory-card, .memory-card-front, .memory-card-back {
            -webkit-transform-style: preserve-3d;
            transform-style: preserve-3d;
            will-change: transform;
        }
        
        /* Smooth scrolling for memory board */
        .memory-board {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) #f0f0f0;
            scroll-behavior: smooth;
        }
        
        .memory-board::-webkit-scrollbar {
            width: 8px;
        }
        
        .memory-board::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 4px;
        }
        
        .memory-board::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 4px;
        }
        
        .dark-mode .memory-board::-webkit-scrollbar-track {
            background: #333;
        }
        
        /* Ensure cards don't overlap during animation */
        .memory-board {
            transform-style: flat;
            -webkit-transform-style: flat;
            width: 100% !important;
            max-width: 100% !important;
            box-sizing: border-box !important;
        }
        
        .dark-mode .memory-card-back {
            background-color: #333;
            color: white;
            border-color: #444;
        }
        
        /* Language-specific card styling */
        .finnish-card .memory-card-back {
            background-color: #e6f0ff;
            border-left: 3px solid #0066cc;
        }
        
        .english-card .memory-card-back {
            background-color: #f9f9f9;
            border-left: 3px solid #4d4d4d;
        }
        
        .dark-mode .finnish-card .memory-card-back {
            background-color: #1a2940;
            border-left: 3px solid #3399ff;
        }
        
        .dark-mode .english-card .memory-card-back {
            background-color: #2a2a2a;
            border-left: 3px solid #999;
        }
        
        .game-sidebar {
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .dark-mode .game-sidebar {
            background-color: #252525;
        }
        
        .game-main {
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .dark-mode .game-main {
            background-color: #252525;
        }
        
        .difficulty-selector, .category-selector {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .difficulty-button, .category-button {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            text-align: left;
        }
        
        .difficulty-button.active, .category-button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .dark-mode .difficulty-button, .dark-mode .category-button {
            background-color: #333;
            border-color: #444;
        }
        
        .dark-mode .difficulty-button.active, .dark-mode .category-button.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .game-message {
            text-align: center;
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.95rem;
            font-weight: bold;
            display: none;
        }
        
        .game-message.success {
            background-color: rgba(0, 102, 204, 0.2);
            color: var(--primary-color);
            display: block;
        }
        
        .dark-mode .game-message.success {
            background-color: rgba(0, 102, 204, 0.3);
            color: var(--primary-color);
        }
        
        /* Responsive styles */
        @media (min-width: 1200px) {
            .memory-board {
                grid-template-columns: repeat(8, minmax(0, 1fr)); /* 8 columns for extra large screens */
                max-height: calc(100vh - 220px);
                gap: 9px;
            }
        }
        
        @media (min-width: 992px) and (max-width: 1199px) {
            .memory-board {
                grid-template-columns: repeat(6, minmax(0, 1fr)); /* 6 columns for large screens */
                max-height: calc(100vh - 220px);
                gap: 8px;
            }
        }
        
        @media (min-width: 768px) and (max-width: 991px) {
            .memory-board {
                grid-template-columns: repeat(4, minmax(0, 1fr)); /* 4 columns for medium screens */
                gap: 7px;
                max-height: calc(100vh - 220px);
            }
            
            .memory-card {
                aspect-ratio: 2.2; /* Slightly different ratio for smaller screens */
                min-height: 60px; /* Slightly smaller minimum height for smaller screens */
            }
            
            .memory-card-back {
                font-size: 0.75rem;
                padding: 2px;
                line-height: 1;
            }
            
            .control-row {
                flex-wrap: wrap;
                gap: 10px;
                justify-content: center;
            }
            
            .game-title {
                width: 100%;
                text-align: center;
                margin-bottom: 5px;
            }
            
            .game-stats {
                order: 2;
                width: 100%;
                justify-content: center;
                margin-bottom: 5px;
            }
            
            .game-options {
                order: 3;
                justify-content: center;
                margin-bottom: 5px;
            }
            
            .game-actions {
                order: 4;
                width: 100%;
                justify-content: center;
            }
        }
        
        @media (min-width: 576px) and (max-width: 767px) {
            .game-select {
                width: 100px; /* Slightly smaller on small screens */
            }
            .memory-board {
                grid-template-columns: repeat(4, minmax(0, 1fr)); /* 4 columns for small screens */
                gap: 6px;
                max-height: calc(100vh - 220px);
            }
            
            .memory-card {
                aspect-ratio: 2; /* Wider cards for small screens */
                min-height: 55px; /* Smaller minimum height for small screens */
            }
        }
        
        @media (min-width: 480px) and (max-width: 575px) {
            .game-container {
                padding: 10px; /* Reduce padding on very small screens */
            }
            .game-select {
                width: 90px; /* Even smaller on very small screens */
            }
            .memory-board {
                grid-template-columns: repeat(3, minmax(0, 1fr)); /* 3 columns for very small screens */
                gap: 5px;
                max-height: calc(100vh - 200px);
            }
            
            .memory-card {
                aspect-ratio: 1.8; /* Slightly less wide for very small screens */
                min-height: 50px; /* Smallest minimum height for very small screens */
            }
            
            .memory-card-back {
                font-size: 0.7rem;
                line-height: 1;
                padding: 2px;
            }
            
            .game-top-row {
                padding: 8px;
            }
            
            .game-title {
                font-size: 1rem;
            }
            
            .stat-item {
                gap: 3px;
            }
            
            .stat-label {
                font-size: 0.7rem;
            }
            
            .stat-value {
                font-size: 0.8rem;
            }
            
            .game-button, .back-button {
                padding: 4px 6px;
                font-size: 0.75rem;
            }
            
            .option-group label {
                font-size: 0.7rem;
            }
            
            .game-select {
                font-size: 0.7rem;
                padding: 3px 5px;
                width: 70px;
            }
        }
        
        @media (max-width: 479px) {
            .memory-board {
                grid-template-columns: repeat(4, minmax(0, 1fr)); /* 4 columns for mobile screens */
                gap: 3px;
            }
            
            .memory-card {
                aspect-ratio: 1; /* Square cards for better fit with 4 columns */
                min-height: 35px; /* Smaller height to fit more cards */
            }
            
            .memory-card-back {
                font-size: 0.65rem;
                padding: 1px;
                line-height: 1.1;
            }
            
            .game-stats {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .stat-item {
                flex: 1 0 30%;
                justify-content: center;
            }
            
            .game-actions {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .game-button, .back-button {
                flex: 1 0 40%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="memory-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../index.html">Home</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content">
                        <a href="../video.html?channel=kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../video.html?channel=finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=yleareena">Yle Areena 1</a>
                        <a href="../video.html?channel=yleareena2">Yle Areena 2</a>
                        <a href="../video.html?channel=yleareena3">Yle Areena 3</a>
                        <a href="../video.html?channel=yleareena4">Yle Areena 4</a>
                        <a href="../video.html?channel=yleareena5">Yle Areena 5</a>
                        <a href="../video.html?channel=pipsapossu">Pipsa Possu</a>
                        <a href="../video.html?channel=katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../audio.html">Audio</a></li>
                <li><a href="../finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../index.html#daily-life">Daily Life</a>
                        <a href="../index.html#web-development">Web Development</a>
                        <a href="../index.html#cleaner">Cleaner</a>
                        <a href="../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../games.html">Games</a>
                    </div>
                </li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="memory-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <div class="game-container">
            <!-- Single row control panel -->
            <div class="game-top-row">
                <!-- All controls in a single row -->
                <div class="control-row">
                    <h3 class="game-title">Muistipeli</h3>
                    
                    <div class="game-stats">
                        <div class="stat-item">
                            <span class="stat-label">Moves:</span>
                            <span class="stat-value" id="moves-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Pairs:</span>
                            <span class="stat-value" id="pairs-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Time:</span>
                            <span class="stat-value" id="time-count">00:00</span>
                        </div>
                    </div>
                    
                    <div class="game-options">
                        <div class="option-group">
                            <label>Words:</label>
                            <select id="category-select" class="game-select">
                                <option value="greetings" selected>Greetings</option>
                                <option value="food">Food</option>
                                <option value="colors">Colors</option>
                                <option value="family">Family</option>
                                <option value="numbers">Numbers</option>
                                <option value="time">Time</option>
                                <option value="common_words">Common</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>Level:</label>
                            <select id="difficulty-select" class="game-select">
                                <option value="easy" selected>Easy</option>
                                <option value="medium">Medium</option>
                                <option value="hard">Hard</option>
                                <option value="best">Best</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="game-actions">
                        <button class="game-button" id="start-button">Start</button>
                        <button class="game-button" id="reset-button" disabled>Reset</button>
                        <a href="../games.html" class="back-button">Back</a>
                    </div>
                </div>
            </div>
            
            <!-- Game message -->
            <div class="game-message" id="game-message"></div>
            
            <!-- Learning progress for common words -->
            <div id="learning-progress" style="display: none; margin: 15px 0; padding: 10px; background-color: #f5f5f5; border-radius: 6px; text-align: center;">
                <h4 style="margin: 0 0 10px 0; color: #0066cc;">Learning Progress</h4>
                <div id="progress-stats" style="display: flex; justify-content: space-around; flex-wrap: wrap;">
                    <div>
                        <span style="font-weight: bold;">Words Mastered:</span>
                        <span id="words-mastered">0</span>/2000
                    </div>
                    <div>
                        <span style="font-weight: bold;">Words In Progress:</span>
                        <span id="words-in-progress">0</span>
                    </div>
                    <div>
                        <span style="font-weight: bold;">Words Remaining:</span>
                        <span id="words-remaining">2000</span>
                    </div>
                </div>
            </div>
            
            <!-- Memory board below -->
            <div class="memory-board" id="memory-board">
                <!-- Cards will be generated here -->
            </div>
        </div>
    </div>

    <!-- Footer removed to allow more space for game board -->

    <script>
        // Function to get word match count from localStorage
        function getWordMatchCount(finnish, english) {
            const key = `memory_match_${finnish}_${english}`;
            return parseInt(localStorage.getItem(key) || '0');
        }
        
        // Function to increment word match count in localStorage
        function incrementWordMatchCount(finnish, english) {
            const key = `memory_match_${finnish}_${english}`;
            const currentCount = getWordMatchCount(finnish, english);
            localStorage.setItem(key, (currentCount + 1).toString());
        }
        
        // Function to fetch vocabulary from HTML files or JSON
        async function fetchVocabulary(category) {
            // Special handling for common_words category
            if (category === 'common_words') {
                try {
                    const response = await fetch('../finnish_vocabulary.json');
                    if (!response.ok) {
                        throw new Error('Failed to fetch vocabulary data');
                    }
                    
                    const allWords = await response.json();
                    const vocabulary = [];
                    
                    // Filter out words that have been matched 10 or more times
                    for (const word of allWords) {
                        const matchCount = getWordMatchCount(word.finnish, word.english);
                        if (matchCount < 10) {
                            vocabulary.push({
                                finnish: word.finnish,
                                english: word.english,
                                matchCount: matchCount,
                                rank: word.rank
                            });
                        }
                    }
                    
                    // If we have fewer than 8 words (minimum for easy mode), use fallback
                    if (vocabulary.length < 8) {
                        console.warn('Not enough unlearned words. Using fallback vocabulary.');
                        return getFallbackVocabulary(category);
                    }
                    
                    return vocabulary;
                } catch (error) {
                    console.error('Error fetching common words:', error);
                    return getFallbackVocabulary(category);
                }
            }
            
            // Regular category handling
            const categoryMap = {
                'greetings': '../finnish_grammar/vocabulary/common_words/greetings.html',
                'food': '../finnish_grammar/vocabulary/common_words/food.html',
                'colors': '../finnish_grammar/vocabulary/common_words/colors.html',
                'family': '../finnish_grammar/vocabulary/common_words/family.html',
                'numbers': '../finnish_grammar/vocabulary/common_words/numbers.html',
                'time': '../finnish_grammar/vocabulary/common_words/time.html'
            };
            
            const url = categoryMap[category];
            
            try {
                const response = await fetch(url);
                const html = await response.text();
                
                // Create a temporary DOM element to parse the HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // Find all vocabulary tables
                const tables = doc.querySelectorAll('.vocabulary-table');
                const vocabulary = [];
                
                // Extract vocabulary from tables
                tables.forEach(table => {
                    const rows = table.querySelectorAll('tr');
                    
                    // Skip the header row
                    for (let i = 1; i < rows.length; i++) {
                        const cells = rows[i].querySelectorAll('td');
                        if (cells.length >= 3) {
                            const finnish = cells[0].textContent.trim();
                            const english = cells[2].textContent.trim();
                            
                            // Only add if both Finnish and English are present
                            if (finnish && english) {
                                vocabulary.push({ finnish, english });
                            }
                        }
                    }
                });
                
                return vocabulary;
            } catch (error) {
                console.error('Error fetching vocabulary:', error);
                // Return fallback vocabulary if fetch fails
                return getFallbackVocabulary(category);
            }
        }
        
        // Fallback vocabulary in case fetch fails
        function getFallbackVocabulary(category) {
            const fallbackSets = {
                'common_words': [
                    { finnish: "Minä", english: "I" },
                    { finnish: "Sinä", english: "You" },
                    { finnish: "Hän", english: "He/She" },
                    { finnish: "Me", english: "We" },
                    { finnish: "Te", english: "You (plural)" },
                    { finnish: "He", english: "They" },
                    { finnish: "Olla", english: "To be" },
                    { finnish: "Mennä", english: "To go" },
                    { finnish: "Tulla", english: "To come" },
                    { finnish: "Tehdä", english: "To do" },
                    { finnish: "Saada", english: "To get" },
                    { finnish: "Nähdä", english: "To see" },
                    { finnish: "Kuulla", english: "To hear" },
                    { finnish: "Tietää", english: "To know" },
                    { finnish: "Haluta", english: "To want" },
                    { finnish: "Puhua", english: "To speak" },
                    { finnish: "Lukea", english: "To read" },
                    { finnish: "Kirjoittaa", english: "To write" },
                    { finnish: "Syödä", english: "To eat" },
                    { finnish: "Juoda", english: "To drink" },
                    { finnish: "Nukkua", english: "To sleep" },
                    { finnish: "Herätä", english: "To wake up" },
                    { finnish: "Kävellä", english: "To walk" },
                    { finnish: "Juosta", english: "To run" },
                    { finnish: "Istua", english: "To sit" },
                    { finnish: "Seisoa", english: "To stand" },
                    { finnish: "Antaa", english: "To give" },
                    { finnish: "Ottaa", english: "To take" },
                    { finnish: "Ostaa", english: "To buy" },
                    { finnish: "Myydä", english: "To sell" },
                    { finnish: "Kysyä", english: "To ask" },
                    { finnish: "Vastata", english: "To answer" },
                    { finnish: "Ajatella", english: "To think" },
                    { finnish: "Tuntea", english: "To feel/know" },
                    { finnish: "Rakastaa", english: "To love" },
                    { finnish: "Pitää", english: "To like" },
                    { finnish: "Auttaa", english: "To help" },
                    { finnish: "Työskennellä", english: "To work" },
                    { finnish: "Opiskella", english: "To study" },
                    { finnish: "Oppia", english: "To learn" },
                    { finnish: "Opettaa", english: "To teach" },
                    { finnish: "Matkustaa", english: "To travel" },
                    { finnish: "Asua", english: "To live" },
                    { finnish: "Koti", english: "Home" },
                    { finnish: "Talo", english: "House" },
                    { finnish: "Huone", english: "Room" },
                    { finnish: "Koulu", english: "School" },
                    { finnish: "Työ", english: "Work" },
                    { finnish: "Auto", english: "Car" },
                    { finnish: "Puhelin", english: "Phone" },
                    { finnish: "Tietokone", english: "Computer" },
                    { finnish: "Kirja", english: "Book" },
                    { finnish: "Lehti", english: "Magazine" },
                    { finnish: "Sanomalehti", english: "Newspaper" },
                    { finnish: "Televisio", english: "Television" },
                    { finnish: "Radio", english: "Radio" },
                    { finnish: "Musiikki", english: "Music" },
                    { finnish: "Elokuva", english: "Movie" },
                    { finnish: "Peli", english: "Game" },
                    { finnish: "Urheilu", english: "Sports" },
                    { finnish: "Jalkapallo", english: "Football" },
                    { finnish: "Jääkiekko", english: "Ice hockey" },
                    { finnish: "Uida", english: "To swim" },
                    { finnish: "Lentää", english: "To fly" },
                    { finnish: "Lentokone", english: "Airplane" },
                    { finnish: "Juna", english: "Train" },
                    { finnish: "Bussi", english: "Bus" },
                    { finnish: "Pyörä", english: "Bicycle" },
                    { finnish: "Katu", english: "Street" },
                    { finnish: "Tie", english: "Road" },
                    { finnish: "Kaupunki", english: "City" },
                    { finnish: "Maa", english: "Country" },
                    { finnish: "Suomi", english: "Finland" },
                    { finnish: "Kieli", english: "Language" },
                    { finnish: "Suomen kieli", english: "Finnish language" },
                    { finnish: "Englannin kieli", english: "English language" },
                    { finnish: "Sana", english: "Word" },
                    { finnish: "Lause", english: "Sentence" },
                    { finnish: "Kysymys", english: "Question" },
                    { finnish: "Vastaus", english: "Answer" },
                    { finnish: "Kyllä", english: "Yes" },
                    { finnish: "Ei", english: "No" },
                    { finnish: "Ehkä", english: "Maybe" },
                    { finnish: "Hyvä", english: "Good" },
                    { finnish: "Paha", english: "Bad" },
                    { finnish: "Iso", english: "Big" },
                    { finnish: "Pieni", english: "Small" },
                    { finnish: "Uusi", english: "New" },
                    { finnish: "Vanha", english: "Old" },
                    { finnish: "Kaunis", english: "Beautiful" },
                    { finnish: "Ruma", english: "Ugly" },
                    { finnish: "Helppo", english: "Easy" },
                    { finnish: "Vaikea", english: "Difficult" },
                    { finnish: "Kuuma", english: "Hot" },
                    { finnish: "Kylmä", english: "Cold" },
                    { finnish: "Pitkä", english: "Long" },
                    { finnish: "Lyhyt", english: "Short" }
                ],
                'greetings': [
                    { finnish: "Hei", english: "Hello" },
                    { finnish: "Moi", english: "Hi" },
                    { finnish: "Terve", english: "Hello" },
                    { finnish: "Hyvää päivää", english: "Good day" },
                    { finnish: "Hyvää huomenta", english: "Good morning" },
                    { finnish: "Hyvää iltaa", english: "Good evening" },
                    { finnish: "Näkemiin", english: "Goodbye" },
                    { finnish: "Hei hei", english: "Bye bye" },
                    { finnish: "Kiitos", english: "Thank you" },
                    { finnish: "Ole hyvä", english: "You're welcome" },
                    { finnish: "Anteeksi", english: "Sorry" },
                    { finnish: "Mitä kuuluu?", english: "How are you?" },
                    { finnish: "Hauska tavata", english: "Nice to meet you" },
                    { finnish: "Tervetuloa", english: "Welcome" },
                    { finnish: "Nähdään", english: "See you" },
                    { finnish: "Hyvää yötä", english: "Good night" },
                    { finnish: "Kiitos paljon", english: "Thank you very much" },
                    { finnish: "Ei kestä", english: "No problem" },
                    { finnish: "Anteeksi paljon", english: "I'm very sorry" },
                    { finnish: "Hyvää viikonloppua", english: "Have a good weekend" },
                    { finnish: "Hyvää matkaa", english: "Have a good trip" },
                    { finnish: "Onnea", english: "Good luck" },
                    { finnish: "Hyvää joulua", english: "Merry Christmas" },
                    { finnish: "Hyvää uutta vuotta", english: "Happy New Year" },
                    { finnish: "Hyvää pääsiäistä", english: "Happy Easter" },
                    { finnish: "Hyvää syntymäpäivää", english: "Happy Birthday" },
                    { finnish: "Onneksi olkoon", english: "Congratulations" },
                    { finnish: "Kippis", english: "Cheers" },
                    { finnish: "Terveydeksi", english: "Bless you" },
                    { finnish: "Voi hyvin", english: "Take care" },
                    { finnish: "Mukavaa päivää", english: "Have a nice day" },
                    { finnish: "Mitä sinulle kuuluu?", english: "How are you doing?" },
                    { finnish: "Minulle kuuluu hyvää", english: "I'm doing well" },
                    { finnish: "Entä sinulle?", english: "And you?" },
                    { finnish: "Hauska tutustua", english: "Nice to get to know you" },
                    { finnish: "Nähdään pian", english: "See you soon" },
                    { finnish: "Nähdään huomenna", english: "See you tomorrow" },
                    { finnish: "Hyvää jatkoa", english: "All the best" },
                    { finnish: "Mukavaa illan jatkoa", english: "Have a nice evening" },
                    { finnish: "Oikein hyvää", english: "Very good" },
                    { finnish: "Kiva nähdä sinua", english: "Nice to see you" },
                    { finnish: "Miten menee?", english: "How's it going?" },
                    { finnish: "Hyvin menee", english: "It's going well" },
                    { finnish: "Hyvää ruokahalua", english: "Enjoy your meal" },
                    { finnish: "Paljon kiitoksia", english: "Many thanks" },
                    { finnish: "Tervetuloa takaisin", english: "Welcome back" },
                    { finnish: "Hyvää kesää", english: "Have a good summer" },
                    { finnish: "Hyvää talvea", english: "Have a good winter" }
                ],
                'food': [
                    { finnish: "Ruoka", english: "Food" },
                    { finnish: "Juoma", english: "Drink" },
                    { finnish: "Kahvi", english: "Coffee" },
                    { finnish: "Tee", english: "Tea" },
                    { finnish: "Vesi", english: "Water" },
                    { finnish: "Maito", english: "Milk" },
                    { finnish: "Leipä", english: "Bread" },
                    { finnish: "Liha", english: "Meat" },
                    { finnish: "Kala", english: "Fish" },
                    { finnish: "Peruna", english: "Potato" },
                    { finnish: "Omena", english: "Apple" },
                    { finnish: "Banaani", english: "Banana" },
                    { finnish: "Appelsiini", english: "Orange" },
                    { finnish: "Mansikka", english: "Strawberry" },
                    { finnish: "Jäätelö", english: "Ice cream" },
                    { finnish: "Suklaa", english: "Chocolate" },
                    { finnish: "Juusto", english: "Cheese" },
                    { finnish: "Kana", english: "Chicken" },
                    { finnish: "Riisi", english: "Rice" },
                    { finnish: "Pasta", english: "Pasta" },
                    { finnish: "Keitto", english: "Soup" },
                    { finnish: "Salaatti", english: "Salad" },
                    { finnish: "Muna", english: "Egg" },
                    { finnish: "Tomaatti", english: "Tomato" },
                    { finnish: "Kurkku", english: "Cucumber" },
                    { finnish: "Porkkana", english: "Carrot" },
                    { finnish: "Sipuli", english: "Onion" },
                    { finnish: "Valkosipuli", english: "Garlic" },
                    { finnish: "Pippuri", english: "Pepper" },
                    { finnish: "Suola", english: "Salt" },
                    { finnish: "Sokeri", english: "Sugar" },
                    { finnish: "Hunaja", english: "Honey" },
                    { finnish: "Olut", english: "Beer" },
                    { finnish: "Viini", english: "Wine" },
                    { finnish: "Mehu", english: "Juice" },
                    { finnish: "Limonadi", english: "Soda" },
                    { finnish: "Pulla", english: "Sweet bun" },
                    { finnish: "Kakku", english: "Cake" },
                    { finnish: "Piirakka", english: "Pie" },
                    { finnish: "Keksi", english: "Cookie" },
                    { finnish: "Mustikka", english: "Blueberry" },
                    { finnish: "Vadelma", english: "Raspberry" },
                    { finnish: "Päärynä", english: "Pear" },
                    { finnish: "Ananas", english: "Pineapple" },
                    { finnish: "Sitruuna", english: "Lemon" },
                    { finnish: "Voi", english: "Butter" },
                    { finnish: "Jogurtti", english: "Yogurt" },
                    { finnish: "Kerma", english: "Cream" }
                ],
                'colors': [
                    { finnish: "Punainen", english: "Red" },
                    { finnish: "Sininen", english: "Blue" },
                    { finnish: "Keltainen", english: "Yellow" },
                    { finnish: "Vihreä", english: "Green" },
                    { finnish: "Musta", english: "Black" },
                    { finnish: "Valkoinen", english: "White" },
                    { finnish: "Harmaa", english: "Gray" },
                    { finnish: "Ruskea", english: "Brown" },
                    { finnish: "Oranssi", english: "Orange" },
                    { finnish: "Violetti", english: "Purple" },
                    { finnish: "Pinkki", english: "Pink" },
                    { finnish: "Turkoosi", english: "Turquoise" },
                    { finnish: "Kulta", english: "Gold" },
                    { finnish: "Hopea", english: "Silver" },
                    { finnish: "Beige", english: "Beige" },
                    { finnish: "Tumma", english: "Dark" }
                ],
                'family': [
                    { finnish: "Perhe", english: "Family" },
                    { finnish: "Äiti", english: "Mother" },
                    { finnish: "Isä", english: "Father" },
                    { finnish: "Veli", english: "Brother" },
                    { finnish: "Sisko", english: "Sister" },
                    { finnish: "Lapsi", english: "Child" },
                    { finnish: "Poika", english: "Son/Boy" },
                    { finnish: "Tytär", english: "Daughter" },
                    { finnish: "Vauva", english: "Baby" },
                    { finnish: "Isoäiti", english: "Grandmother" },
                    { finnish: "Isoisä", english: "Grandfather" },
                    { finnish: "Täti", english: "Aunt" },
                    { finnish: "Setä", english: "Uncle" },
                    { finnish: "Serkku", english: "Cousin" },
                    { finnish: "Puoliso", english: "Spouse" },
                    { finnish: "Ystävä", english: "Friend" }
                ],
                'numbers': [
                    { finnish: "Yksi", english: "One" },
                    { finnish: "Kaksi", english: "Two" },
                    { finnish: "Kolme", english: "Three" },
                    { finnish: "Neljä", english: "Four" },
                    { finnish: "Viisi", english: "Five" },
                    { finnish: "Kuusi", english: "Six" },
                    { finnish: "Seitsemän", english: "Seven" },
                    { finnish: "Kahdeksan", english: "Eight" },
                    { finnish: "Yhdeksän", english: "Nine" },
                    { finnish: "Kymmenen", english: "Ten" },
                    { finnish: "Yksitoista", english: "Eleven" },
                    { finnish: "Kaksitoista", english: "Twelve" },
                    { finnish: "Kolmetoista", english: "Thirteen" },
                    { finnish: "Neljätoista", english: "Fourteen" },
                    { finnish: "Viisitoista", english: "Fifteen" },
                    { finnish: "Kuusitoista", english: "Sixteen" },
                    { finnish: "Seitsemäntoista", english: "Seventeen" },
                    { finnish: "Kahdeksantoista", english: "Eighteen" },
                    { finnish: "Yhdeksäntoista", english: "Nineteen" },
                    { finnish: "Kaksikymmentä", english: "Twenty" },
                    { finnish: "Kolmekymmentä", english: "Thirty" },
                    { finnish: "Neljäkymmentä", english: "Forty" },
                    { finnish: "Viisikymmentä", english: "Fifty" },
                    { finnish: "Kuusikymmentä", english: "Sixty" },
                    { finnish: "Seitsemänkymmentä", english: "Seventy" },
                    { finnish: "Kahdeksankymmentä", english: "Eighty" },
                    { finnish: "Yhdeksänkymmentä", english: "Ninety" },
                    { finnish: "Sata", english: "Hundred" },
                    { finnish: "Tuhat", english: "Thousand" },
                    { finnish: "Miljoona", english: "Million" },
                    { finnish: "Miljardi", english: "Billion" },
                    { finnish: "Nolla", english: "Zero" },
                    { finnish: "Puoli", english: "Half" },
                    { finnish: "Neljännes", english: "Quarter" },
                    { finnish: "Ensimmäinen", english: "First" },
                    { finnish: "Toinen", english: "Second" },
                    { finnish: "Kolmas", english: "Third" },
                    { finnish: "Neljäs", english: "Fourth" },
                    { finnish: "Viides", english: "Fifth" },
                    { finnish: "Kuudes", english: "Sixth" },
                    { finnish: "Seitsemäs", english: "Seventh" },
                    { finnish: "Kahdeksas", english: "Eighth" },
                    { finnish: "Yhdeksäs", english: "Ninth" },
                    { finnish: "Kymmenes", english: "Tenth" },
                    { finnish: "Viimeinen", english: "Last" },
                    { finnish: "Monta", english: "Many" },
                    { finnish: "Vähän", english: "Few" },
                    { finnish: "Paljon", english: "A lot" }
                ],
                'time': [
                    { finnish: "Aika", english: "Time" },
                    { finnish: "Kello", english: "Clock" },
                    { finnish: "Tunti", english: "Hour" },
                    { finnish: "Minuutti", english: "Minute" },
                    { finnish: "Sekunti", english: "Second" },
                    { finnish: "Päivä", english: "Day" },
                    { finnish: "Viikko", english: "Week" },
                    { finnish: "Kuukausi", english: "Month" },
                    { finnish: "Vuosi", english: "Year" },
                    { finnish: "Tänään", english: "Today" },
                    { finnish: "Huomenna", english: "Tomorrow" },
                    { finnish: "Eilen", english: "Yesterday" },
                    { finnish: "Aamu", english: "Morning" },
                    { finnish: "Iltapäivä", english: "Afternoon" },
                    { finnish: "Ilta", english: "Evening" },
                    { finnish: "Yö", english: "Night" },
                    { finnish: "Maanantai", english: "Monday" },
                    { finnish: "Tiistai", english: "Tuesday" },
                    { finnish: "Keskiviikko", english: "Wednesday" },
                    { finnish: "Torstai", english: "Thursday" },
                    { finnish: "Perjantai", english: "Friday" },
                    { finnish: "Lauantai", english: "Saturday" },
                    { finnish: "Sunnuntai", english: "Sunday" },
                    { finnish: "Viikonloppu", english: "Weekend" },
                    { finnish: "Tammikuu", english: "January" },
                    { finnish: "Helmikuu", english: "February" },
                    { finnish: "Maaliskuu", english: "March" },
                    { finnish: "Huhtikuu", english: "April" },
                    { finnish: "Toukokuu", english: "May" },
                    { finnish: "Kesäkuu", english: "June" },
                    { finnish: "Heinäkuu", english: "July" },
                    { finnish: "Elokuu", english: "August" },
                    { finnish: "Syyskuu", english: "September" },
                    { finnish: "Lokakuu", english: "October" },
                    { finnish: "Marraskuu", english: "November" },
                    { finnish: "Joulukuu", english: "December" },
                    { finnish: "Kevät", english: "Spring" },
                    { finnish: "Kesä", english: "Summer" },
                    { finnish: "Syksy", english: "Autumn" },
                    { finnish: "Talvi", english: "Winter" },
                    { finnish: "Puolipäivä", english: "Noon" },
                    { finnish: "Keskiyö", english: "Midnight" },
                    { finnish: "Aikaisin", english: "Early" },
                    { finnish: "Myöhään", english: "Late" },
                    { finnish: "Nyt", english: "Now" },
                    { finnish: "Myöhemmin", english: "Later" },
                    { finnish: "Pian", english: "Soon" },
                    { finnish: "Ennen", english: "Before" }
                ]
            };
            
            return fallbackSets[category] || fallbackSets['greetings'];
        }
        
        // Cache for vocabulary
        const vocabularyCache = {};

        // Game variables
        let cards = [];
        let flippedCards = [];
        let matchedPairs = 0;
        let totalPairs = 0;
        let moves = 0;
        let gameStarted = false;
        let gameTimer;
        let seconds = 0;
        let currentDifficulty = 'easy';
        let currentCategory = 'greetings';
        let canFlip = true;
        let currentCols = 4; // Track current number of columns

        // DOM elements
        const memoryBoard = document.getElementById('memory-board');
        const movesCount = document.getElementById('moves-count');
        const pairsCount = document.getElementById('pairs-count');
        const timeCount = document.getElementById('time-count');
        const startButton = document.getElementById('start-button');
        const resetButton = document.getElementById('reset-button');
        const gameMessage = document.getElementById('game-message');
        const difficultySelect = document.getElementById('difficulty-select');
        const categorySelect = document.getElementById('category-select');

        // Event listeners
        startButton.addEventListener('click', startGame);
        resetButton.addEventListener('click', resetGame);
        
        // Add resize event listener with debounce to avoid too many calls
        let resizeTimeout;
        window.addEventListener('resize', function() {
            // Clear previous timeout
            clearTimeout(resizeTimeout);
            
            // Set new timeout to avoid multiple calls during resize
            resizeTimeout = setTimeout(function() {
                if (gameStarted) {
                    // Handle resize during gameplay
                    handleGameResize();
                }
            }, 250); // Wait 250ms after resize ends
        });
        
        // Function to handle resize during gameplay
        function handleGameResize() {
            // Store current state
            const oldCols = currentCols;
            
            // Update grid layout
            updateGridLayout();
            
            // If columns changed, we need to adjust the cards
            if (oldCols !== currentCols) {
                console.log(`Columns changed from ${oldCols} to ${currentCols}`);
            }
            
            // Force redraw of all cards to ensure proper layout
            const currentCards = [...memoryBoard.children];
            currentCards.forEach(card => {
                // Apply inline styles to override any CSS
                card.style.width = "100%";
                card.style.boxSizing = "border-box";
                
                // Ensure card front and back are also properly sized
                const front = card.querySelector('.memory-card-front');
                const back = card.querySelector('.memory-card-back');
                if (front) front.style.width = "100%";
                if (back) back.style.width = "100%";
            });
        }
        
        // Function to update grid layout based on screen size
        function updateGridLayout() {
            // Get current screen width
            const screenWidth = window.innerWidth;
            
            // If currentCols is not set yet or we're recalculating based on screen size
            if (arguments.length === 0) {
                // Determine columns based on screen width
                if (screenWidth >= 1200) {
                    currentCols = 8;
                } else if (screenWidth >= 992) {
                    currentCols = 6;
                } else if (screenWidth >= 768) {
                    currentCols = 4;
                } else if (screenWidth >= 480) {
                    currentCols = 4;
                } else {
                    currentCols = 4; // Changed to 4 columns for mobile screens
                }
            }
            
            // Update grid with specific CSS for different column counts
            // Use a more direct approach with !important to override any conflicting styles
            memoryBoard.style.cssText = `
                display: grid !important;
                grid-template-columns: repeat(${currentCols}, minmax(0, 1fr)) !important;
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            `;
            
            // Apply specific styles based on column count
            if (currentCols === 8) {
                memoryBoard.style.gap = "10px";
            } else if (currentCols === 6) {
                memoryBoard.style.gap = "8px";
            } else if (currentCols === 4) {
                memoryBoard.style.gap = "7px";
            } else if (currentCols === 3) {
                memoryBoard.style.gap = "6px";
            } else {
                memoryBoard.style.gap = "5px";
            }
            
            // Log for debugging
            console.log(`Grid updated: ${screenWidth}px, Columns: ${currentCols}`);
        }

        // Set up difficulty select
        difficultySelect.addEventListener('change', () => {
            currentDifficulty = difficultySelect.value;
            
            if (gameStarted) {
                resetGame();
                startGame();
            }
        });

        // Set up category select
        categorySelect.addEventListener('change', () => {
            currentCategory = categorySelect.value;
            
            // Show or hide learning progress based on category
            const learningProgress = document.getElementById('learning-progress');
            if (currentCategory === 'common_words') {
                learningProgress.style.display = 'block';
                updateLearningProgress();
            } else {
                learningProgress.style.display = 'none';
            }
            
            if (gameStarted) {
                resetGame();
                startGame();
            }
        });
        
        // Function to update learning progress display
        async function updateLearningProgress() {
            try {
                const response = await fetch('../finnish_vocabulary.json');
                if (!response.ok) {
                    throw new Error('Failed to fetch vocabulary data');
                }
                
                const allWords = await response.json();
                let wordsMastered = 0;
                let wordsInProgress = 0;
                
                for (const word of allWords) {
                    const matchCount = getWordMatchCount(word.finnish, word.english);
                    if (matchCount >= 10) {
                        wordsMastered++;
                    } else if (matchCount > 0) {
                        wordsInProgress++;
                    }
                }
                
                const wordsRemaining = allWords.length - wordsMastered - wordsInProgress;
                
                document.getElementById('words-mastered').textContent = wordsMastered;
                document.getElementById('words-in-progress').textContent = wordsInProgress;
                document.getElementById('words-remaining').textContent = wordsRemaining;
            } catch (error) {
                console.error('Error updating learning progress:', error);
            }
        }

        // Start the game
        async function startGame() {
            resetGame();
            gameStarted = true;
            startButton.disabled = true;
            resetButton.disabled = false;
            
            // Show loading state
            memoryBoard.innerHTML = '<div style="text-align: center; width: 100%; padding: 20px;">Loading vocabulary...</div>';
            
            // Set up board based on difficulty
            let gridSize;
            let totalCards;
            
            switch (currentDifficulty) {
                case 'easy':
                    gridSize = { cols: 8, rows: 3 };
                    totalCards = 24; // 12 pairs
                    break;
                case 'medium':
                    gridSize = { cols: 8, rows: 6 };
                    totalCards = 48; // 24 pairs
                    break;
                case 'hard':
                    gridSize = { cols: 8, rows: 9 };
                    totalCards = 72; // 36 pairs
                    break;
                case 'best':
                    gridSize = { cols: 8, rows: 12 };
                    totalCards = 96; // 48 pairs
                    break;
                default:
                    gridSize = { cols: 8, rows: 3 };
                    totalCards = 24;
            }
            
            // Determine optimal grid layout based on screen width
            let cols = 4; // Default to 4 columns as a safe option
            
            // Adjust columns based on screen width
            const screenWidth = window.innerWidth;
            
            if (screenWidth >= 1200) {
                // Extra large screens - 8 columns
                cols = 8;
            } else if (screenWidth >= 992) {
                // Large screens - 6 columns
                cols = 6;
            } else if (screenWidth >= 768) {
                // Medium screens - 4 columns
                cols = 4;
            } else if (screenWidth >= 480) {
                // Small screens - 4 columns
                cols = 4;
            } else {
                // Very small screens - 4 columns
                cols = 4;
            }
            
            // Override based on difficulty if needed
            if (currentDifficulty === 'easy' && screenWidth < 768) {
                // For easy difficulty on small screens, ensure we don't have too few columns
                cols = Math.max(cols, 3);
            }
            
            // Use the updateGridLayout function to set the grid columns
            // This ensures consistent behavior between initial load and resize
            
            // First, store the calculated columns
            currentCols = cols;
            
            // Then update the grid layout
            updateGridLayout();
            
            // Log for debugging
            console.log(`Initial screen width: ${screenWidth}px, Columns: ${cols}`);
            
            // Get vocabulary for current category (from cache or fetch)
            let vocabulary;
            if (vocabularyCache[currentCategory]) {
                vocabulary = vocabularyCache[currentCategory];
            } else {
                vocabulary = await fetchVocabulary(currentCategory);
                vocabularyCache[currentCategory] = vocabulary;
            }
            
            // Calculate how many pairs we need
            totalPairs = totalCards / 2;
            
            // Make sure we have enough vocabulary items
            if (vocabulary.length < totalPairs) {
                // If not enough words, use fallback
                vocabulary = getFallbackVocabulary(currentCategory);
                console.warn(`Not enough vocabulary items for ${currentCategory}. Using fallback.`);
            }
            
            // Select random vocabulary pairs
            const selectedVocab = vocabulary
                .sort(() => 0.5 - Math.random())
                .slice(0, totalPairs);
            
            // Create card pairs
            cards = [];
            selectedVocab.forEach((item, index) => {
                // Finnish card
                cards.push({
                    id: index * 2,
                    content: item.finnish,
                    type: 'finnish',
                    matched: false,
                    pairId: index * 2 + 1
                });
                
                // English card
                cards.push({
                    id: index * 2 + 1,
                    content: item.english,
                    type: 'english',
                    matched: false,
                    pairId: index * 2
                });
            });
            
            // Shuffle cards
            cards = cards.sort(() => 0.5 - Math.random());
            
            // Render cards
            renderCards();
            
            // Start timer
            startTimer();
        }

        // Reset the game
        function resetGame() {
            clearInterval(gameTimer);
            gameStarted = false;
            flippedCards = [];
            matchedPairs = 0;
            totalPairs = 0;
            moves = 0;
            seconds = 0;
            canFlip = true;
            
            // Update UI
            movesCount.textContent = moves;
            pairsCount.textContent = `${matchedPairs}/${totalPairs}`;
            timeCount.textContent = '00:00';
            gameMessage.textContent = '';
            gameMessage.classList.remove('success');
            gameMessage.style.display = 'none';
            
            // Clear board
            memoryBoard.innerHTML = '';
            
            // Reset buttons
            startButton.disabled = false;
            resetButton.disabled = true;
        }

        // Render cards on the board
        function renderCards() {
            memoryBoard.innerHTML = '';
            
            // Make sure grid layout is set correctly before adding cards
            updateGridLayout();
            
            // Create a document fragment for better performance
            const fragment = document.createDocumentFragment();
            
            cards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = 'memory-card';
                cardElement.dataset.id = card.id;
                cardElement.style.width = "100%";
                cardElement.style.boxSizing = "border-box";
                
                if (card.matched) {
                    cardElement.classList.add('matched');
                    cardElement.classList.add('flipped');
                }
                
                // Format content for better display
                let displayContent = card.content;
                
                // Add language indicator for better context
                if (card.type === 'finnish') {
                    cardElement.classList.add('finnish-card');
                } else {
                    cardElement.classList.add('english-card');
                }
                
                // Create front and back elements separately for better control
                const frontElement = document.createElement('div');
                frontElement.className = 'memory-card-front';
                frontElement.innerHTML = '<i class="fas fa-brain" style="font-size: 1.2rem;"></i>';
                
                const backElement = document.createElement('div');
                backElement.className = 'memory-card-back';
                backElement.textContent = displayContent;
                
                // Append elements to card
                cardElement.appendChild(frontElement);
                cardElement.appendChild(backElement);
                
                cardElement.addEventListener('click', () => flipCard(card, cardElement));
                fragment.appendChild(cardElement);
            });
            
            // Append all cards at once
            memoryBoard.appendChild(fragment);
            
            // Update pairs count
            pairsCount.textContent = `${matchedPairs}/${totalPairs}`;
        }

        // Flip a card
        function flipCard(card, cardElement) {
            // Don't allow flipping if game not started, card already matched, or two cards already flipped
            if (!gameStarted || card.matched || flippedCards.length >= 2 || !canFlip) {
                return;
            }
            
            // Don't allow flipping the same card twice
            if (flippedCards.length === 1 && flippedCards[0].id === card.id) {
                return;
            }
            
            // Flip the card
            cardElement.classList.add('flipped');
            flippedCards.push({ ...card, element: cardElement });
            
            // If we have two flipped cards, check for a match
            if (flippedCards.length === 2) {
                moves++;
                movesCount.textContent = moves;
                canFlip = false;
                
                setTimeout(() => {
                    checkForMatch();
                    canFlip = true;
                }, 500);
            }
        }

        // Check if the two flipped cards match
        function checkForMatch() {
            const [card1, card2] = flippedCards;
            
            // Check if the cards form a pair
            if (card1.pairId === card2.id && card2.pairId === card1.id) {
                // Match found - keep cards flipped
                card1.element.classList.add('matched');
                card2.element.classList.add('matched');
                
                // Update cards array
                cards = cards.map(card => {
                    if (card.id === card1.id || card.id === card2.id) {
                        return { ...card, matched: true };
                    }
                    return card;
                });
                
                // Increment match count in localStorage for common_words category
                if (currentCategory === 'common_words') {
                    // Find the original vocabulary items
                    const finnishCard = card1.type === 'finnish' ? card1 : card2;
                    const englishCard = card1.type === 'english' ? card1 : card2;
                    
                    // Get the content of the cards
                    const finnishContent = finnishCard.content;
                    const englishContent = englishCard.content;
                    
                    // Increment the match count
                    incrementWordMatchCount(finnishContent, englishContent);
                    
                    // Get the updated count
                    const newCount = getWordMatchCount(finnishContent, englishContent);
                    
                    // Add a visual indicator of progress if count < 10
                    if (newCount < 10) {
                        const progressIndicator = document.createElement('div');
                        progressIndicator.className = 'match-progress';
                        progressIndicator.textContent = `${newCount}/10`;
                        progressIndicator.style.position = 'absolute';
                        progressIndicator.style.bottom = '2px';
                        progressIndicator.style.right = '3px';
                        progressIndicator.style.fontSize = '0.6rem';
                        progressIndicator.style.color = 'rgba(0, 102, 204, 0.8)';
                        progressIndicator.style.fontWeight = 'bold';
                        
                        finnishCard.element.appendChild(progressIndicator.cloneNode(true));
                        englishCard.element.appendChild(progressIndicator);
                    }
                }
                
                matchedPairs++;
                pairsCount.textContent = `${matchedPairs}/${totalPairs}`;
                
                // Check if game is complete
                if (matchedPairs === totalPairs) {
                    endGame();
                }
            } else {
                // No match, flip cards back
                setTimeout(() => {
                    card1.element.classList.remove('flipped');
                    card2.element.classList.remove('flipped');
                }, 500);
            }
            
            // Clear flipped cards
            flippedCards = [];
        }

        // Start the timer
        function startTimer() {
            clearInterval(gameTimer);
            seconds = 0;
            gameTimer = setInterval(() => {
                seconds++;
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                timeCount.textContent = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // End the game
        function endGame() {
            clearInterval(gameTimer);
            gameStarted = false;
            
            // Show success message
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
            
            gameMessage.textContent = `Complete! Time: ${timeString} | Moves: ${moves}`;
            gameMessage.classList.add('success');
            gameMessage.style.display = 'block';
            
            // Update learning progress if using common words
            if (currentCategory === 'common_words') {
                updateLearningProgress();
            }
            
            // Reset buttons
            startButton.disabled = false;
            resetButton.disabled = true;
        }

        // Mobile menu functionality
        // Function to adjust grid based on window size
        function adjustGridLayout() {
            if (!gameStarted) return;
            
            let cols;
            
            // For larger screens, always use 8 columns
            // For smaller screens, reduce columns based on screen size
            if (window.innerWidth >= 992) {
                // Large screens - always 8 columns
                cols = 8;
            } else if (window.innerWidth >= 768) {
                // Medium screens - 6 columns
                cols = 6;
            } else if (window.innerWidth >= 480) {
                // Small screens - 4 columns
                cols = 4;
            } else {
                // Very small screens - 3 columns
                cols = 3;
            }
            
            // Ensure minimum columns
            if (window.innerWidth < 360) {
                cols = 4; // Changed to 4 columns for very small screens
            }
            
            memoryBoard.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
        }
        
        // Listen for window resize events
        window.addEventListener('resize', adjustGridLayout);
        
        document.addEventListener("DOMContentLoaded", function() {
            // Check if common_words is selected and show learning progress
            if (categorySelect.value === 'common_words') {
                document.getElementById('learning-progress').style.display = 'block';
                updateLearningProgress();
            }
            
            // Mobile menu toggle
            const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            const navLinks = document.getElementById("nav-links");

            if (mobileMenuToggle && navLinks) {
                mobileMenuToggle.addEventListener("click", function() {
                    navLinks.classList.toggle("show");
                    this.classList.toggle("active");
                });
            }

            // Check for saved dark mode preference
            const darkMode = localStorage.getItem("darkMode");
            
            // Apply dark mode if previously enabled
            if (darkMode === "enabled") {
                document.body.classList.add("dark-mode");
                
                // Update icon to sun
                const darkModeBtn = document.getElementById("memory-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Update mobile icon too
                const mobileDarkModeBtn = document.getElementById("memory-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            }
            
            // Set up dark mode toggle
            const darkModeToggle = document.getElementById("memory-toggle-dark");
            if (darkModeToggle) {
                darkModeToggle.addEventListener("click", toggleDarkMode);
            }
            
            // Set up mobile dark mode toggle
            const mobileDarkModeToggle = document.getElementById("memory-toggle-dark-mobile");
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.addEventListener("click", toggleDarkMode);
            }

            // Handle dropdown menus in mobile view
            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach((dropdown) => {
                const dropbtn = dropdown.querySelector(".dropbtn");
                if (dropbtn) {
                    dropbtn.addEventListener("click", function (e) {
                        // Only in mobile view
                        if (window.innerWidth <= 767) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Close other active dropdowns
                            dropdowns.forEach((otherDropdown) => {
                                if (
                                    otherDropdown !== dropdown &&
                                    otherDropdown.classList.contains("active")
                                ) {
                                    otherDropdown.classList.remove("active");
                                }
                            });

                            // Toggle current dropdown
                            dropdown.classList.toggle("active");
                        }
                    });
                }
            });
        });
        
        // Toggle dark mode function
        function toggleDarkMode() {
            document.body.classList.toggle("dark-mode");
            
            // Store preference in localStorage
            if (document.body.classList.contains("dark-mode")) {
                localStorage.setItem("darkMode", "enabled");
                
                // Change icon to sun
                const darkModeBtn = document.getElementById("memory-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                
                // Change mobile icon to sun
                const mobileDarkModeBtn = document.getElementById("memory-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            } else {
                localStorage.setItem("darkMode", "disabled");
                
                // Change icon back to moon
                const darkModeBtn = document.getElementById("memory-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-sun");
                        icon.classList.add("fa-moon");
                    }
                }
                
                // Change mobile icon back to moon
                const mobileDarkModeBtn = document.getElementById("memory-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-sun");
                        mobileIcon.classList.add("fa-moon");
                    }
                }
            }
        }
    </script>
</body>
</html>