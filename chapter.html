<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter - <PERSON><PERSON><PERSON><PERSON></title>
    <!-- Meta tag for Daily Life chapter file patterns -->
    <meta name="chapter-files-daily-life" content="chapter01_morning_greetings.md,chapter02_family_friends.md,chapter03_home_living.md,chapter04_daily_shopping.md,chapter05_transport_vehicles.md,chapter06_health_wellness.md,chapter07_weather_seasons.md,chapter08_leisure_hobbies.md,chapter09_nutrition_meals.md,chapter10_special_diets.md,chapter11_music_movies.md,chapter12_fashion_clothing.md,chapter13_friendly_small_talk.md,chapter14_holidays_traditions.md,chapter15_education_study.md,chapter16_workplace_career.md,chapter17_money_banking.md,chapter18_phones_communication.md,chapter19_basic_tech_use.md,chapter20_netiquette_social_media.md,chapter21_pets_animals.md,chapter22_environment_sustainability.md,chapter23_recycling_waste.md,chapter24_home_safety.md,chapter25_first_aid_basics.md,chapter26_local_culture.md,chapter27_offices_services.md,chapter28_online_ordering.md,chapter29_repairs_maintenance.md,chapter30_daily_conversations_review.md">
    
    <!-- Meta tag for Web Development chapter file patterns -->
    <meta name="chapter-files-web-development" content="chapter31_html_basics.md,chapter32_css_styling.md,chapter33_responsive_design.md,chapter34_javascript_basics.md,chapter35_dom_manipulation.md,chapter36_forms_validation.md,chapter37_ajax_api_calls.md,chapter38_databases_sql.md,chapter39_php_basics.md,chapter40_nodejs_basics.md,chapter41_react_basics.md,chapter42_web_security.md,chapter43_testing_quality_assurance.md,chapter44_version_control.md,chapter45_performance_optimization.md,chapter46_ui_design.md,chapter47_user_experience.md,chapter48_website_deployment.md,chapter49_deployment_release.md,chapter50_emerging_web_trends.md">
    
    <!-- Meta tag for Cleaner chapter file patterns -->
    <meta name="chapter-files-cleaner" content="chapter51_cleaning_tools.md,chapter52_cleaning_products.md,chapter53_home_cleaning.md,chapter54_office_cleaning.md,chapter55_hotel_cleaning.md,chapter56_industrial_cleaning.md,chapter57_floor_maintenance.md,chapter58_window_cleaning.md,chapter59_bathroom_cleaning.md,chapter60_daily_reporting.md">
    
    <!-- Meta tag for Kitchen Assistant chapter file patterns -->
    <meta name="chapter-files-kitchen-assistant" content="chapter61_kitchen_basics.md,chapter62_cooking_methods.md,chapter63_kitchen_utensils_equipment.md,chapter64_food_handling_storage.md,chapter65_kitchen_cleanliness_hygiene.md,chapter66_special_diets_allergies.md,chapter67_customer_service_kitchen.md,chapter68_work_safety_kitchen.md,chapter69_orders_inventory_management.md,chapter70_teamwork_communication_kitchen.md">
    
    <!-- Meta tag for Warehouse chapter file patterns -->
    <meta name="chapter-files-warehouse" content="chapter71_receiving_inspection.md,chapter72_storage_techniques.md,chapter73_order_picking.md,chapter74_packing_shipping.md,chapter75_warehouse_safety.md,chapter76_forklift_equipment.md,chapter77_inventory_management.md,chapter78_waste_management.md,chapter79_quality_inspections.md,chapter80_daily_reporting.md">
    
    <!-- Meta tag for all chapter file patterns (for backward compatibility) -->
    <meta name="chapter-files" content="chapter01_morning_greetings.md,chapter02_family_friends.md,chapter03_home_living.md,chapter04_daily_shopping.md,chapter05_transport_vehicles.md,chapter06_health_wellness.md,chapter07_weather_seasons.md,chapter08_leisure_hobbies.md,chapter09_nutrition_meals.md,chapter10_special_diets.md,chapter11_music_movies.md,chapter12_fashion_clothing.md,chapter13_friendly_small_talk.md,chapter14_holidays_traditions.md,chapter15_education_study.md,chapter16_workplace_career.md,chapter17_money_banking.md,chapter18_phones_communication.md,chapter19_basic_tech_use.md,chapter20_netiquette_social_media.md,chapter21_pets_animals.md,chapter22_environment_sustainability.md,chapter23_recycling_waste.md,chapter24_home_safety.md,chapter25_first_aid_basics.md,chapter26_local_culture.md,chapter27_offices_services.md,chapter28_online_ordering.md,chapter29_repairs_maintenance.md,chapter30_daily_conversations_review.md">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
</head>
<body class="chapter-body">
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="compact-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="index.html#daily-life">Daily Life</a>
                        <a href="index.html#web-development">Web Development</a>
                        <a href="index.html#cleaner">Cleaner</a>
                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="compact-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="compact-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>



    <div class="container chapter-layout-full">
        <main class="chapter-main-full">
            <div id="chapter-loader" class="loader"></div>

            <div id="chapter-container">
                <div class="chapter-progress-container">
                    <div class="chapter-progress-bar" id="reading-progress"></div>
                </div>

                <div class="chapter-header-navigation">
                    <button id="prev-chapter-top" class="nav-button" title="Previous Chapter"><i class="fas fa-chevron-left"></i><span class="button-text">&nbsp; Previous</span></button>
                    <div id="table-of-contents-dropdown">
                        <button class="toc-button"><i class="fas fa-list"></i><span class="button-text">&nbsp; Contents</span></button>
                        <div id="table-of-contents" class="toc-dropdown-content">
                            <!-- Table of contents will be generated dynamically -->
                            <div class="toc-placeholder">Loading contents...</div>
                        </div>
                    </div>
                    <button id="next-chapter-top" class="nav-button" title="Next Chapter"><span class="button-text">Next &nbsp;</span><i class="fas fa-chevron-right"></i></button>
                </div>

                <div id="chapter-content" class="chapter-content">
                    <!-- Chapter content will be loaded here -->
                </div>

                <div class="chapter-footer-navigation">
                    <button id="prev-chapter-bottom" class="nav-button" title="Previous Chapter"><i class="fas fa-chevron-left"></i><span class="button-text">&nbsp; Previous</span></button>
                    <a href="index.html" class="back-to-home"><i class="fas fa-home"></i><span class="button-text">&nbsp; Home</span></a>
                    <button id="next-chapter-bottom" class="nav-button" title="Next Chapter"><span class="button-text">Next &nbsp;</span><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        </main>
    </div>

    <!-- Highlight mode notification -->
    <div id="highlight-notification" class="highlight-notification">
        <i class="fas fa-highlighter"></i> Highlight mode enabled. Select text to highlight it.
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Opiskelen Suomea provides Finnish language learning resources specifically designed for professional environments and workplace integration in Finland.</p>
                <div class="footer-social">
                    <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Categories</h3>
                <ul class="footer-links">
                    <li><a href="index.html#daily-life"><i class="fas fa-angle-right"></i> Daily Life</a></li>
                    <li><a href="index.html#web-development"><i class="fas fa-angle-right"></i> Web Development</a></li>
                    <li><a href="index.html#cleaner"><i class="fas fa-angle-right"></i> Cleaner</a></li>
                    <li><a href="index.html#kitchen-assistant"><i class="fas fa-angle-right"></i> Kitchen Assistant</a></li>
                    <li><a href="index.html#warehouse"><i class="fas fa-angle-right"></i> Warehouse</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html"><i class="fas fa-angle-right"></i> Home</a></li>
                    <li><a href="audio.html"><i class="fas fa-angle-right"></i> Audio</a></li>
                    <li><a href="video.html"><i class="fas fa-angle-right"></i> Videos</a></li>
                    <li><a href="index.html#about"><i class="fas fa-angle-right"></i> About</a></li>
                    <li><a href="index.html#structure"><i class="fas fa-angle-right"></i> Learning Structure</a></li>
                </ul>
            </div>

            <div class="footer-section footer-contact">
                <h3>Contact Us</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> +358 40 123 4567</p>
                <p><i class="fas fa-map-marker-alt"></i> Helsinki, Finland</p>
            </div>

            <div class="footer-bottom">
                <p class="copyright">&copy; 2024 Opiskelen Suomea. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="highlight.js"></script>
    <script>
        // Function to handle text selection - defined at the top to ensure it's available
        function handleTextSelection(event) {
            console.log("handleTextSelection called");
            
            if (!document.body.classList.contains('highlight-mode')) {
                console.log("Highlight mode is not enabled, returning");
                return;
            }

            // Check if we're in a chapter page by looking for the chapter-content element
            const chapterContent = document.getElementById("chapter-content");
            if (!chapterContent) {
                console.log("Not in a chapter page, returning");
                return;
            }

            // Only activate highlighting if the selection is within the chapter content
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0) {
                console.log("No selection found, returning");
                return;
            }

            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;

            // Check if the selection is within the chapter content
            if (
                chapterContent.contains(container) ||
                chapterContent.contains(container.parentNode)
            ) {
                console.log("Selection is valid, proceeding with highlighting");
                
                // Use the highlightSelectedText function from highlight.js
                if (typeof highlightSelectedText === 'function') {
                    console.log("Using highlightSelectedText function from highlight.js");
                    highlightSelectedText();
                } else {
                    console.error('highlightSelectedText function not found, using fallback');
                    
                    // Fallback implementation if highlightSelectedText is not available
                    if (selection.toString().length > 0) {
                        // Create a span element to wrap the selected text
                        const range = selection.getRangeAt(0);
                        const selectedText = range.extractContents();
                        const span = document.createElement('span');
                        span.className = 'user-highlight';
                        span.appendChild(selectedText);
                        range.insertNode(span);

                        // Clear the selection
                        selection.removeAllRanges();
                        console.log("Highlighting successful using fallback method");
                    }
                }
            } else {
                console.log("Selection is not within chapter content");
            }
        }

        // Function to remove all highlights
        function removeAllHighlights() {
            const highlights = document.querySelectorAll('.user-highlight');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                while (highlight.firstChild) {
                    parent.insertBefore(highlight.firstChild, highlight);
                }
                parent.removeChild(highlight);
            });
        }
        
        // Function to toggle text highlighting - defined at the top to ensure it's available
        function toggleHighlight() {
            console.log("toggleHighlight called");
            
            // Get all highlight buttons
            const highlightButtons = document.querySelectorAll('[id$="-toggle-highlight"]');
            
            // Check current state based on the button's active class
            const highlightButton = document.getElementById('compact-toggle-highlight');
            const isCurrentlyHighlighted = highlightButton && highlightButton.classList.contains('active-tool');
            
            console.log("Current highlight state:", isCurrentlyHighlighted ? "enabled" : "disabled", 
                        "Button active:", highlightButton && highlightButton.classList.contains('active-tool'));
            
            if (isCurrentlyHighlighted) {
                // DISABLE HIGHLIGHTING
                console.log("Disabling highlight mode");
                
                // 1. Update DOM
                document.body.classList.remove('highlight-mode');
                document.body.classList.remove('highlight-enabled');
                document.body.style.cursor = ''; // Reset cursor
                
                // 2. Update all button appearances
                highlightButtons.forEach(button => {
                    button.classList.remove('active');
                    button.classList.remove('active-tool');
                    console.log("Removed active classes from button:", button.id);
                });
                
                // 3. Remove event listener for text selection
                document.removeEventListener('mouseup', handleTextSelection);
                
                // 4. Remove all highlights if the function exists
                if (typeof removeAllHighlights === 'function') {
                    removeAllHighlights();
                }
                
                // 5. Remove background color from Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = '';
                });
            } else {
                // ENABLE HIGHLIGHTING
                console.log("Enabling highlight mode");
                
                // 1. Update DOM
                document.body.classList.add('highlight-mode');
                document.body.style.cursor = 'text'; // Change cursor to indicate highlight mode
                
                // 2. Update all button appearances
                highlightButtons.forEach(button => {
                    const activeClass = button.classList.contains('compact-nav-button') ? 'active-tool' : 'active';
                    button.classList.add(activeClass);
                    console.log("Added", activeClass, "class to button:", button.id);
                });
                
                // 3. Show notification
                const notification = document.getElementById('highlight-notification');
                if (notification) {
                    console.log("Showing highlight notification");
                    notification.style.display = 'block';
                    setTimeout(() => {
                        notification.style.opacity = '1';
                    }, 10);
                    setTimeout(() => {
                        notification.style.opacity = '0';
                        setTimeout(() => {
                            notification.style.display = 'none';
                        }, 500);
                    }, 3000);
                }
                
                // 4. Add event listener for text selection
                document.addEventListener('mouseup', handleTextSelection);
                
                // 5. Add background color to Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                });
            }
        }

        // Direct implementation without any dependencies
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM Content Loaded for chapter.html");

            // Apply dark mode from localStorage immediately
            if (localStorage.getItem('darkMode') === 'enabled') {
                document.body.classList.add('dark-mode');

                // Update all moon icons to sun icons
                document.querySelectorAll('.fa-moon').forEach(function(icon) {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                });

                // Add active-tool class to dark mode toggle buttons
                const darkModeToggle = document.getElementById('compact-toggle-dark');
                const darkModeToggleMobile = document.getElementById('compact-toggle-dark-mobile');
                if (darkModeToggle) darkModeToggle.classList.add('active-tool');
                if (darkModeToggleMobile) darkModeToggleMobile.classList.add('active-tool');
            }

            // Highlight mode initialization is now handled in a single place at the end of the script

            // Function to handle dark mode toggle
            function handleDarkModeToggle(event) {
                // Prevent default behavior
                event.preventDefault();
                event.stopPropagation();

                console.log("Toggle dark mode clicked");

                // Get dark mode toggle buttons
                const darkModeToggle = document.getElementById('compact-toggle-dark');
                const darkModeToggleMobile = document.getElementById('compact-toggle-dark-mobile');

                // Toggle dark mode class
                if (document.body.classList.contains('dark-mode')) {
                    // Currently in dark mode, switch to light mode
                    document.body.classList.remove('dark-mode');
                    localStorage.setItem('darkMode', 'disabled');

                    // Update all icons
                    document.querySelectorAll('.fa-sun').forEach(function(icon) {
                        icon.classList.remove('fa-sun');
                        icon.classList.add('fa-moon');
                    });

                    // Remove active-tool class from dark mode toggle buttons
                    if (darkModeToggle) darkModeToggle.classList.remove('active-tool');
                    if (darkModeToggleMobile) darkModeToggleMobile.classList.remove('active-tool');

                    console.log("Switched to light mode");
                } else {
                    // Currently in light mode, switch to dark mode
                    document.body.classList.add('dark-mode');
                    localStorage.setItem('darkMode', 'enabled');

                    // Update all icons
                    document.querySelectorAll('.fa-moon').forEach(function(icon) {
                        icon.classList.remove('fa-moon');
                        icon.classList.add('fa-sun');
                    });

                    // Add active-tool class to dark mode toggle buttons
                    if (darkModeToggle) darkModeToggle.classList.add('active-tool');
                    if (darkModeToggleMobile) darkModeToggleMobile.classList.add('active-tool');

                    console.log("Switched to dark mode");
                }

                console.log("Dark mode is now:", document.body.classList.contains('dark-mode') ? 'enabled' : 'disabled');
                console.log("localStorage darkMode is now:", localStorage.getItem('darkMode'));

                return false;
            }

            // Add click event to desktop toggle button
            const darkModeToggle = document.getElementById('compact-toggle-dark');
            if (darkModeToggle) {
                darkModeToggle.addEventListener('click', handleDarkModeToggle, true);
            }

            // Add click event to mobile toggle button
            const darkModeToggleMobile = document.getElementById('compact-toggle-dark-mobile');
            if (darkModeToggleMobile) {
                darkModeToggleMobile.addEventListener('click', handleDarkModeToggle, true);
            }

            // Highlight toggle - use the toggleHighlight function for consistency
            const highlightToggle = document.getElementById('compact-toggle-highlight');
            if (highlightToggle) {
                highlightToggle.addEventListener('click', function() {
                    // Call the toggleHighlight function to ensure consistent behavior
                    toggleHighlight();
                });
            }

            // handleTextSelection function is now defined at the top of the script

            // removeAllHighlights function is now defined at the top of the script
        });
    </script>
    <script src="chapter-new.js"></script>
    <script src="chapter-navigation.js"></script>
    <script src="speech-new.js">        
        // No longer using localStorage for highlight state
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Highlight state will be determined by button state only");
            
            // Make sure highlight mode is disabled by default
            document.body.classList.remove('highlight-mode');
            document.body.classList.remove('highlight-enabled');
            
            // Update button appearance - ensure it's in the correct state
            const highlightButtons = document.querySelectorAll('[id$="-toggle-highlight"]');
            highlightButtons.forEach(button => {
                // Check if the button has the active class
                if (button.classList.contains('active-tool') || button.classList.contains('active')) {
                    // If button is active, enable highlight mode
                    document.body.classList.add('highlight-mode');
                    document.body.style.cursor = 'text';
                    document.addEventListener('mouseup', handleTextSelection);
                    
                    // Add background color to Finnish words if they exist
                    const finnishWords = document.querySelectorAll('.finnish');
                    if (finnishWords.length > 0) {
                        finnishWords.forEach(function(element) {
                            element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                        });
                    }
                    
                    console.log("Highlight mode enabled based on button state");
                } else {
                    // If button is not active, make sure highlight mode is disabled
                    button.classList.remove('active');
                    button.classList.remove('active-tool');
                }
            });
        });        // toggleHighlight function is now defined at the top of the script
        
        // This section has been consolidated with the earlier implementation to avoid duplication

// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    console.log("Mobile menu elements:", mobileMenuToggle, navLinks);
    
    // Add direct click handler to the button
    document.querySelector('.mobile-menu-toggle').addEventListener('click', function(e) {
        console.log("Direct mobile menu toggle clicked");
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('nav-links').classList.toggle('show');
        this.classList.toggle('active');
    });
    
    if (mobileMenuToggle && navLinks) {
        console.log("Setting up mobile menu toggle");
        mobileMenuToggle.addEventListener('click', function(e) {
            console.log("Mobile menu toggle clicked via ID");
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Handle dropdown menus in mobile view
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const dropbtn = dropdown.querySelector('.dropbtn');
        if (dropbtn) {
            dropbtn.addEventListener('click', function(e) {
                // Only in mobile view
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('active');
                }
            });
        }
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (navLinks && navLinks.classList.contains('show')) {
            // Check if click is outside the nav menu
            if (!navLinks.contains(e.target) && e.target !== mobileMenuToggle) {
                navLinks.classList.remove('show');
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                    
                    // Change icon back to bars
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-bars';
                    }
                }
            }
        }
    });
});
</script>
</body>
</html>







