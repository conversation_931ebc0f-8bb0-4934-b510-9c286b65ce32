<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consonant Gradation - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .example-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .example-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .highlight {
            background-color: #ffff99;
            padding: 0 3px;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .example-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .example-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .highlight {
            background-color: #665e00;
            color: #fff;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="gradation-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
                </li>
                <li><a href="../../grammar.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="gradation-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="gradation-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../grammar.html">Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="index.html">Verbs</a>
            <span class="separator">></span>
            <span>Consonant Gradation</span>
        </div>
        
        <section class="grammar-section">
            <h2>Consonant Gradation in Finnish Verbs</h2>
            <p>Consonant gradation (astevaihtelu) is a phonological phenomenon in Finnish where certain consonants alternate between a "strong" and a "weak" grade depending on the syllable structure. This alternation affects both nouns and verbs, but this page focuses specifically on how consonant gradation works in verbs.</p>
            
            <div class="note-box">
                <p><strong>Important:</strong> Consonant gradation is one of the most challenging aspects of Finnish grammar for learners. Understanding the patterns will help you correctly conjugate many Finnish verbs.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Basic Principles of Consonant Gradation</h3>
            <p>Consonant gradation affects the consonants p, t, and k when they appear:</p>
            <ul>
                <li>As single consonants (p, t, k)</li>
                <li>As geminates (double consonants: pp, tt, kk)</li>
                <li>In certain consonant clusters (mp, lt, nk, etc.)</li>
            </ul>
            
            <p>The alternation typically follows these patterns:</p>
            
            <table class="example-table">
                <tr>
                    <th>Strong Grade</th>
                    <th>Weak Grade</th>
                    <th>Type of Change</th>
                </tr>
                <tr>
                    <td>pp</td>
                    <td>p</td>
                    <td>Quantitative</td>
                </tr>
                <tr>
                    <td>tt</td>
                    <td>t</td>
                    <td>Quantitative</td>
                </tr>
                <tr>
                    <td>kk</td>
                    <td>k</td>
                    <td>Quantitative</td>
                </tr>
                <tr>
                    <td>p</td>
                    <td>v</td>
                    <td>Qualitative</td>
                </tr>
                <tr>
                    <td>t</td>
                    <td>d</td>
                    <td>Qualitative</td>
                </tr>
                <tr>
                    <td>k</td>
                    <td>∅ (disappears)</td>
                    <td>Qualitative</td>
                </tr>
                <tr>
                    <td>mp</td>
                    <td>mm</td>
                    <td>Consonant cluster</td>
                </tr>
                <tr>
                    <td>nt</td>
                    <td>nn</td>
                    <td>Consonant cluster</td>
                </tr>
                <tr>
                    <td>nk</td>
                    <td>ng</td>
                    <td>Consonant cluster</td>
                </tr>
                <tr>
                    <td>lt</td>
                    <td>ll</td>
                    <td>Consonant cluster</td>
                </tr>
                <tr>
                    <td>rt</td>
                    <td>rr</td>
                    <td>Consonant cluster</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> The symbol ∅ indicates that the consonant disappears completely in the weak grade.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>When Does Consonant Gradation Occur in Verbs?</h3>
            <p>In verbs, consonant gradation typically occurs when:</p>
            <ul>
                <li>The syllable containing the affected consonant closes (becomes a closed syllable) due to conjugation</li>
                <li>Or when a closed syllable opens (becomes an open syllable)</li>
            </ul>
            
            <p>The basic rule is:</p>
            <ul>
                <li>Strong grade appears in open syllables (syllables ending in a vowel)</li>
                <li>Weak grade appears in closed syllables (syllables ending in a consonant)</li>
            </ul>
            
            <p>However, there are many exceptions and special cases, especially in verb conjugation.</p>
        </section>
        
        <section class="grammar-section">
            <h3>Consonant Gradation Patterns in Different Verb Types</h3>
            
            <h4>Type 1 Verbs</h4>
            <p>Type 1 verbs (ending in -a/-ä) often show consonant gradation in their conjugation.</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive (Strong)</th>
                    <th>1st Person Singular (Weak)</th>
                    <th>3rd Person Singular (Strong)</th>
                    <th>Pattern</th>
                </tr>
                <tr>
                    <td>pu<span class="highlight">k</span>ea (to dress)</td>
                    <td>pu<span class="highlight">e</span>n (I dress)</td>
                    <td>pu<span class="highlight">k</span>ee (he/she dresses)</td>
                    <td>k → ∅</td>
                </tr>
                <tr>
                    <td>ta<span class="highlight">p</span>aa (to meet)</td>
                    <td>ta<span class="highlight">p</span>aan (I meet)</td>
                    <td>ta<span class="highlight">p</span>aa (he/she meets)</td>
                    <td>No gradation</td>
                </tr>
                <tr>
                    <td>o<span class="highlight">tt</span>aa (to take)</td>
                    <td>o<span class="highlight">t</span>an (I take)</td>
                    <td>o<span class="highlight">tt</span>aa (he/she takes)</td>
                    <td>tt → t</td>
                </tr>
                <tr>
                    <td>au<span class="highlight">tt</span>aa (to help)</td>
                    <td>au<span class="highlight">t</span>an (I help)</td>
                    <td>au<span class="highlight">tt</span>aa (he/she helps)</td>
                    <td>tt → t</td>
                </tr>
                <tr>
                    <td>lu<span class="highlight">k</span>ea (to read)</td>
                    <td>lu<span class="highlight">e</span>n (I read)</td>
                    <td>lu<span class="highlight">k</span>ee (he/she reads)</td>
                    <td>k → ∅</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>Infinitive: <strong>lukea</strong> (to read)</p>
                <p>minä <strong>luen</strong> (I read) - weak grade</p>
                <p>sinä <strong>luet</strong> (you read) - weak grade</p>
                <p>hän <strong>lukee</strong> (he/she reads) - strong grade</p>
                <p>me <strong>luemme</strong> (we read) - weak grade</p>
                <p>te <strong>luette</strong> (you read) - weak grade</p>
                <p>he <strong>lukevat</strong> (they read) - strong grade</p>
            </div>
            
            <h4>Type 2 Verbs</h4>
            <p>Type 2 verbs (ending in -da/-dä) may also show consonant gradation.</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>1st Person Singular</th>
                    <th>3rd Person Singular</th>
                    <th>Pattern</th>
                </tr>
                <tr>
                    <td>teh<span class="highlight">d</span>ä (to do)</td>
                    <td>tee<span class="highlight">n</span> (I do)</td>
                    <td>te<span class="highlight">k</span>ee (he/she does)</td>
                    <td>Special case</td>
                </tr>
                <tr>
                    <td>näh<span class="highlight">d</span>ä (to see)</td>
                    <td>nä<span class="highlight">e</span>n (I see)</td>
                    <td>nä<span class="highlight">k</span>ee (he/she sees)</td>
                    <td>Special case</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Some Type 2 verbs like "tehdä" and "nähdä" have irregular conjugation patterns that involve consonant changes.</p>
            </div>
            
            <h4>Type 3 Verbs</h4>
            <p>Type 3 verbs (ending in -la/-lä, -na/-nä, -ra/-rä, -sta/-stä) often show consonant gradation.</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive (Strong)</th>
                    <th>1st Person Singular (Weak)</th>
                    <th>3rd Person Singular (Weak)</th>
                    <th>Pattern</th>
                </tr>
                <tr>
                    <td>men<span class="highlight">n</span>ä (to go)</td>
                    <td>me<span class="highlight">n</span>en (I go)</td>
                    <td>me<span class="highlight">n</span>ee (he/she goes)</td>
                    <td>nn → n</td>
                </tr>
                <tr>
                    <td>tul<span class="highlight">l</span>a (to come)</td>
                    <td>tu<span class="highlight">l</span>en (I come)</td>
                    <td>tu<span class="highlight">l</span>ee (he/she comes)</td>
                    <td>ll → l</td>
                </tr>
                <tr>
                    <td>pur<span class="highlight">r</span>a (to bite)</td>
                    <td>pu<span class="highlight">r</span>en (I bite)</td>
                    <td>pu<span class="highlight">r</span>ee (he/she bites)</td>
                    <td>rr → r</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>Infinitive: <strong>tulla</strong> (to come)</p>
                <p>minä <strong>tulen</strong> (I come) - weak grade</p>
                <p>sinä <strong>tulet</strong> (you come) - weak grade</p>
                <p>hän <strong>tulee</strong> (he/she comes) - weak grade</p>
                <p>me <strong>tulemme</strong> (we come) - weak grade</p>
                <p>te <strong>tulette</strong> (you come) - weak grade</p>
                <p>he <strong>tulevat</strong> (they come) - weak grade</p>
            </div>
            
            <h4>Type 4 Verbs</h4>
            <p>Type 4 verbs (ending in -ta/-tä) often show consonant gradation in their conjugation.</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>1st Person Singular</th>
                    <th>3rd Person Singular</th>
                    <th>Pattern</th>
                </tr>
                <tr>
                    <td>tava<span class="highlight">t</span>a (to meet)</td>
                    <td>tapaa<span class="highlight">n</span> (I meet)</td>
                    <td>tapaa (he/she meets)</td>
                    <td>Special conjugation</td>
                </tr>
                <tr>
                    <td>hala<span class="highlight">t</span>a (to hug)</td>
                    <td>halaa<span class="highlight">n</span> (I hug)</td>
                    <td>halaa (he/she hugs)</td>
                    <td>Special conjugation</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Consonant Gradation in Different Verb Forms</h3>
            
            <h4>Present Tense</h4>
            <p>In the present tense, consonant gradation typically follows these patterns:</p>
            
            <table class="example-table">
                <tr>
                    <th>Person</th>
                    <th>Typical Grade</th>
                    <th>Example: lukea (to read)</th>
                </tr>
                <tr>
                    <td>minä (I)</td>
                    <td>Weak</td>
                    <td>luen</td>
                </tr>
                <tr>
                    <td>sinä (you)</td>
                    <td>Weak</td>
                    <td>luet</td>
                </tr>
                <tr>
                    <td>hän (he/she)</td>
                    <td>Strong (Type 1), Weak (Type 3)</td>
                    <td>lukee</td>
                </tr>
                <tr>
                    <td>me (we)</td>
                    <td>Weak</td>
                    <td>luemme</td>
                </tr>
                <tr>
                    <td>te (you pl.)</td>
                    <td>Weak</td>
                    <td>luette</td>
                </tr>
                <tr>
                    <td>he (they)</td>
                    <td>Strong (Type 1), Weak (Type 3)</td>
                    <td>lukevat</td>
                </tr>
            </table>
            
            <h4>Imperfect (Past Tense)</h4>
            <p>In the imperfect tense, consonant gradation often follows different patterns:</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>Present (1st Person)</th>
                    <th>Imperfect (1st Person)</th>
                    <th>Pattern</th>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luen (I read)</td>
                    <td>luin (I read)</td>
                    <td>Weak → Weak</td>
                </tr>
                <tr>
                    <td>ottaa (to take)</td>
                    <td>otan (I take)</td>
                    <td>otin (I took)</td>
                    <td>Weak → Weak</td>
                </tr>
                <tr>
                    <td>hakea (to fetch)</td>
                    <td>haen (I fetch)</td>
                    <td>hain (I fetched)</td>
                    <td>Weak → Weak</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p>Infinitive: <strong>lukea</strong> (to read)</p>
                <p>minä <strong>luin</strong> (I read) - weak grade</p>
                <p>sinä <strong>luit</strong> (you read) - weak grade</p>
                <p>hän <strong>luki</strong> (he/she read) - strong grade</p>
                <p>me <strong>luimme</strong> (we read) - weak grade</p>
                <p>te <strong>luitte</strong> (you read) - weak grade</p>
                <p>he <strong>lukivat</strong> (they read) - strong grade</p>
            </div>
            
            <h4>Conditional</h4>
            <p>In the conditional mood, consonant gradation typically follows the same pattern as in the present tense:</p>
            
            <div class="example-box">
                <p>Infinitive: <strong>lukea</strong> (to read)</p>
                <p>minä <strong>lukisin</strong> (I would read) - strong grade</p>
                <p>sinä <strong>lukisit</strong> (you would read) - strong grade</p>
                <p>hän <strong>lukisi</strong> (he/she would read) - strong grade</p>
                <p>me <strong>lukisimme</strong> (we would read) - strong grade</p>
                <p>te <strong>lukisitte</strong> (you would read) - strong grade</p>
                <p>he <strong>lukisivat</strong> (they would read) - strong grade</p>
            </div>
            
            <h4>Imperative</h4>
            <p>In the imperative mood, consonant gradation can vary:</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>2nd Person Singular Imperative</th>
                    <th>2nd Person Plural Imperative</th>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>lue! (read!)</td>
                    <td>lukekaa! (read!)</td>
                </tr>
                <tr>
                    <td>ottaa (to take)</td>
                    <td>ota! (take!)</td>
                    <td>ottakaa! (take!)</td>
                </tr>
                <tr>
                    <td>hakea (to fetch)</td>
                    <td>hae! (fetch!)</td>
                    <td>hakekaa! (fetch!)</td>
                </tr>
            </table>
            
            <h4>Passive Forms</h4>
            <p>In passive forms, consonant gradation often occurs:</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>Present Passive</th>
                    <th>Imperfect Passive</th>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luetaan (it is read)</td>
                    <td>luettiin (it was read)</td>
                </tr>
                <tr>
                    <td>ottaa (to take)</td>
                    <td>otetaan (it is taken)</td>
                    <td>otettiin (it was taken)</td>
                </tr>
                <tr>
                    <td>hakea (to fetch)</td>
                    <td>haetaan (it is fetched)</td>
                    <td>haettiin (it was fetched)</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Common Patterns and Examples</h3>
            
            <h4>kk → k</h4>
            <div class="example-box">
                <p>Infinitive: <strong>rakastaa</strong> (to love)</p>
                <p>minä <strong>rakastan</strong> (I love)</p>
                <p>hän <strong>rakastaa</strong> (he/she loves)</p>
            </div>
            
            <h4>pp → p</h4>
            <div class="example-box">
                <p>Infinitive: <strong>tappaa</strong> (to kill)</p>
                <p>minä <strong>tapan</strong> (I kill)</p>
                <p>hän <strong>tappaa</strong> (he/she kills)</p>
            </div>
            
            <h4>tt → t</h4>
            <div class="example-box">
                <p>Infinitive: <strong>ottaa</strong> (to take)</p>
                <p>minä <strong>otan</strong> (I take)</p>
                <p>hän <strong>ottaa</strong> (he/she takes)</p>
            </div>
            
            <h4>k → ∅ (disappears)</h4>
            <div class="example-box">
                <p>Infinitive: <strong>lukea</strong> (to read)</p>
                <p>minä <strong>luen</strong> (I read)</p>
                <p>hän <strong>lukee</strong> (he/she reads)</p>
            </div>
            
            <h4>p → v</h4>
            <div class="example-box">
                <p>Infinitive: <strong>leipoa</strong> (to bake)</p>
                <p>minä <strong>leivon</strong> (I bake)</p>
                <p>hän <strong>leipoo</strong> (he/she bakes)</p>
            </div>
            
            <h4>t → d</h4>
            <div class="example-box">
                <p>Infinitive: <strong>pitää</strong> (to keep, to like)</p>
                <p>minä <strong>pidän</strong> (I like)</p>
                <p>hän <strong>pitää</strong> (he/she likes)</p>
            </div>
            
            <h4>nk → ng</h4>
            <div class="example-box">
                <p>Infinitive: <strong>tunkea</strong> (to push, to crowd)</p>
                <p>minä <strong>tungen</strong> (I push)</p>
                <p>hän <strong>tunkee</strong> (he/she pushes)</p>
            </div>
            
            <h4>mp → mm</h4>
            <div class="example-box">
                <p>Infinitive: <strong>ampua</strong> (to shoot)</p>
                <p>minä <strong>ammun</strong> (I shoot)</p>
                <p>hän <strong>ampuu</strong> (he/she shoots)</p>
            </div>
            
            <h4>lt → ll</h4>
            <div class="example-box">
                <p>Infinitive: <strong>kieltää</strong> (to deny, to forbid)</p>
                <p>minä <strong>kiellän</strong> (I deny)</p>
                <p>hän <strong>kieltää</strong> (he/she denies)</p>
            </div>
            
            <h4>rt → rr</h4>
            <div class="example-box">
                <p>Infinitive: <strong>kertoa</strong> (to tell)</p>
                <p>minä <strong>kerron</strong> (I tell)</p>
                <p>hän <strong>kertoo</strong> (he/she tells)</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Verbs Without Consonant Gradation</h3>
            <p>Not all Finnish verbs undergo consonant gradation. Here are some common verbs that don't have gradation:</p>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>1st Person Singular</th>
                    <th>3rd Person Singular</th>
                </tr>
                <tr>
                    <td>puhua (to speak)</td>
                    <td>puhun (I speak)</td>
                    <td>puhuu (he/she speaks)</td>
                </tr>
                <tr>
                    <td>etsiä (to search)</td>
                    <td>etsin (I search)</td>
                    <td>etsii (he/she searches)</td>
                </tr>
                <tr>
                    <td>ostaa (to buy)</td>
                    <td>ostan (I buy)</td>
                    <td>ostaa (he/she buys)</td>
                </tr>
                <tr>
                    <td>sanoa (to say)</td>
                    <td>sanon (I say)</td>
                    <td>sanoo (he/she says)</td>
                </tr>
                <tr>
                    <td>kysyä (to ask)</td>
                    <td>kysyn (I ask)</td>
                    <td>kysyy (he/she asks)</td>
                </tr>
            </table>
        </section>
        
        <section class="grammar-section">
            <h3>Tips for Learning Consonant Gradation</h3>
            <ol>
                <li><strong>Learn the patterns:</strong> Familiarize yourself with the common gradation patterns (kk → k, p → v, etc.).</li>
                <li><strong>Practice with common verbs:</strong> Start with frequently used verbs that show gradation.</li>
                <li><strong>Pay attention to syllable structure:</strong> Remember that gradation is often related to whether a syllable is open or closed.</li>
                <li><strong>Use a good dictionary:</strong> Many Finnish dictionaries indicate which verbs undergo gradation and how.</li>
                <li><strong>Listen carefully:</strong> Pay attention to how native speakers pronounce different forms of the same verb.</li>
            </ol>
            
            <div class="note-box">
                <p><strong>Remember:</strong> Consonant gradation is a regular feature of Finnish, but there are many exceptions and special cases. With practice, you'll develop an intuitive feel for when and how gradation occurs.</p>
            </div>
        </section>
        
        <section class="grammar-section">
            <h3>Practice Examples</h3>
            
            <table class="example-table">
                <tr>
                    <th>Infinitive</th>
                    <th>Present (minä)</th>
                    <th>Present (hän)</th>
                    <th>Imperfect (minä)</th>
                    <th>Gradation Pattern</th>
                </tr>
                <tr>
                    <td>lukea (to read)</td>
                    <td>luen</td>
                    <td>lukee</td>
                    <td>luin</td>
                    <td>k → ∅</td>
                </tr>
                <tr>
                    <td>ottaa (to take)</td>
                    <td>otan</td>
                    <td>ottaa</td>
                    <td>otin</td>
                    <td>tt → t</td>
                </tr>
                <tr>
                    <td>hakea (to fetch)</td>
                    <td>haen</td>
                    <td>hakee</td>
                    <td>hain</td>
                    <td>k → ∅</td>
                </tr>
                <tr>
                    <td>tietää (to know)</td>
                    <td>tiedän</td>
                    <td>tietää</td>
                    <td>tiesin</td>
                    <td>t → d</td>
                </tr>
                <tr>
                    <td>antaa (to give)</td>
                    <td>annan</td>
                    <td>antaa</td>
                    <td>annoin</td>
                    <td>nt → nn</td>
                </tr>
                <tr>
                    <td>kertoa (to tell)</td>
                    <td>kerron</td>
                    <td>kertoo</td>
                    <td>kerroin</td>
                    <td>rt → rr</td>
                </tr>
                <tr>
                    <td>tulla (to come)</td>
                    <td>tulen</td>
                    <td>tulee</td>
                    <td>tulin</td>
                    <td>ll → l</td>
                </tr>
                <tr>
                    <td>mennä (to go)</td>
                    <td>menen</td>
                    <td>menee</td>
                    <td>menin</td>
                    <td>nn → n</td>
                </tr>
                <tr>
                    <td>nähdä (to see)</td>
                    <td>näen</td>
                    <td>näkee</td>
                    <td>näin</td>
                    <td>Special case</td>
                </tr>
                <tr>
                    <td>tehdä (to do)</td>
                    <td>teen</td>
                    <td>tekee</td>
                    <td>tein</td>
                    <td>Special case</td>
                </tr>
            </table>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>









