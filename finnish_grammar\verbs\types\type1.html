﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finnish Verb Type 1 - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Verbs</a>
            <span class="separator">></span>
            <span>Type 1 Verbs</span>
        </div>
        
        <section class="grammar-section">
            <h2>Finnish Verb Type 1: -a/-ä</h2>
            <p>Type 1 verbs are the most common verb type in Finnish. They end in -a or -ä (depending on vowel harmony) and have a consonant before the ending. Examples include puhua (to speak), sanoa (to say), and ymmärtää (to understand).</p>
        </section>

        <section class="grammar-category">
            <h3>CHARACTERISTICS OF TYPE 1 VERBS</h3>
            
            <div class="grammar-content">
                <p>Type 1 verbs have the following characteristics:</p>
                <ul>
                    <li>They end in -a or -ä</li>
                    <li>There is a consonant before the ending</li>
                    <li>The infinitive stem is formed by removing the final -a/-ä</li>
                    <li>Many type 1 verbs undergo consonant gradation</li>
                </ul>
                
                <p>Common examples of type 1 verbs:</p>
                <ul>
                    <li>puhua (to speak)</li>
                    <li>sanoa (to say)</li>
                    <li>ymmärtää (to understand)</li>
                    <li>oppia (to learn)</li>
                    <li>lukea (to read)</li>
                    <li>kirjoittaa (to write)</li>
                    <li>ostaa (to buy)</li>
                    <li>antaa (to give)</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PRESENT TENSE CONJUGATION</h3>
            
            <div class="grammar-content">
                <p>To form the present tense of type 1 verbs, remove the final -a/-ä and add the personal endings:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Ending</th>
                        <th>puhua (to speak)</th>
                        <th>ymmärtää (to understand)</th>
                    </tr>
                    <tr>
                        <td>minä (I)</td>
                        <td>-n</td>
                        <td>puhun</td>
                        <td>ymmärrän</td>
                    </tr>
                    <tr>
                        <td>sinä (you)</td>
                        <td>-t</td>
                        <td>puhut</td>
                        <td>ymmärrät</td>
                    </tr>
                    <tr>
                        <td>hän (he/she)</td>
                        <td>-</td>
                        <td>puhuu</td>
                        <td>ymmärtää</td>
                    </tr>
                    <tr>
                        <td>me (we)</td>
                        <td>-mme</td>
                        <td>puhumme</td>
                        <td>ymmärrämme</td>
                    </tr>
                    <tr>
                        <td>te (you - plural)</td>
                        <td>-tte</td>
                        <td>puhutte</td>
                        <td>ymmärrätte</td>
                    </tr>
                    <tr>
                        <td>he (they)</td>
                        <td>-vat/-vät</td>
                        <td>puhuvat</td>
                        <td>ymmärtävät</td>
                    </tr>
                </table>
                
                <p>Note that in the third person singular (hän), the final vowel is lengthened (a → aa, ä → ää).</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä puhun suomea.</span> <span class="english">I speak Finnish.</span></p>
                    <p><span class="finnish">Hän ymmärtää hyvin.</span> <span class="english">He/she understands well.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION IN TYPE 1 VERBS</h3>
            
            <div class="grammar-content">
                <p>Many type 1 verbs undergo consonant gradation, where certain consonants alternate between strong and weak forms. The strong form appears in the infinitive, and the weak form appears in certain conjugated forms.</p>
                
                <p>Common consonant gradation patterns in type 1 verbs:</p>
                <table class="grammar-table">
                    <tr>
                        <th>Strong → Weak</th>
                        <th>Example</th>
                        <th>Infinitive</th>
                        <th>minä (I)</th>
                    </tr>
                    <tr>
                        <td>kk → k</td>
                        <td>rakastaa (to love)</td>
                        <td>rakastaa</td>
                        <td>rakastan</td>
                    </tr>
                    <tr>
                        <td>pp → p</td>
                        <td>tappaa (to kill)</td>
                        <td>tappaa</td>
                        <td>tapan</td>
                    </tr>
                    <tr>
                        <td>tt → t</td>
                        <td>odottaa (to wait)</td>
                        <td>odottaa</td>
                        <td>odotan</td>
                    </tr>
                    <tr>
                        <td>k → ∅ (disappears)</td>
                        <td>lukea (to read)</td>
                        <td>lukea</td>
                        <td>luen</td>
                    </tr>
                    <tr>
                        <td>p → v</td>
                        <td>tietää (to know)</td>
                        <td>tietää</td>
                        <td>tiedän</td>
                    </tr>
                    <tr>
                        <td>t → d</td>
                        <td>pitää (to keep, like)</td>
                        <td>pitää</td>
                        <td>pidän</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen kirjaa.</span> <span class="english">I read a book.</span></p>
                    <p><span class="finnish">Hän pitää sinusta.</span> <span class="english">He/she likes you.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>NEGATIVE PRESENT TENSE</h3>
            
            <div class="grammar-content">
                <p>To form the negative present tense, use the negative verb "ei" conjugated for person, followed by the verb stem (without the final -a/-ä):</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Negative Verb</th>
                        <th>puhua (to speak)</th>
                        <th>ymmärtää (to understand)</th>
                    </tr>
                    <tr>
                        <td>minä (I)</td>
                        <td>en</td>
                        <td>en puhu</td>
                        <td>en ymmärrä</td>
                    </tr>
                    <tr>
                        <td>sinä (you)</td>
                        <td>et</td>
                        <td>et puhu</td>
                        <td>et ymmärrä</td>
                    </tr>
                    <tr>
                        <td>hän (he/she)</td>
                        <td>ei</td>
                        <td>ei puhu</td>
                        <td>ei ymmärrä</td>
                    </tr>
                    <tr>
                        <td>me (we)</td>
                        <td>emme</td>
                        <td>emme puhu</td>
                        <td>emme ymmärrä</td>
                    </tr>
                    <tr>
                        <td>te (you - plural)</td>
                        <td>ette</td>
                        <td>ette puhu</td>
                        <td>ette ymmärrä</td>
                    </tr>
                    <tr>
                        <td>he (they)</td>
                        <td>eivät</td>
                        <td>eivät puhu</td>
                        <td>eivät ymmärrä</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä en puhu venäjää.</span> <span class="english">I don't speak Russian.</span></p>
                    <p><span class="finnish">He eivät ymmärrä suomea.</span> <span class="english">They don't understand Finnish.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>PAST TENSE (IMPERFECT) CONJUGATION</h3>
            
            <div class="grammar-content">
                <p>To form the past tense (imperfect) of type 1 verbs, remove the final -a/-ä, add the past tense marker -i-, and then add the personal endings:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Ending</th>
                        <th>puhua (to speak)</th>
                        <th>ymmärtää (to understand)</th>
                    </tr>
                    <tr>
                        <td>minä (I)</td>
                        <td>-in</td>
                        <td>puhuin</td>
                        <td>ymmärsin</td>
                    </tr>
                    <tr>
                        <td>sinä (you)</td>
                        <td>-it</td>
                        <td>puhuit</td>
                        <td>ymmärsit</td>
                    </tr>
                    <tr>
                        <td>hän (he/she)</td>
                        <td>-i</td>
                        <td>puhui</td>
                        <td>ymmärsi</td>
                    </tr>
                    <tr>
                        <td>me (we)</td>
                        <td>-imme</td>
                        <td>puhuimme</td>
                        <td>ymmärsimme</td>
                    </tr>
                    <tr>
                        <td>te (you - plural)</td>
                        <td>-itte</td>
                        <td>puhuitte</td>
                        <td>ymmärsitte</td>
                    </tr>
                    <tr>
                        <td>he (they)</td>
                        <td>-ivat/-ivät</td>
                        <td>puhuivat</td>
                        <td>ymmärsivät</td>
                    </tr>
                </table>
                
                <p>Note that some verbs undergo stem changes in the past tense. For example, verbs ending in -taa/-tää often change t → s before the past tense marker -i-.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä puhuin eilen suomea.</span> <span class="english">I spoke Finnish yesterday.</span></p>
                    <p><span class="finnish">Hän ymmärsi kaiken.</span> <span class="english">He/she understood everything.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXCEPTIONS</h3>
            
            <div class="grammar-content">
                <p>Some type 1 verbs have special forms or irregularities:</p>
                
                <h4>Verbs with stem-final -a/-ä</h4>
                <p>Verbs like antaa (to give) and ottaa (to take) have a stem that ends in the same vowel as the infinitive ending. In the third person singular present tense, these verbs have a special form:</p>
                <ul>
                    <li>antaa → antaa (not *antaaa)</li>
                    <li>ottaa → ottaa (not *ottaaa)</li>
                </ul>
                
                <h4>Verbs with stem changes in the past tense</h4>
                <p>Some verbs have special stem changes in the past tense:</p>
                <ul>
                    <li>antaa (to give): annoin, annoit, antoi... (not *antin, *antit, *anti...)</li>
                    <li>ottaa (to take): otin, otit, otti... (not *ottin, *ottit, *otti...)</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän antaa sinulle lahjan.</span> <span class="english">He/she gives you a gift.</span></p>
                    <p><span class="finnish">Minä annoin hänelle kirjan.</span> <span class="english">I gave him/her a book.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>












