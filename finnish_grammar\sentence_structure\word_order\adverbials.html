﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adverbials in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                                        <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                                                <a href="../../../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Adverbials</span>
        </div>
        
        <section class="grammar-section">
            <h2>Adverbials in Finnish</h2>
            <p>Adverbials (adverbiaalit) are words, phrases, or clauses that modify verbs, adjectives, or entire sentences by providing additional information about time, place, manner, reason, or other circumstances. This page explains how adverbials are used and positioned in Finnish sentences.</p>
        </section>

        <section class="grammar-category">
            <h3>TYPES OF ADVERBIALS</h3>
            
            <div class="grammar-content">
                <p>Finnish uses several types of adverbials to provide different kinds of information:</p>
                
                <h4>1. Adverbials of place (paikan adverbiaali)</h4>
                <p>These indicate where an action takes place and typically use locative cases:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Asun <strong>Helsingissä</strong>.</span> <span class="english">I live <strong>in Helsinki</strong>.</span></p>
                    <p><span class="finnish">Menen <strong>kauppaan</strong>.</span> <span class="english">I go <strong>to the store</strong>.</span></p>
                    <p><span class="finnish">Tulen <strong>koulusta</strong>.</span> <span class="english">I come <strong>from school</strong>.</span></p>
                </div>
                
                <h4>2. Adverbials of time (ajan adverbiaali)</h4>
                <p>These indicate when an action takes place:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tulen <strong>huomenna</strong>.</span> <span class="english">I will come <strong>tomorrow</strong>.</span></p>
                    <p><span class="finnish"><strong>Viime viikolla</strong> kävin Turussa.</span> <span class="english"><strong>Last week</strong> I visited Turku.</span></p>
                    <p><span class="finnish">Odotin <strong>kaksi tuntia</strong>.</span> <span class="english">I waited <strong>for two hours</strong>.</span></p>
                </div>
                
                <h4>3. Adverbials of manner (tavan adverbiaali)</h4>
                <p>These describe how an action is performed:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän puhuu <strong>nopeasti</strong>.</span> <span class="english">He/she speaks <strong>quickly</strong>.</span></p>
                    <p><span class="finnish">Luen <strong>ääneen</strong>.</span> <span class="english">I read <strong>aloud</strong>.</span></p>
                    <p><span class="finnish">Hän ajaa <strong>varovasti</strong>.</span> <span class="english">He/she drives <strong>carefully</strong>.</span></p>
                </div>
                
                <h4>4. Adverbials of reason (syyn adverbiaali)</h4>
                <p>These explain why an action happens:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">En tule <strong>sairauden takia</strong>.</span> <span class="english">I won't come <strong>because of illness</strong>.</span></p>
                    <p><span class="finnish">Hän itki <strong>surusta</strong>.</span> <span class="english">He/she cried <strong>from sadness</strong>.</span></p>
                </div>
                
                <h4>5. Adverbials of purpose (tarkoituksen adverbiaali)</h4>
                <p>These indicate the purpose or goal of an action:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Opiskelen <strong>tutkintoa varten</strong>.</span> <span class="english">I study <strong>for a degree</strong>.</span></p>
                    <p><span class="finnish">Säästän rahaa <strong>matkaa varten</strong>.</span> <span class="english">I save money <strong>for a trip</strong>.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>FORMING ADVERBIALS</h3>
            
            <div class="grammar-content">
                <p>Finnish adverbials can be formed in several ways:</p>
                
                <h4>1. Using case endings</h4>
                <p>Many adverbials are formed by adding case endings to nouns:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Typical Use</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>Inessive (-ssa/-ssä)</td>
                        <td>Location in</td>
                        <td><span class="finnish">talossa</span> (in the house)</td>
                    </tr>
                    <tr>
                        <td>Elative (-sta/-stä)</td>
                        <td>Movement from inside</td>
                        <td><span class="finnish">talosta</span> (from the house)</td>
                    </tr>
                    <tr>
                        <td>Illative (-Vn, -hVn, -seen)</td>
                        <td>Movement into</td>
                        <td><span class="finnish">taloon</span> (into the house)</td>
                    </tr>
                    <tr>
                        <td>Adessive (-lla/-llä)</td>
                        <td>Location on/at</td>
                        <td><span class="finnish">pöydällä</span> (on the table)</td>
                    </tr>
                    <tr>
                        <td>Ablative (-lta/-ltä)</td>
                        <td>Movement from on/at</td>
                        <td><span class="finnish">pöydältä</span> (from the table)</td>
                    </tr>
                    <tr>
                        <td>Allative (-lle)</td>
                        <td>Movement onto/to</td>
                        <td><span class="finnish">pöydälle</span> (onto the table)</td>
                    </tr>
                </table>
                
                <h4>2. Using adverbs</h4>
                <p>Finnish has many dedicated adverbs:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän puhuu <strong>hyvin</strong>.</span> <span class="english">He/she speaks <strong>well</strong>.</span></p>
                    <p><span class="finnish">Tulen <strong>pian</strong>.</span> <span class="english">I'll come <strong>soon</strong>.</span></p>
                    <p><span class="finnish">Asun <strong>täällä</strong>.</span> <span class="english">I live <strong>here</strong>.</span></p>
                </div>
                
                <h4>3. Using postpositions and prepositions</h4>
                <p>Finnish uses mostly postpositions (words that come after the noun) and some prepositions:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Pöydän <strong>alla</strong>.</span> <span class="english"><strong>Under</strong> the table.</span></p>
                    <p><span class="finnish">Talon <strong>edessä</strong>.</span> <span class="english"><strong>In front of</strong> the house.</span></p>
                    <p><span class="finnish"><strong>Ilman</strong> sinua.</span> <span class="english"><strong>Without</strong> you.</span></p>
                </div>
                
                <h4>4. Using adverbial clauses</h4>
                <p>Entire clauses can function as adverbials:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tulen, <strong>kun olen valmis</strong>.</span> <span class="english">I'll come <strong>when I'm ready</strong>.</span></p>
                    <p><span class="finnish">Lähden, <strong>jos sataa</strong>.</span> <span class="english">I'll leave <strong>if it rains</strong>.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>POSITION OF ADVERBIALS IN SENTENCES</h3>
            
            <div class="grammar-content">
                <p>The position of adverbials in Finnish sentences is relatively flexible, but there are some general patterns:</p>
                
                <h4>1. Time adverbials</h4>
                <p>Time adverbials often appear at the beginning of the sentence or after the verb:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Huomenna</strong> menen töihin.</span> <span class="english"><strong>Tomorrow</strong> I will go to work.</span></p>
                    <p><span class="finnish">Menen töihin <strong>huomenna</strong>.</span> <span class="english">I will go to work <strong>tomorrow</strong>.</span></p>
                </div>
                
                <h4>2. Place adverbials</h4>
                <p>Place adverbials typically follow the verb:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Asun <strong>Helsingissä</strong>.</span> <span class="english">I live <strong>in Helsinki</strong>.</span></p>
                    <p><span class="finnish">Hän kävelee <strong>puistossa</strong>.</span> <span class="english">He/she walks <strong>in the park</strong>.</span></p>
                </div>
                
                <h4>3. Manner adverbials</h4>
                <p>Manner adverbials usually come right before or after the verb:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Hän <strong>nopeasti</strong> vastasi.</span> <span class="english">He/she <strong>quickly</strong> answered.</span></p>
                    <p><span class="finnish">Hän vastasi <strong>nopeasti</strong>.</span> <span class="english">He/she answered <strong>quickly</strong>.</span></p>
                </div>
                
                <h4>4. Multiple adverbials</h4>
                <p>When multiple adverbials are present, they typically follow this order:</p>
                <ol>
                    <li>Time</li>
                    <li>Place</li>
                    <li>Manner</li>
                    <li>Other adverbials</li>
                </ol>
                
                <div class="grammar-example">
                    <p><span class="finnish">Menen <strong>huomenna</strong> <strong>kauppaan</strong> <strong>nopeasti</strong>.</span> <span class="english">I will go <strong>tomorrow</strong> <strong>to the store</strong> <strong>quickly</strong>.</span></p>
                </div>
                
                <p>However, for emphasis, this order can be changed:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Nopeasti</strong> menen <strong>huomenna</strong> <strong>kauppaan</strong>.</span> <span class="english"><strong>Quickly</strong> I will go <strong>tomorrow</strong> <strong>to the store</strong>. (emphasis on "quickly")</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ADVERBIALS AND EMPHASIS</h3>
            
            <div class="grammar-content">
                <p>The position of adverbials can significantly affect the emphasis and meaning of a sentence:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish"><strong>Helsingissä</strong> minä asun.</span> <span class="english">It's <strong>in Helsinki</strong> that I live. (emphasis on location)</span></p>
                    <p><span class="finnish"><strong>Huomenna</strong> tulen, en tänään.</span> <span class="english">I'll come <strong>tomorrow</strong>, not today. (emphasis on time)</span></p>
                    <p><span class="finnish"><strong>Nopeasti</strong> hän juoksi.</span> <span class="english">It was <strong>quickly</strong> that he/she ran. (emphasis on manner)</span></p>
                </div>
                
                <p>In Finnish, elements at the beginning or end of the sentence receive more emphasis, with the end position typically carrying the strongest emphasis.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON ADVERBIAL EXPRESSIONS</h3>
            
            <div class="grammar-content">
                <h4>1. Time expressions</h4>
                <ul>
                    <li><span class="finnish">tänään</span> - today</li>
                    <li><span class="finnish">huomenna</span> - tomorrow</li>
                    <li><span class="finnish">eilen</span> - yesterday</li>
                    <li><span class="finnish">nyt</span> - now</li>
                    <li><span class="finnish">pian</span> - soon</li>
                    <li><span class="finnish">myöhemmin</span> - later</li>
                    <li><span class="finnish">aina</span> - always</li>
                    <li><span class="finnish">usein</span> - often</li>
                    <li><span class="finnish">joskus</span> - sometimes</li>
                    <li><span class="finnish">harvoin</span> - rarely</li>
                </ul>
                
                <h4>2. Place expressions</h4>
                <ul>
                    <li><span class="finnish">täällä</span> - here</li>
                    <li><span class="finnish">siellä</span> - there</li>
                    <li><span class="finnish">tuolla</span> - over there</li>
                    <li><span class="finnish">ylhäällä</span> - up, upstairs</li>
                    <li><span class="finnish">alhaalla</span> - down, downstairs</li>
                    <li><span class="finnish">sisällä</span> - inside</li>
                    <li><span class="finnish">ulkona</span> - outside</li>
                    <li><span class="finnish">kotona</span> - at home</li>
                </ul>
                
                <h4>3. Manner expressions</h4>
                <ul>
                    <li><span class="finnish">hyvin</span> - well</li>
                    <li><span class="finnish">huonosti</span> - badly</li>
                    <li><span class="finnish">nopeasti</span> - quickly</li>
                    <li><span class="finnish">hitaasti</span> - slowly</li>
                    <li><span class="finnish">kovaa</span> - hard, loudly</li>
                    <li><span class="finnish">hiljaa</span> - quietly</li>
                    <li><span class="finnish">yhdessä</span> - together</li>
                    <li><span class="finnish">yksin</span> - alone</li>
                </ul>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggleButtons = document.querySelectorAll('.theme-toggle-button');
    themeToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('darkMode', 'enabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-sun';
                    }
                });
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('darkMode', 'disabled');
                themeToggleButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-moon';
                    }
                });
            }
        });
    });
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>















